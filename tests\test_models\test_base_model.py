"""
Test suite for BaseModel functionality

This module tests:
- BaseModel class attributes and initialization
- Common fields and custom fields
- CRUD operations (create, write, unlink)
- Instance creation and data conversion
"""
import pytest
from unittest.mock import AsyncMock, patch

from erp.models import BaseModel
from erp.fields import (
    <PERSON><PERSON>, <PERSON>, Integer, <PERSON>an,
    Datetime
)


class TestBaseModel:
    """Test BaseModel functionality"""

    def test_base_model_class_attributes(self):
        """Test BaseModel class attributes"""
        class TestModel(BaseModel):
            _name = 'test.model'
            _description = 'Test Model'
            _table = 'test_model'

        assert TestModel._name == 'test.model'
        assert TestModel._description == 'Test Model'
        assert TestModel._table == 'test_model'

    def test_base_model_common_fields(self):
        """Test BaseModel common fields"""
        class TestModel(BaseModel):
            _name = 'test.model'

        # Check that common fields are present
        assert hasattr(TestModel, 'id')
        assert hasattr(TestModel, 'name')
        assert hasattr(TestModel, 'createAt')
        assert hasattr(TestModel, 'updateAt')

        # Check field types
        assert isinstance(TestModel.id, Char)
        assert isinstance(TestModel.name, Char)
        assert isinstance(TestModel.createAt, Datetime)
        assert isinstance(TestModel.updateAt, Datetime)

    def test_base_model_custom_fields(self):
        """Test BaseModel with custom fields"""
        class TestModel(BaseModel):
            _name = 'test.model'

            description = Text(string='Description')
            active = Boolean(string='Active', default=True)
            sort_order = Integer(string='Sort Order', default=10)

        assert hasattr(TestModel, 'description')
        assert hasattr(TestModel, 'active')
        assert hasattr(TestModel, 'sort_order')

        assert isinstance(TestModel.description, Text)
        assert isinstance(TestModel.active, Boolean)
        assert isinstance(TestModel.sort_order, Integer)

    def test_base_model_instance_creation(self):
        """Test BaseModel instance creation"""
        class TestModel(BaseModel):
            _name = 'test.model'

        instance = TestModel()

        assert hasattr(instance, '_values')
        assert hasattr(instance, '_fields')
        assert hasattr(instance, '_is_new_record')
        assert instance._is_new_record is True

    @pytest.mark.asyncio
    async def test_base_model_create(self):
        """Test BaseModel create operation"""
        class TestModel(BaseModel):
            _name = 'test.model'

        with patch('erp.models.base.DatabaseRegistry.get_current_database') as mock_db:
            mock_db_instance = AsyncMock()
            mock_db_instance.insert.return_value = "test_id"
            mock_db.return_value = mock_db_instance

            instance = TestModel()
            result = await instance.create({'name': 'Test Record'})

            assert result is True
            mock_db_instance.insert.assert_called_once()

    @pytest.mark.asyncio
    async def test_base_model_write(self):
        """Test BaseModel write operation"""
        class TestModel(BaseModel):
            _name = 'test.model'

        with patch('erp.models.base.DatabaseRegistry.get_current_database') as mock_db:
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance

            instance = TestModel()
            instance._is_new_record = False
            instance._values = {'id': 'test_id', 'name': 'Original Name'}

            await instance.write({'name': 'Updated Name'})

            assert instance._values['name'] == 'Updated Name'
            mock_db_instance.update.assert_called_once()

    @pytest.mark.asyncio
    async def test_base_model_unlink(self):
        """Test BaseModel unlink operation"""
        class TestModel(BaseModel):
            _name = 'test.model'

        with patch('erp.models.base.DatabaseRegistry.get_current_database') as mock_db:
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance

            instance = TestModel()
            instance._is_new_record = False
            instance._values = {'id': 'test_id'}

            result = await instance.unlink()

            assert result is True
            mock_db_instance.delete.assert_called_once()

    def test_base_model_to_dict(self):
        """Test BaseModel to_dict conversion"""
        class TestModel(BaseModel):
            _name = 'test.model'

        instance = TestModel()
        instance._values = {'id': 'test_id', 'name': 'Test Name'}

        result = instance.to_dict()

        assert result == {'id': 'test_id', 'name': 'Test Name'}
        assert result is not instance._values  # Should be a copy
