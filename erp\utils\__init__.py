"""
Utilities package for ERP system
Contains various utility functions and classes organized in modular subdirectories
"""

# Import all utilities for backward compatibility
# Core utilities
from .core import (
    APIResponse, ModelResponse, handle_database_error, handle_generic_error,
    DomainFilter,
    SystemInfo, print_startup_banner, get_database_info, print_database_info
)

# Schema utilities
from .schema import (
    SchemaGenerator, SchemaComparator,
    camel_to_snake_case
)

# IR utilities
from .ir import (
    IRPopulationManager, ir_population_manager,
    IRMetadataOperations,
    IRValidationManager
)

# Validation utilities
from .validation import (
    ValidationManager, get_validation_manager,
    RegistryUpdater, get_registry_updater
)

# Timezone utilities
from .timezone import (
    get_all_timezones, get_common_timezones, validate_timezone,
    get_timezone_offset, convert_timezone, get_user_timezone_display,
    get_timezone_selection, get_timezone_selection_lambda
)

# Handler utilities
from .handlers import (
    ModelRequestHandler,
    RequestValidator
)

# Middleware (keep existing structure)
from .middleware import (
    database_middleware, timing_middleware, error_handling_middleware,
    logging_middleware, environment_middleware
)

__all__ = [
    # Core utilities
    'APIResponse', 'ModelResponse', 'handle_database_error', 'handle_generic_error',
    'DomainFilter',
    'SystemInfo', 'print_startup_banner', 'get_database_info', 'print_database_info',

    # Schema utilities
    'SchemaGenerator', 'SchemaComparator',
    'camel_to_snake_case',

    # IR utilities
    'IRPopulationManager', 'ir_population_manager',
    'IRMetadataOperations',
    'IRValidationManager',

    # Validation utilities
    'ValidationManager', 'get_validation_manager',
    'RegistryUpdater', 'get_registry_updater',

    # Timezone utilities
    'get_all_timezones', 'get_common_timezones', 'validate_timezone',
    'get_timezone_offset', 'convert_timezone', 'get_user_timezone_display',
    'get_timezone_selection', 'get_timezone_selection_lambda',

    # Handler utilities
    'ModelRequestHandler',
    'RequestValidator',

    # Middleware
    'database_middleware', 'timing_middleware', 'error_handling_middleware',
    'logging_middleware', 'environment_middleware'
]
