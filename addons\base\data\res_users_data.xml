<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">

    <!-- System Administrator User -->
    <record id="user_admin" model="res.users">
        <field name="name">Administrator</field>
        <field name="email"><EMAIL></field>
        <field name="phone">******-123-4567</field>
        <field name="lang">en_US</field>
        <field name="tz">UTC</field>
        <field name="login">admin</field>
        <field name="password">admin</field>
        <field name="active">True</field>
        <field name="groups_id" eval="[(6, 0, [ref('group_system'), ref('group_user_admin')])]"/>
        <field name="notification_type">inbox</field>
        <field name="signature">--
Administrator
My Company
<EMAIL></field>
    </record>

    <!-- Public User (for anonymous access) -->
    <record id="user_public" model="res.users">
        <field name="name">Public User</field>
        <field name="email">public@localhost</field>
        <field name="lang">en_US</field>
        <field name="tz">UTC</field>
        <field name="login">public</field>
        <field name="password" eval="False"/>
        <field name="active">True</field>
        <field name="groups_id" eval="[(6, 0, [ref('group_public')])]"/>
        <field name="notification_type">email</field>
    </record>

</data>
