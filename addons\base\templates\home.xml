<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="home.html">
        <html>
            <head>
                <meta charset="UTF-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
                <title t-esc="title"/>
                <script src="https://cdn.tailwindcss.com"></script>
                <style>
                    .code-block {
                        background-color: #f8f9fa;
                        border: 1px solid #e9ecef;
                        border-radius: 0.375rem;
                        padding: 1rem;
                        font-family: 'Courier New', monospace;
                        font-size: 0.875rem;
                        overflow-x: auto;
                    }
                    .info-card {
                        background: white;
                        border-radius: 0.5rem;
                        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                        border: 1px solid #e5e7eb;
                    }
                    .status-badge {
                        display: inline-block;
                        padding: 0.25rem 0.75rem;
                        border-radius: 9999px;
                        font-size: 0.75rem;
                        font-weight: 600;
                        text-transform: uppercase;
                    }
                    .status-connected {
                        background-color: #dcfce7;
                        color: #166534;
                    }
                    .status-error {
                        background-color: #fef2f2;
                        color: #dc2626;
                    }
                </style>
            </head>
            <body class="bg-gray-50 min-h-screen">
                <div class="container mx-auto px-4 py-8">
                    <!-- Header -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900 mb-2" t-esc="title"/>
                        <p class="text-gray-600">ERP System Environment and Registry Information</p>
                    </div>

                    <!-- Database Information -->
                    <div class="info-card p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0-2.21-1.79-4-4-4H4V7z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7c0-2.21 1.79-4 4-4h8c2.21 0 4 1.79 4 4v10c0 2.21-1.79 4-4 4"/>
                            </svg>
                            Database Information
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Database Name</label>
                                <p class="text-lg font-mono text-blue-600" t-esc="db_name"/>
                            </div>
                            <div t-if="db_info and db_info.get('database')">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                <span t-att-class="'status-badge ' + ('status-connected' if db_info['database'].get('status') == 'connected' else 'status-error')" 
                                      t-esc="db_info['database'].get('status', 'unknown')"/>
                            </div>
                            <div t-if="db_info and db_info.get('database') and db_info['database'].get('size')">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Size</label>
                                <p class="text-sm text-gray-900" t-esc="db_info['database']['size']"/>
                            </div>
                            <div t-if="db_info and db_info.get('database')">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Tables</label>
                                <p class="text-sm text-gray-900" t-esc="db_info['database'].get('tables', 0)"/>
                            </div>
                        </div>
                    </div>

                    <!-- Environment Information -->
                    <div class="info-card p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Environment Information
                        </h2>
                        <div t-if="db_info and db_info.get('environment')">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Current Environment</label>
                                    <p class="text-sm font-mono text-gray-900" t-esc="db_info['environment'].get('current_env', 'None')"/>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                                    <p class="text-sm text-gray-900" t-esc="db_info['environment'].get('user_id', 'None')"/>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Database</label>
                                    <p class="text-sm font-mono text-blue-600" t-esc="db_info['environment'].get('database', 'None')"/>
                                </div>
                            </div>
                            <div t-if="db_info['environment'].get('context')">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Context</label>
                                <div class="code-block">
                                    <pre t-esc="db_info['environment']['context']"/>
                                </div>
                            </div>
                        </div>
                        <div t-if="not db_info or not db_info.get('environment')">
                            <p class="text-gray-500 italic">No environment information available</p>
                        </div>
                    </div>

                    <!-- Registry Information -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- Models -->
                        <div class="info-card p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                Models Registry
                            </h3>
                            <div t-if="db_info and db_info.get('models')">
                                <p class="text-2xl font-bold text-blue-600 mb-2" t-esc="db_info['models'].get('registered_count', 0)"/>
                                <p class="text-sm text-gray-600 mb-4">Registered Models</p>
                                <div t-if="db_info['models'].get('models')" class="max-h-48 overflow-y-auto">
                                    <t t-foreach="db_info['models']['models']" t-as="model">
                                        <div class="border-b border-gray-200 py-2 last:border-b-0">
                                            <p class="font-medium text-sm text-gray-900" t-esc="model['name']"/>
                                            <p class="text-xs text-gray-500" t-esc="model['description']"/>
                                            <p class="text-xs text-blue-600 font-mono" t-esc="model['table']"/>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>

                        <!-- Addons -->
                        <div class="info-card p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                Addons Registry
                            </h3>
                            <div t-if="db_info and db_info.get('addons')">
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p class="text-xl font-bold text-green-600" t-esc="db_info['addons'].get('installed_count', 0)"/>
                                        <p class="text-xs text-gray-600">Installed</p>
                                    </div>
                                    <div>
                                        <p class="text-xl font-bold text-gray-600" t-esc="db_info['addons'].get('available_count', 0)"/>
                                        <p class="text-xs text-gray-600">Available</p>
                                    </div>
                                </div>
                                <div t-if="db_info['addons'].get('addons')" class="max-h-48 overflow-y-auto">
                                    <t t-foreach="db_info['addons']['addons']" t-as="addon">
                                        <div class="border-b border-gray-200 py-2 last:border-b-0 flex justify-between items-center">
                                            <div>
                                                <p class="font-medium text-sm text-gray-900" t-esc="addon['name']"/>
                                                <p class="text-xs text-gray-500" t-esc="addon['version']"/>
                                            </div>
                                            <span t-att-class="'status-badge ' + ('status-connected' if addon['state'] == 'installed' else 'status-error')" 
                                                  t-esc="addon['state']"/>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tables Information -->
                    <div class="info-card p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"/>
                            </svg>
                            Database Tables
                        </h3>
                        <div t-if="db_info and db_info.get('tables')">
                            <div class="grid grid-cols-3 gap-4 mb-4">
                                <div>
                                    <p class="text-xl font-bold text-blue-600" t-esc="db_info['tables'].get('total_count', 0)"/>
                                    <p class="text-xs text-gray-600">Total Tables</p>
                                </div>
                                <div>
                                    <p class="text-xl font-bold text-green-600" t-esc="db_info['tables'].get('model_tables', 0)"/>
                                    <p class="text-xs text-gray-600">Model Tables</p>
                                </div>
                                <div>
                                    <p class="text-xl font-bold text-gray-600" t-esc="db_info['tables'].get('system_tables', 0)"/>
                                    <p class="text-xs text-gray-600">System Tables</p>
                                </div>
                            </div>
                            <div t-if="db_info['tables'].get('tables')" class="max-h-64 overflow-y-auto">
                                <table class="min-w-full text-sm">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-3 py-2 text-left font-medium text-gray-700">Table Name</th>
                                            <th class="px-3 py-2 text-left font-medium text-gray-700">Model</th>
                                            <th class="px-3 py-2 text-left font-medium text-gray-700">Owner</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <t t-foreach="db_info['tables']['tables']" t-as="table">
                                            <tr>
                                                <td class="px-3 py-2 font-mono text-blue-600" t-esc="table['name']"/>
                                                <td class="px-3 py-2 text-gray-900" t-esc="table.get('model', '-')"/>
                                                <td class="px-3 py-2 text-gray-600" t-esc="table['owner']"/>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Configuration Information -->
                    <div class="info-card p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Configuration
                        </h3>
                        <div t-if="config" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Multi-DB Mode</label>
                                <span t-att-class="'status-badge ' + ('status-connected' if config.get('is_multi_db_mode') else 'status-error')" 
                                      t-esc="'Yes' if config.get('is_multi_db_mode') else 'No'"/>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">List DB</label>
                                <span t-att-class="'status-badge ' + ('status-connected' if config.get('list_db') else 'status-error')" 
                                      t-esc="'Yes' if config.get('list_db') else 'No'"/>
                            </div>
                            <div t-if="config.get('db_filter')">
                                <label class="block text-sm font-medium text-gray-700 mb-1">DB Filter</label>
                                <p class="text-sm font-mono text-gray-900" t-esc="config['db_filter']"/>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Information -->
                    <div class="info-card p-6" t-if="db_info and db_info.get('performance')">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                            Performance Metrics
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data Collection Time</label>
                                <p class="text-lg font-mono text-blue-600" t-esc="'{:.3f}s'.format(db_info['performance'].get('data_collection_time', 0))"/>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="mt-8 text-center text-gray-500 text-sm">
                        <p>ERP System - Environment and Registry Information</p>
                        <p>Generated at <span t-esc="request.url if request else 'unknown'"/></p>
                    </div>
                </div>
            </body>
        </html>
    </t>
</templates>
