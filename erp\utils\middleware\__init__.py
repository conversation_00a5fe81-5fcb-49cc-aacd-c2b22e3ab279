from .database import DatabaseMiddleware
from .timing import TimingMiddleware
from .error_handling import ErrorHandlingMiddleware
from .logging import LoggingMiddleware
from .environment import EnvironmentMiddleware
from .transaction import TransactionMiddleware

timing_middleware = TimingMiddleware.process_request
error_handling_middleware = ErrorHandlingMiddleware.process_request
logging_middleware = LoggingMiddleware.process_request
database_middleware = DatabaseMiddleware.process_request
environment_middleware = EnvironmentMiddleware.process_request
transaction_middleware = TransactionMiddleware.process_request