"""
Shared Validation Utilities
Provides reusable validation functionality for module installers.
"""
from typing import List, Dict, Any, Optional, TYPE_CHECKING
from ...logging import get_logger

if TYPE_CHECKING:
    from ...database.manager import DatabaseManager
    from ...environment import Environment


class ValidationManager:
    """
    Shared validation manager utility that provides generic database schema,
    metadata, and registry validation capabilities for any module installer.
    """

    def __init__(self):
        self.logger = get_logger(__name__)

    async def validate_tables_exist(self, db_manager: 'DatabaseManager', table_names: List[str]) -> bool:
        """
        Validate that all specified database tables exist.
        
        Args:
            db_manager: Database manager for queries
            table_names: List of table names to validate
            
        Returns:
            True if all tables exist, False otherwise
        """
        try:
            self.logger.debug(f"Validating existence of {len(table_names)} tables...")

            for table_name in table_names:
                exists = await db_manager.fetchval(
                    """SELECT EXISTS (
                       SELECT FROM information_schema.tables
                       WHERE table_schema = 'public'
                       AND table_name = $1
                    )""",
                    table_name
                )

                if not exists:
                    self.logger.error(f"Required table {table_name} does not exist")
                    return False

            self.logger.debug("✓ Database tables validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate database tables: {e}")
            return False

    async def validate_constraints_exist(self, db_manager: 'DatabaseManager', constraint_names: List[str]) -> bool:
        """
        Validate that all specified database constraints exist.
        
        Args:
            db_manager: Database manager for queries
            constraint_names: List of constraint names to validate
            
        Returns:
            True if all constraints exist, False otherwise
        """
        try:
            self.logger.debug(f"Validating existence of {len(constraint_names)} constraints...")

            for constraint_name in constraint_names:
                exists = await db_manager.fetchval(
                    """SELECT EXISTS (
                       SELECT FROM information_schema.table_constraints
                       WHERE constraint_name = $1
                    )""",
                    constraint_name
                )

                if not exists:
                    self.logger.error(f"Required constraint {constraint_name} does not exist")
                    return False

            self.logger.debug("✓ Database constraints validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate database constraints: {e}")
            return False

    async def validate_indexes_exist(self, db_manager: 'DatabaseManager', index_names: List[str]) -> bool:
        """
        Validate that all specified database indexes exist.
        
        Args:
            db_manager: Database manager for queries
            index_names: List of index names to validate
            
        Returns:
            True if all indexes exist, False otherwise
        """
        try:
            self.logger.debug(f"Validating existence of {len(index_names)} indexes...")

            for index_name in index_names:
                exists = await db_manager.fetchval(
                    """SELECT EXISTS (
                       SELECT FROM pg_indexes
                       WHERE indexname = $1
                    )""",
                    index_name
                )

                if not exists:
                    self.logger.error(f"Required index {index_name} does not exist")
                    return False

            self.logger.debug("✓ Database indexes validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate database indexes: {e}")
            return False

    async def validate_models_in_ir_metadata(self, db_manager: 'DatabaseManager', model_names: List[str]) -> bool:
        """
        Validate that all specified models exist in IR metadata.
        
        Args:
            db_manager: Database manager for queries
            model_names: List of model names to validate
            
        Returns:
            True if all models exist in IR metadata, False otherwise
        """
        try:
            self.logger.debug(f"Validating IR metadata for {len(model_names)} models...")

            for model_name in model_names:
                exists = await db_manager.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM ir_model WHERE model = $1)",
                    model_name
                )
                
                if not exists:
                    self.logger.error(f"Model {model_name} not found in ir_model table")
                    return False

                # Also validate that the model has fields in ir_model_fields
                field_count = await db_manager.fetchval(
                    "SELECT COUNT(*) FROM ir_model_fields WHERE model = $1",
                    model_name
                )
                
                if field_count == 0:
                    self.logger.warning(f"Model {model_name} has no fields in ir_model_fields table")

            self.logger.debug("✓ IR metadata validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate IR metadata: {e}")
            return False

    async def validate_module_registration(self, db_manager: 'DatabaseManager', module_name: str, 
                                         expected_state: str = 'installed') -> bool:
        """
        Validate that a module is properly registered in ir_module_module.
        
        Args:
            db_manager: Database manager for queries
            module_name: Name of the module to validate
            expected_state: Expected module state (default: 'installed')
            
        Returns:
            True if module is properly registered, False otherwise
        """
        try:
            self.logger.debug(f"Validating registration of module '{module_name}'...")

            # Check that module exists and has correct state
            result = await db_manager.fetchrow(
                "SELECT name, state, installable FROM ir_module_module WHERE name = $1",
                module_name
            )

            if not result:
                self.logger.error(f"Module '{module_name}' not found in ir_module_module table")
                return False

            if result['state'] != expected_state:
                self.logger.error(f"Module '{module_name}' state is '{result['state']}', expected '{expected_state}'")
                return False

            if not result['installable']:
                self.logger.error(f"Module '{module_name}' is marked as not installable")
                return False

            self.logger.debug(f"✓ Module '{module_name}' registration validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate module '{module_name}' registration: {e}")
            return False

    async def validate_module_in_registry(self, env: 'Environment', module_name: str, 
                                        expected_models: Optional[List[str]] = None) -> bool:
        """
        Validate that a module is properly loaded in the registry.
        
        Args:
            env: Environment for database operations
            module_name: Name of the module to validate
            expected_models: Optional list of model names that should be available
            
        Returns:
            True if module is properly loaded in registry, False otherwise
        """
        try:
            from ...database.memory.registry_manager import MemoryRegistryManager

            self.logger.debug(f"Validating registry state for module '{module_name}'...")

            db_name = env.cr.db_name
            registry = await MemoryRegistryManager.get_registry(db_name)
            
            # Validate that module is loaded
            if module_name not in registry.installed_modules:
                self.logger.error(f"Module '{module_name}' not found in registry installed modules")
                return False

            # Validate expected models if provided
            if expected_models:
                for model_name in expected_models:
                    if not registry.model_metadata_manager.has_model(model_name):
                        self.logger.error(f"Expected model '{model_name}' not found in registry metadata for module '{module_name}'")
                        return False

            self.logger.debug(f"✓ Registry validation completed successfully for module '{module_name}'")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate module '{module_name}' in registry: {e}")
            return False

    async def get_validation_report(self, db_manager: 'DatabaseManager', env: 'Environment',
                                  module_name: str, required_tables: List[str], 
                                  required_models: List[str], required_constraints: Optional[List[str]] = None,
                                  required_indexes: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Get comprehensive validation report for a module.
        
        Args:
            db_manager: Database manager for queries
            env: Environment for database operations
            module_name: Name of the module to validate
            required_tables: List of required table names
            required_models: List of required model names
            required_constraints: Optional list of required constraint names
            required_indexes: Optional list of required index names
            
        Returns:
            Dictionary containing detailed validation report
        """
        try:
            report = {
                'module_name': module_name,
                'schema_valid': await self.validate_tables_exist(db_manager, required_tables),
                'metadata_valid': await self.validate_models_in_ir_metadata(db_manager, required_models),
                'registration_valid': await self.validate_module_registration(db_manager, module_name),
                'registry_valid': await self.validate_module_in_registry(env, module_name, required_models),
                'overall_valid': False
            }

            # Add optional validations if specified
            if required_constraints:
                report['constraints_valid'] = await self.validate_constraints_exist(db_manager, required_constraints)
            else:
                report['constraints_valid'] = True

            if required_indexes:
                report['indexes_valid'] = await self.validate_indexes_exist(db_manager, required_indexes)
            else:
                report['indexes_valid'] = True

            # Calculate overall validity
            report['overall_valid'] = all([
                report['schema_valid'],
                report['metadata_valid'],
                report['registration_valid'],
                report['registry_valid'],
                report['constraints_valid'],
                report['indexes_valid']
            ])

            return report

        except Exception as e:
            self.logger.error(f"Failed to generate validation report for module '{module_name}': {e}")
            return {'error': str(e), 'overall_valid': False, 'module_name': module_name}


# Singleton instance for reuse
_validation_manager = None

def get_validation_manager() -> ValidationManager:
    """Get shared ValidationManager instance"""
    global _validation_manager
    if _validation_manager is None:
        _validation_manager = ValidationManager()
    return _validation_manager
