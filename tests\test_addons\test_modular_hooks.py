"""
Test suite for modular addon hook system

This module tests:
- Modular hook components (types, context, registry, decorators)
- Hook registration and execution
- Hook priority handling
- Hook context management
- Decorator functionality
"""
from unittest.mock import MagicMock

from erp.addons.hooks import (
    HookType,
    HookContext,
    AddonHook,
    HookRegistry,
    get_hook_registry,
    pre_install_hook,
    post_install_hook,
    pre_uninstall_hook,
    post_uninstall_hook,
    pre_upgrade_hook,
    post_upgrade_hook,
)


class TestHookTypes:
    """Test hook type enumeration"""
    
    def test_hook_type_values(self):
        """Test that all hook types are defined"""
        assert HookType.PRE_INSTALL
        assert HookType.POST_INSTALL
        assert HookType.PRE_UNINSTALL
        assert HookType.POST_UNINSTALL
        assert HookType.PRE_UPGRADE
        assert HookType.POST_UPGRADE
    
    def test_hook_type_string_representation(self):
        """Test hook type string representation"""
        assert str(HookType.PRE_INSTALL) in ["HookType.PRE_INSTALL", "pre_install"]
        assert str(HookType.POST_INSTALL) in ["HookType.POST_INSTALL", "post_install"]


class TestHookContext:
    """Test hook context functionality"""
    
    def test_hook_context_creation(self):
        """Test HookContext creation with required parameters"""
        mock_env = MagicMock()

        context = HookContext(
            addon_name="test_addon",
            hook_type=HookType.PRE_INSTALL,
            env=mock_env,
            metadata={"key": "value"}
        )

        assert context.hook_type == HookType.PRE_INSTALL
        assert context.addon_name == "test_addon"
        assert context.env == mock_env
        assert context.data == {"metadata": {"key": "value"}}
    
    def test_hook_context_with_custom_attributes(self):
        """Test HookContext with custom attributes"""
        mock_env = MagicMock()

        context = HookContext(
            addon_name="test_addon",
            hook_type=HookType.POST_INSTALL,
            env=mock_env,
            custom_param="test_value",
            another_param=42
        )

        assert context.data["custom_param"] == "test_value"
        assert context.data["another_param"] == 42


class TestAddonHook:
    """Test AddonHook functionality"""
    
    def test_addon_hook_creation(self):
        """Test AddonHook creation"""
        def test_hook(context):
            return True
        
        hook = AddonHook(
            func=test_hook,
            hook_type=HookType.PRE_INSTALL,
            addon_name="test_addon",
            priority=50
        )
        
        assert hook.func == test_hook
        assert hook.hook_type == HookType.PRE_INSTALL
        assert hook.addon_name == "test_addon"
        assert hook.priority == 50
    
    def test_addon_hook_execution(self):
        """Test AddonHook execution"""
        def test_hook(context):
            return f"Hook executed for {context.addon_name}"
        
        hook = AddonHook(
            func=test_hook,
            hook_type=HookType.PRE_INSTALL,
            addon_name="test_addon",
            priority=50
        )
        
        mock_env = MagicMock()
        context = HookContext(
            hook_type=HookType.PRE_INSTALL,
            addon_name="test_addon",
            env=mock_env
        )
        
        result = hook.func(context)
        assert result == "Hook executed for test_addon"


class TestHookRegistry:
    """Test HookRegistry functionality"""
    
    def test_hook_registry_singleton(self):
        """Test that get_hook_registry returns the same instance"""
        registry1 = get_hook_registry()
        registry2 = get_hook_registry()
        assert registry1 is registry2
    
    def test_hook_registration(self):
        """Test hook registration"""
        registry = HookRegistry()

        def test_hook(context):
            return True

        registry.register_hook(
            func=test_hook,
            hook_type=HookType.PRE_INSTALL,
            addon_name="test_addon",
            priority=50
        )

        hooks = registry.get_hooks(HookType.PRE_INSTALL)
        # Find our hook among all hooks
        our_hook = None
        for hook in hooks:
            if hook.addon_name == "test_addon" and hook.func == test_hook:
                our_hook = hook
                break

        assert our_hook is not None
        assert our_hook.priority == 50
    
    def test_hook_priority_ordering(self):
        """Test that hooks are ordered by priority"""
        registry = HookRegistry()

        def hook1(context):
            return "hook1"

        def hook2(context):
            return "hook2"

        def hook3(context):
            return "hook3"

        # Register hooks in non-priority order
        registry.register_hook(hook2, HookType.PRE_INSTALL, "test_addon", 50)
        registry.register_hook(hook1, HookType.PRE_INSTALL, "test_addon", 10)
        registry.register_hook(hook3, HookType.PRE_INSTALL, "test_addon", 100)

        hooks = registry.get_hooks(HookType.PRE_INSTALL)

        # Find our test addon hooks and check their order
        test_hooks = [h for h in hooks if h.addon_name == "test_addon"]
        assert len(test_hooks) == 3
        assert test_hooks[0].priority == 10  # hook1
        assert test_hooks[1].priority == 50  # hook2
        assert test_hooks[2].priority == 100  # hook3


class TestHookDecorators:
    """Test hook decorator functionality"""
    
    def test_pre_install_hook_decorator(self):
        """Test pre_install_hook decorator"""
        @pre_install_hook(addon_name="test_addon_decorator", priority=50)
        def test_hook(context):
            return True

        # The decorator should register the hook
        registry = get_hook_registry()
        hooks = registry.get_hooks(HookType.PRE_INSTALL)

        # Find our hook (there might be others)
        our_hook = None
        for hook in hooks:
            if hook.func == test_hook and hook.addon_name == "test_addon_decorator":
                our_hook = hook
                break

        assert our_hook is not None
        assert our_hook.hook_type == HookType.PRE_INSTALL
        assert our_hook.addon_name == "test_addon_decorator"
        assert our_hook.priority == 50
    
    def test_post_install_hook_decorator(self):
        """Test post_install_hook decorator"""
        @post_install_hook(addon_name="test_addon_post", priority=75)
        def test_hook(context):
            return True

        registry = get_hook_registry()
        hooks = registry.get_hooks(HookType.POST_INSTALL)

        our_hook = None
        for hook in hooks:
            if hook.func == test_hook and hook.addon_name == "test_addon_post":
                our_hook = hook
                break

        assert our_hook is not None
        assert our_hook.hook_type == HookType.POST_INSTALL
    
    def test_all_hook_decorators(self):
        """Test all hook decorators are available"""
        decorators = [
            pre_install_hook,
            post_install_hook,
            pre_uninstall_hook,
            post_uninstall_hook,
            pre_upgrade_hook,
            post_upgrade_hook,
        ]
        
        for decorator in decorators:
            assert callable(decorator)
    
    def test_hook_decorator_with_default_priority(self):
        """Test hook decorator with default priority"""
        @pre_install_hook(addon_name="test_addon_default")
        def test_hook(context):
            return True

        registry = get_hook_registry()
        hooks = registry.get_hooks(HookType.PRE_INSTALL)

        our_hook = None
        for hook in hooks:
            if hook.func == test_hook and hook.addon_name == "test_addon_default":
                our_hook = hook
                break

        assert our_hook is not None
        # Should have a default priority (typically 50)
        assert isinstance(our_hook.priority, int)
