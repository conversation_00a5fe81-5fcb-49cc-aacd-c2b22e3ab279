"""
Schema generation utilities
Contains logic for generating database schemas from model definitions
"""
from typing import Dict, List, Optional, Any
from ...logging import get_logger
from .types import (
    camel_to_snake_case, map_field_type_to_postgresql,
    get_default_value_sql
)

logger = get_logger(__name__)


class SchemaGenerator:
    """Utility for generating database schema from model definitions"""

    @classmethod
    def get_model_schema(cls, model_name: str, model_module_map: Dict[str, str] = None,
                        class_name_map: Dict[str, str] = None) -> Optional[Dict[str, Any]]:
        """
        Generate schema for a model given its name by dynamically loading the model

        Args:
            model_name: Technical name of the model (e.g., 'ir.model')
            model_module_map: Optional mapping of model names to module paths
            class_name_map: Optional mapping of model names to class names

        Returns:
            Dictionary containing schema information or None if model not found
        """
        import importlib
        import sys

        # Use provided mappings or try to discover the model
        if model_module_map:
            module_path = model_module_map.get(model_name)
        else:
            # Try to discover model from registry or other sources
            module_path = cls._discover_model_module_path(model_name)

        if not module_path:
            logger.error(f"No module mapping found for model: {model_name}")
            return None

        try:
            # Import the module to load the model class
            module = importlib.import_module(module_path)

            # Find the model class in the module
            model_class = None

            if class_name_map:
                class_name = class_name_map.get(model_name)
                if class_name and hasattr(module, class_name):
                    model_class = getattr(module, class_name)

            if not model_class:
                # Try to find any class that has the right _name
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (hasattr(attr, '_name') and
                        getattr(attr, '_name') == model_name and
                        hasattr(attr, '__bases__')):
                        model_class = attr
                        break

            if not model_class:
                logger.error(f"Model class not found for: {model_name}")
                return None

            # Extract schema from model class
            schema = cls._extract_schema_from_model_class(model_class)

            # Unload the module to avoid keeping it in memory
            if module_path in sys.modules:
                del sys.modules[module_path]

            return schema

        except Exception as e:
            logger.error(f"Error loading model {model_name}: {e}")
            return None

    @classmethod
    def _discover_model_module_path(cls, model_name: str) -> Optional[str]:
        """
        Try to discover the module path for a model.
        This is a fallback when no explicit mapping is provided.
        """
        # TODO: Enhance to use model registry or other discovery mechanisms
        return None

    @classmethod
    def _extract_schema_from_model_class(cls, model_class) -> Dict[str, Any]:
        """
        Extract schema information from a model class

        Args:
            model_class: The model class to extract schema from

        Returns:
            Dictionary containing schema information
        """

        # Get table name
        table_name = getattr(model_class, '_table', model_class._name.replace('.', '_'))

        # Extract fields
        fields = {}
        indexes = []

        # Extract fields from model class _fields attribute
        if hasattr(model_class, '_fields'):
            for field_name, field_obj in model_class._fields.items():
                field_info = cls._field_to_schema(field_name, field_obj)
                if field_info:
                    # Special handling for id field
                    if field_name == 'id':
                        field_info['type'] = 'UUID'
                        field_info['primary_key'] = True
                        field_info['default'] = 'gen_random_uuid()'

                    fields[field_name] = field_info

                    # Add index if field is indexed
                    if getattr(field_obj, 'index', False):
                        db_column_name = camel_to_snake_case(field_name)
                        indexes.append({
                            'name': f'idx_{table_name}_{db_column_name}',
                            'columns': [db_column_name]
                        })

        return {
            'table_name': table_name,
            'fields': fields,
            'indexes': indexes
        }

    @classmethod
    def _field_to_schema(cls, field_name: str, field_obj) -> Optional[Dict[str, Any]]:
        """
        Convert a field object to schema information

        Args:
            field_name: Name of the field
            field_obj: Field object instance

        Returns:
            Dictionary containing field schema information
        """

        field_type = type(field_obj).__name__

        # Skip fields that don't have SQL representation
        if field_type in ('One2Many', 'Many2Many', 'Related') and not getattr(field_obj, 'store', True):
            return None

        pg_type = map_field_type_to_postgresql(field_type, field_obj)

        schema_info = {
            'name': field_name,
            'type': pg_type,
            'required': getattr(field_obj, 'required', False),
            'unique': getattr(field_obj, 'unique', False),
            'primary_key': False
        }

        # Add relational field information
        if field_type in ('Many2One', 'One2One'):
            schema_info['foreign_key'] = {
                'references_table': field_obj.comodel_name.replace('.', '_'),
                'references_column': 'id',
                'on_delete': getattr(field_obj, 'ondelete', 'set null')
            }
            if field_type == 'One2One':
                schema_info['unique'] = True

        elif field_type == 'Many2Many':
            # Many2Many fields need intersection table info
            schema_info['many2many'] = {
                'comodel_name': field_obj.comodel_name,
                'relation_table': getattr(field_obj, 'relation_table', None),
                'column1': getattr(field_obj, 'column1', None),
                'column2': getattr(field_obj, 'column2', None)
            }

        # Add default value if specified
        if hasattr(field_obj, 'default') and field_obj.default is not None:
            if callable(field_obj.default):
                # For callable defaults (like lambda functions), try to evaluate them
                try:
                    default_value = field_obj.default()
                    if isinstance(default_value, str):
                        schema_info['default'] = f"'{default_value}'"
                    elif isinstance(default_value, bool):
                        schema_info['default'] = str(default_value).upper()
                    else:
                        schema_info['default'] = str(default_value)
                except:
                    # If evaluation fails, skip the default
                    pass
            elif isinstance(field_obj.default, str):
                schema_info['default'] = f"'{field_obj.default}'"
            elif isinstance(field_obj.default, bool):
                schema_info['default'] = str(field_obj.default).upper()
            else:
                schema_info['default'] = str(field_obj.default)

        return schema_info

    @classmethod
    def generate_create_table_sql(cls, model_name: str, model_module_map: Dict[str, str] = None,
                                 class_name_map: Dict[str, str] = None) -> Optional[str]:
        """
        Generate CREATE TABLE SQL statement for a model

        Args:
            model_name: Technical name of the model
            model_module_map: Optional mapping of model names to module paths
            class_name_map: Optional mapping of model names to class names

        Returns:
            SQL CREATE TABLE statement or None if model not found
        """
        schema = cls.get_model_schema(model_name, model_module_map, class_name_map)
        if not schema:
            return None

        table_name = schema['table_name']
        columns = []

        # Add field columns
        constraints = []
        for field_name, field_info in schema['fields'].items():
            sql_type = field_info['type']
            if not sql_type:  # Skip fields without SQL representation
                continue

            # Convert camelCase field names to snake_case for database columns
            db_column_name = camel_to_snake_case(field_name)
            column_def = f"{db_column_name} {sql_type}"

            if field_info.get('required', False):
                column_def += " NOT NULL"

            if field_info.get('default') is not None:
                default_val = field_info['default']
                if isinstance(default_val, str) and not default_val.startswith(('CURRENT_TIMESTAMP', 'gen_random_uuid()', "'", 'TRUE', 'FALSE')):
                    column_def += f" DEFAULT '{default_val}'"
                else:
                    column_def += f" DEFAULT {default_val}"

            # Add unique constraint if specified
            if field_info.get('unique', False):
                column_def += " UNIQUE"

            columns.append(column_def)

            # Collect foreign key constraints
            if 'foreign_key' in field_info:
                fk_info = field_info['foreign_key']
                on_delete_map = {
                    'cascade': 'CASCADE',
                    'set null': 'SET NULL',
                    'restrict': 'RESTRICT',
                    'no action': 'NO ACTION'
                }
                on_delete = on_delete_map.get(fk_info['on_delete'].lower(), 'SET NULL')

                constraint_name = f"fk_{table_name}_{field_name}"
                constraint = (
                    f"CONSTRAINT {constraint_name} FOREIGN KEY ({db_column_name}) "
                    f"REFERENCES {fk_info['references_table']} ({fk_info['references_column']}) "
                    f"ON DELETE {on_delete}"
                )
                constraints.append(constraint)

        # Add primary key
        columns.append("PRIMARY KEY (id)")

        # Add constraints to columns
        all_definitions = columns + constraints

        sql = f"""CREATE TABLE IF NOT EXISTS {table_name} (
    {',\n    '.join(all_definitions)}
);"""

        # Add indexes
        index_sqls = []
        for index in schema['indexes']:
            index_sql = f"CREATE INDEX IF NOT EXISTS {index['name']} ON {table_name} ({', '.join(index['columns'])});"
            index_sqls.append(index_sql)

        if index_sqls:
            sql += "\n\n" + "\n".join(index_sqls)

        return sql

    @classmethod
    def generate_create_table_sql_from_class(cls, model_name: str, model_class: type) -> Optional[str]:
        """
        Generate CREATE TABLE SQL statement for a model using the model class directly

        Args:
            model_name: Technical name of the model
            model_class: The model class

        Returns:
            SQL CREATE TABLE statement or None if model not found
        """
        try:
            schema = cls._extract_schema_from_model_class(model_class)
            if not schema:
                return None

            table_name = schema['table_name']
            columns = []

            for field_name, field_info in schema['fields'].items():
                column_def = cls._generate_column_definition(field_name, field_info)
                if column_def:
                    columns.append(column_def)

            if not columns:
                logger.error(f"No valid columns found for model {model_name}")
                return None

            sql = f"CREATE TABLE IF NOT EXISTS {table_name} (\n"
            sql += ",\n".join(f"    {col}" for col in columns)
            sql += "\n);"

            # Add indexes if any
            if schema.get('indexes'):
                for index in schema['indexes']:
                    index_sql = f"CREATE INDEX IF NOT EXISTS {index['name']} ON {table_name} ({', '.join(index['columns'])})"
                    sql += f"\n{index_sql};"

            return sql

        except Exception as e:
            logger.error(f"Error generating SQL for model class {model_class}: {e}")
            return None

    @classmethod
    def _generate_column_definition(cls, field_name: str, field_info: Dict[str, Any]) -> Optional[str]:
        """
        Generate SQL column definition from field information

        Args:
            field_name: Name of the field
            field_info: Dictionary containing field information (type, required, default, etc.)

        Returns:
            SQL column definition string or None if field should be skipped
        """
        sql_type = field_info.get('type')
        if not sql_type:  # Skip fields without SQL representation
            return None

        # Convert camelCase field names to snake_case for database columns
        db_column_name = camel_to_snake_case(field_name)
        column_def = f"{db_column_name} {sql_type}"

        # Add NOT NULL constraint if required
        if field_info.get('required', False):
            column_def += " NOT NULL"

        # Add default value if specified
        default_sql = get_default_value_sql(field_info)
        if default_sql:
            column_def += f" DEFAULT {default_sql}"

        # Add unique constraint if specified
        if field_info.get('unique', False):
            column_def += " UNIQUE"

        return column_def

    @classmethod
    def generate_many2many_table_sql(cls, model_name: str, field_name: str, field_obj) -> str:
        """
        Generate SQL for Many2many intersection table

        Args:
            model_name: Name of the model containing the Many2many field
            field_name: Name of the Many2many field
            field_obj: The Many2many field object

        Returns:
            SQL string to create the intersection table
        """
        from ...fields import Many2Many

        if not isinstance(field_obj, Many2Many):
            raise ValueError("Field must be a Many2Many field")

        table_name = field_obj.get_relation_table_name(model_name)
        col1, col2 = field_obj.get_column_names(model_name)

        # Determine which model is which
        model1_table = model_name.replace('.', '_')
        model2_table = field_obj.comodel_name.replace('.', '_')

        sql = f"""CREATE TABLE IF NOT EXISTS {table_name} (
    {col1} UUID NOT NULL,
    {col2} UUID NOT NULL,
    PRIMARY KEY ({col1}, {col2}),
    FOREIGN KEY ({col1}) REFERENCES {model1_table} (id) ON DELETE CASCADE,
    FOREIGN KEY ({col2}) REFERENCES {model2_table} (id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_{table_name}_{col1} ON {table_name} ({col1});
CREATE INDEX IF NOT EXISTS idx_{table_name}_{col2} ON {table_name} ({col2});"""

        return sql

    @classmethod
    async def generate_model_tables(cls, db_manager: 'DatabaseManager', model_names: List[str],
                                   model_module_map: Dict[str, str] = None,
                                   class_name_map: Dict[str, str] = None) -> Dict[str, bool]:
        """
        Generate database tables for specified models

        Args:
            db_manager: Database manager instance
            model_names: List of model names to generate tables for
            model_module_map: Optional mapping of model names to module paths
            class_name_map: Optional mapping of model names to class names

        Returns:
            Dictionary mapping model names to success status
        """
        results = {}

        logger.info(f"Generating tables for {len(model_names)} models...")

        created_count = 0
        for model_name in model_names:
            try:
                sql = cls.generate_create_table_sql(model_name, model_module_map, class_name_map)
                if sql:
                    logger.debug(f"Generated SQL for {model_name}")
                    await db_manager.execute(sql)
                    logger.debug(f"✓ Created table for model: {model_name}")
                    results[model_name] = True
                    created_count += 1
                else:
                    logger.error(f"✗ Failed to generate SQL for model: {model_name}")
                    results[model_name] = False
            except Exception as e:
                logger.error(f"✗ Error creating table for model {model_name}: {e}")
                if 'sql' in locals():
                    logger.error("Problematic SQL was:")
                    logger.error(sql)
                results[model_name] = False

        logger.info(f"✓ Generated {created_count} model tables successfully")
        return results

    @classmethod
    async def generate_model_tables_from_classes(cls, db_manager: 'DatabaseManager',
                                                model_classes: Dict[str, type]) -> Dict[str, bool]:
        """
        Generate database tables for models using model classes directly

        Args:
            db_manager: Database manager instance
            model_classes: Dictionary mapping model names to model classes

        Returns:
            Dictionary mapping model names to success status
        """
        results = {}

        logger.info(f"Generating tables for {len(model_classes)} models from classes...")

        created_count = 0
        for model_name, model_class in model_classes.items():
            try:
                sql = cls.generate_create_table_sql_from_class(model_name, model_class)
                if sql:
                    logger.debug(f"Generated SQL for {model_name}")
                    await db_manager.execute(sql)
                    logger.debug(f"✓ Created table for model: {model_name}")
                    results[model_name] = True
                    created_count += 1
                else:
                    logger.error(f"✗ Failed to generate SQL for model: {model_name}")
                    results[model_name] = False
            except Exception as e:
                logger.error(f"✗ Error creating table for model {model_name}: {e}")
                if 'sql' in locals():
                    logger.error("Problematic SQL was:")
                    logger.error(sql)
                results[model_name] = False

        logger.info(f"✓ Generated {created_count} model tables from classes successfully")
        return results

    @classmethod
    def generate_dynamic_constraints_and_indexes(cls, model_classes: Dict[str, type]) -> tuple:
        """
        Generate constraints and indexes dynamically from model field definitions

        Args:
            model_classes: Dictionary mapping model names to model classes

        Returns:
            Tuple of (constraints, indexes) lists
        """
        constraints = []
        indexes = []

        try:
            for model_name, model_class in model_classes.items():
                # Extract schema from model class
                schema = cls._extract_schema_from_model_class(model_class)
                if not schema:
                    continue

                table_name = schema['table_name']
                fields = schema['fields']

                # Generate constraints based on field properties
                for field_name, field_info in fields.items():
                    db_column_name = camel_to_snake_case(field_name)

                    # Unique constraints
                    if field_info.get('unique', False):
                        constraint_name = f"uk_{table_name}_{db_column_name}"
                        constraints.append({
                            'constraint_name': constraint_name,
                            'table_name': table_name,
                            'type': 'unique',
                            'columns': [db_column_name]
                        })

                    # Index for indexed fields
                    if field_info.get('index', False):
                        index_name = f"idx_{table_name}_{db_column_name}"
                        indexes.append({
                            'index_name': index_name,
                            'table_name': table_name,
                            'columns': [db_column_name]
                        })

                # Add any existing indexes from schema
                for index in schema.get('indexes', []):
                    indexes.append({
                        'index_name': index['name'],
                        'table_name': table_name,
                        'columns': index['columns']
                    })

            logger.debug(f"Generated {len(constraints)} constraints and {len(indexes)} indexes dynamically")
            return constraints, indexes

        except Exception as e:
            logger.error(f"Error generating dynamic constraints and indexes: {e}")
            return [], []

    @classmethod
    async def setup_table_constraints(cls, db_manager: 'DatabaseManager',
                                    constraints: List[Dict], indexes: List[Dict]) -> bool:
        """
        Set up table constraints and indexes

        Args:
            db_manager: Database manager instance
            constraints: List of constraint definitions
            indexes: List of index definitions

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create constraints
            for constraint in constraints:
                constraint_name = constraint['constraint_name']
                table_name = constraint['table_name']
                constraint_type = constraint['type']
                columns = constraint['columns']

                if constraint_type == 'unique':
                    sql = f"ALTER TABLE {table_name} ADD CONSTRAINT {constraint_name} UNIQUE ({', '.join(columns)})"
                    try:
                        await db_manager.execute(sql)
                        logger.debug(f"✓ Created constraint: {constraint_name}")
                    except Exception as e:
                        # Constraint might already exist, log but continue
                        logger.debug(f"Constraint {constraint_name} might already exist: {e}")

            # Create indexes
            for index in indexes:
                index_name = index['index_name']
                table_name = index['table_name']
                columns = index['columns']

                sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({', '.join(columns)})"
                try:
                    await db_manager.execute(sql)
                    logger.debug(f"✓ Created index: {index_name}")
                except Exception as e:
                    logger.debug(f"Index {index_name} creation issue: {e}")

            return True

        except Exception as e:
            logger.error(f"Error setting up constraints and indexes: {e}")
            return False
