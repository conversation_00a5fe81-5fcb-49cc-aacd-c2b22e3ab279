"""
Record Processor for ERP system

Specialized processor for handling standard model records with CRUD operations,
field validation, and relationship management.
"""

from typing import Dict, List, Any, Optional

from .base import BaseDataProcessor
from ..sql_helpers import SQLHelpers, ModelSQLHelpers
from ..xmlid_manager import XMLIDManager


class RecordProcessor(BaseDataProcessor):
    """
    Processor for standard model records
    
    Handles creation, updating, and deletion of regular model records
    with proper field validation and relationship handling.
    """
    
    def __init__(self, db_manager, name: str = "RecordProcessor"):
        super().__init__(db_manager, name)
        
        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_manager = XMLIDManager(db_manager)
        
        # Supported field types for processing
        self.supported_field_types = {
            'text', 'ref', 'eval', 'search', 'value'
        }
        
        # Models that should be excluded from standard processing
        self.excluded_models = {
            'ir.ui.view', 'ir.ui.menu', 'ir.actions.act_window',
            'ir.actions.server', 'ir.model.access', 'ir.rule'
        }
    
    def can_process(self, item: Dict[str, Any]) -> bool:
        """Check if this processor can handle the given item"""
        if not isinstance(item, dict):
            return False
        
        model = item.get('model')
        if not model:
            return False
        
        # Exclude special models that have dedicated processors
        if model in self.excluded_models:
            return False
        
        # Check if it's a standard record
        return 'values' in item
    
    def get_supported_models(self) -> List[str]:
        """Get list of models this processor supports"""
        return ['*']  # Supports all models except excluded ones
    
    def get_processing_order(self) -> int:
        """Get processing order (process after special elements)"""
        return 50
    
    async def _process_item(self, item: Dict[str, Any], **kwargs) -> bool:
        """Process a single record item"""
        model_name = item['model']
        xml_id = item.get('xml_id')
        values = item.get('values', {})
        noupdate = item.get('noupdate', False)
        forcecreate = item.get('forcecreate', False)
        
        try:
            # Validate model exists
            if not await self._validate_model_exists(model_name):
                self.result.add_error(f"Model {model_name} does not exist")
                return False
            
            # Process field values
            processed_values = await self._process_field_values(values, model_name)
            if processed_values is None:
                return False
            
            # Check if record exists
            existing_record_id = None
            if xml_id:
                existing_record_id = await self._find_record_by_xmlid(xml_id)
            
            if existing_record_id:
                # Update existing record
                if not noupdate:
                    success = await self._update_record(model_name, existing_record_id, processed_values)
                    if success:
                        self.logger.debug(f"Updated record {xml_id} in {model_name}")
                        return True
                else:
                    self.logger.debug(f"Skipped updating record {xml_id} (noupdate=True)")
                    return True
            else:
                # Create new record
                new_record_id = await self._create_record(model_name, processed_values)
                if new_record_id:
                    # Store XML ID mapping if provided
                    if xml_id:
                        await self._store_xmlid_mapping(xml_id, model_name, new_record_id)
                    
                    self.logger.debug(f"Created record {xml_id or 'no-id'} in {model_name}")
                    return True
            
            return False
            
        except Exception as e:
            error_msg = f"Failed to process record {xml_id or 'no-id'} in {model_name}: {e}"
            self.result.add_error(error_msg)
            return False
    
    async def _validate_model_exists(self, model_name: str) -> bool:
        """Validate that the model table exists"""
        table_name = model_name.replace('.', '_')
        return await self.sql.table_exists(table_name)
    
    async def _process_field_values(self, values: Dict[str, Any], model_name: str) -> Optional[Dict[str, Any]]:
        """Process and validate field values"""
        processed = {}
        
        for field_name, field_def in values.items():
            try:
                processed_value = await self._process_single_field(field_def, field_name, model_name)
                if processed_value is not None:
                    processed[field_name] = processed_value
            except Exception as e:
                error_msg = f"Error processing field {field_name} in {model_name}: {e}"
                self.result.add_error(error_msg)
                return None
        
        return processed
    
    async def _process_single_field(self, field_def: Any, field_name: str, model_name: str) -> Any:
        """Process a single field value"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value')
            
            if field_type == 'ref':
                # Reference to another record
                return await self._resolve_reference(field_value)
            elif field_type == 'eval':
                # Python expression to evaluate
                return self._evaluate_expression(field_value)
            elif field_type == 'search':
                # Search for record in specified model
                search_model = field_def.get('model')
                return await self._search_record(field_value, search_model)
            elif field_type in ['text', 'value']:
                # Direct text value
                return field_value
            else:
                self.result.add_warning(f"Unknown field type '{field_type}' for field {field_name}")
                return field_value
        else:
            # Direct value
            return field_def
    
    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record"""
        try:
            record_id = await self.xmlid_manager.resolve_xmlid_to_record_id(
                ref_value,
                context=f"field reference in {self.context.get('current_model', 'unknown model')}"
            )
            return record_id
        except Exception as e:
            self.result.add_error(f"Failed to resolve reference {ref_value}: {e}")
            return None
    
    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        try:
            # Handle common cases safely
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]  # String literal
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]  # String literal
            else:
                # Try to evaluate as number
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression  # Return as string
        except Exception:
            return expression
    
    async def _search_record(self, search_expr: str, model: str) -> Optional[str]:
        """Search for a record using search expression"""
        try:
            # Simple search implementation
            # In a full implementation, this would parse the search domain
            # For now, assume it's a simple field=value search
            if '=' in search_expr:
                field, value = search_expr.split('=', 1)
                field = field.strip()
                value = value.strip().strip('"\'')
                
                records = await self.model_sql.search_records(model, {field: value}, limit=1)
                if records:
                    return records[0]['id']
            
            return None
        except Exception as e:
            self.result.add_warning(f"Failed to search for record in {model}: {e}")
            return None
    
    async def _find_record_by_xmlid(self, xml_id: str) -> Optional[str]:
        """Find a record ID by its XML ID"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(xml_id)
        except Exception as e:
            self.logger.debug(f"XML ID {xml_id} not found: {e}")
            return None
    
    async def _create_record(self, model_name: str, values: Dict[str, Any]) -> Optional[str]:
        """Create a new record"""
        try:
            return await self.model_sql.create_record(model_name, values)
        except Exception as e:
            self.result.add_error(f"Failed to create record in {model_name}: {e}")
            return None
    
    async def _update_record(self, model_name: str, record_id: str, values: Dict[str, Any]) -> bool:
        """Update an existing record"""
        try:
            return await self.model_sql.update_record(model_name, record_id, values)
        except Exception as e:
            self.result.add_error(f"Failed to update record {record_id} in {model_name}: {e}")
            return False
    
    async def _store_xmlid_mapping(self, xml_id: str, model_name: str, record_id: str):
        """Store XML ID to record ID mapping"""
        try:
            # Extract module and name from XML ID
            if '.' in xml_id:
                module, name = xml_id.split('.', 1)
            else:
                module = self.context.get('addon_name', 'base')
                name = xml_id
            
            await self.xmlid_manager.create_xmlid_mapping(
                module=module,
                name=name,
                model=model_name,
                res_id=record_id
            )
        except Exception as e:
            self.result.add_warning(f"Failed to store XML ID mapping for {xml_id}: {e}")
