"""
Factory patterns for route handlers and components
Provides decoupled creation of route handlers and related components
"""

import inspect
import importlib
from typing import Any, Callable, Dict, List, Optional, Type
from abc import ABC, abstractmethod

from ..interfaces import IRouteHandler, RouteInfo, RouteScope
from ..metadata import RouteType, AuthType
from ...logging import get_logger

logger = get_logger(__name__)


class IRouteHandlerFactory(ABC):
    """Interface for route handler factories"""
    
    @abstractmethod
    def create_handler(self, route_info: RouteInfo) -> IRouteHandler:
        """Create a route handler for the given route info"""
        pass
    
    @abstractmethod
    def supports_route_type(self, route_type: RouteType) -> bool:
        """Check if factory supports the route type"""
        pass


class BaseRouteHandler(IRouteHandler):
    """Base implementation of route handler"""
    
    def __init__(self, original_handler: Callable, route_info: RouteInfo):
        self.original_handler = original_handler
        self.route_info = route_info
        self.logger = get_logger(f"{__name__}.{original_handler.__name__}")
    
    async def handle_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Default request handling"""
        try:
            # Check if handler is async
            if inspect.iscoroutinefunction(self.original_handler):
                return await self.original_handler(request)
            else:
                return self.original_handler(request)
        except Exception as e:
            self.logger.error(f"Error handling request for {route_info.path}: {e}")
            raise
    
    def supports_route_type(self, route_type: RouteType) -> bool:
        """Base implementation supports HTTP routes"""
        return route_type == RouteType.HTTP


class ControllerRouteHandler(BaseRouteHandler):
    """Route handler for controller-based routes"""
    
    def __init__(self, original_handler: Callable, route_info: RouteInfo, controller_class: Type = None):
        super().__init__(original_handler, route_info)
        self.controller_class = controller_class
        self._controller_instance = None
    
    async def handle_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Handle request with controller instantiation"""
        try:
            # Get or create controller instance
            controller = await self._get_controller_instance()
            
            # Set request context if controller supports it
            if hasattr(controller, '_set_request_context'):
                controller._set_request_context(request)
            
            # Get the method to call
            method_name = self.original_handler.__name__
            bound_method = getattr(controller, method_name)
            
            # Call the method
            if inspect.iscoroutinefunction(bound_method):
                result = await bound_method(request)
            else:
                result = bound_method(request)
            
            # Clear request context
            if hasattr(controller, '_clear_request_context'):
                controller._clear_request_context()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in controller handler for {route_info.path}: {e}")
            raise
    
    async def _get_controller_instance(self):
        """Get or create controller instance"""
        if self._controller_instance is None and self.controller_class:
            self._controller_instance = self.controller_class()
        return self._controller_instance


class JsonRpcRouteHandler(BaseRouteHandler):
    """Route handler for JSON-RPC routes"""
    
    def supports_route_type(self, route_type: RouteType) -> bool:
        """Supports JSON-RPC routes"""
        return route_type == RouteType.JSON
    
    async def handle_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Handle JSON-RPC request"""
        try:
            from .jsonrpc import JsonRpcHandler
            
            # Create JSON-RPC handler
            rpc_handler = JsonRpcHandler()
            
            # Register the method
            method_name = route_info.metadata.get('rpc_method', route_info.path.strip('/'))
            rpc_handler.register_method(method_name, self.original_handler)
            
            # Handle the request
            return await rpc_handler.handle_request(request)
            
        except Exception as e:
            self.logger.error(f"Error in JSON-RPC handler for {route_info.path}: {e}")
            raise


class RouteHandlerFactory(IRouteHandlerFactory):
    """Main factory for creating route handlers"""
    
    def __init__(self):
        self._handler_types: Dict[str, Type[IRouteHandler]] = {
            'base': BaseRouteHandler,
            'controller': ControllerRouteHandler,
            'jsonrpc': JsonRpcRouteHandler
        }
    
    def create_handler(self, route_info: RouteInfo) -> IRouteHandler:
        """Create appropriate handler based on route info"""
        try:
            # Determine handler type
            handler_type = self._determine_handler_type(route_info)
            
            # Create handler
            if handler_type == 'controller':
                controller_class = self._extract_controller_class(route_info)
                return ControllerRouteHandler(route_info.handler, route_info, controller_class)
            elif handler_type == 'jsonrpc':
                return JsonRpcRouteHandler(route_info.handler, route_info)
            else:
                return BaseRouteHandler(route_info.handler, route_info)
                
        except Exception as e:
            logger.error(f"Failed to create handler for {route_info.path}: {e}")
            # Fallback to base handler
            return BaseRouteHandler(route_info.handler, route_info)
    
    def supports_route_type(self, route_type: RouteType) -> bool:
        """Factory supports all route types"""
        return True
    
    def _determine_handler_type(self, route_info: RouteInfo) -> str:
        """Determine the appropriate handler type"""
        # Check route type
        if route_info.route_type == RouteType.JSON:
            return 'jsonrpc'
        
        # Check if it's a controller method
        if self._is_controller_method(route_info):
            return 'controller'
        
        return 'base'
    
    def _is_controller_method(self, route_info: RouteInfo) -> bool:
        """Check if route is a controller method"""
        handler = route_info.handler
        
        # Check if handler has route metadata with controller info
        if hasattr(handler, '_route_metadata'):
            original_func = handler._route_metadata.get('original_func')
            if original_func and hasattr(original_func, '__qualname__'):
                qualname_parts = original_func.__qualname__.split('.')
                return len(qualname_parts) > 1
        
        # Check if handler qualname suggests it's a method
        if hasattr(handler, '__qualname__'):
            qualname_parts = handler.__qualname__.split('.')
            return len(qualname_parts) > 1
        
        return False
    
    def _extract_controller_class(self, route_info: RouteInfo) -> Optional[Type]:
        """Extract controller class from route info"""
        try:
            handler = route_info.handler
            
            # Get original function if available
            original_func = handler
            if hasattr(handler, '_route_metadata'):
                original_func = handler._route_metadata.get('original_func', handler)
            
            if not hasattr(original_func, '__qualname__'):
                return None
            
            qualname_parts = original_func.__qualname__.split('.')
            if len(qualname_parts) < 2:
                return None
            
            # Get module and class
            module = importlib.import_module(original_func.__module__)
            class_name = qualname_parts[-2]
            controller_class = getattr(module, class_name, None)
            
            if controller_class and hasattr(controller_class, '__bases__'):
                # Check if it's a controller
                try:
                    from .controller import Controller
                    if issubclass(controller_class, Controller):
                        return controller_class
                except ImportError:
                    pass
            
            return None
            
        except Exception as e:
            logger.debug(f"Could not extract controller class: {e}")
            return None
    
    def register_handler_type(self, name: str, handler_class: Type[IRouteHandler]):
        """Register a custom handler type"""
        self._handler_types[name] = handler_class
        logger.debug(f"Registered custom handler type: {name}")


class RouteInfoFactory:
    """Factory for creating RouteInfo objects"""
    
    @staticmethod
    def create_route_info(
        path: str,
        handler: Callable,
        route_type: RouteType = RouteType.HTTP,
        auth: AuthType = AuthType.USER,
        methods: Optional[List[str]] = None,
        scope: RouteScope = RouteScope.SYSTEM,
        source: Optional[str] = None,
        **metadata
    ) -> RouteInfo:
        """Create a RouteInfo object with proper defaults"""
        
        if methods is None:
            methods = ['POST'] if route_type == RouteType.JSON else ['GET']
        
        # Normalize methods
        methods = [method.upper() for method in methods]
        
        # Add handler metadata
        if hasattr(handler, '_route_metadata'):
            metadata.update(handler._route_metadata)
        
        return RouteInfo(
            path=path,
            handler=handler,
            methods=methods,
            route_type=route_type,
            auth=auth,
            scope=scope,
            metadata=metadata,
            source=source
        )


# Global factory instances
_route_handler_factory = RouteHandlerFactory()
_route_info_factory = RouteInfoFactory()


def get_route_handler_factory() -> RouteHandlerFactory:
    """Get the global route handler factory"""
    return _route_handler_factory


def get_route_info_factory() -> RouteInfoFactory:
    """Get the global route info factory"""
    return _route_info_factory
