"""
Tests for the refactored modular CLI system
"""
import pytest
import sys
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from erp.cli.manager import ERPCLIManager
from erp.cli.compatibility import CompatibilityArgumentParser
from erp.cli.database import DatabaseCommandGroup, InitCommand, PurgeCommand
from erp.cli.server import ServerCommandGroup, StartCommand
from erp.cli.utils import setup_python_path, validate_database_name


class TestCompatibilityArgumentParser:
    """Test backward compatibility argument parsing"""
    
    def setup_method(self):
        self.parser = CompatibilityArgumentParser()
    
    def test_detect_explicit_commands(self):
        """Test detection of explicit commands"""
        # Test explicit commands
        assert self.parser.detect_command_from_args(['init', 'mydb']) == ('init', ['init', 'mydb'])
        assert self.parser.detect_command_from_args(['start', '--db', 'mydb']) == ('start', ['start', '--db', 'mydb'])
        assert self.parser.detect_command_from_args(['purge', '--force']) == ('purge', ['purge', '--force'])
    
    def test_detect_legacy_init_pattern(self):
        """Test detection of legacy init patterns"""
        # Legacy init patterns
        command, args = self.parser.detect_command_from_args(['mydb', '--force'])
        assert command == 'init'
        
        command, args = self.parser.detect_command_from_args(['testdb', '--demo'])
        assert command == 'init'
    
    def test_detect_legacy_start_pattern(self):
        """Test detection of legacy start patterns"""
        # Legacy start patterns
        command, args = self.parser.detect_command_from_args(['--db', 'mydb'])
        assert command == 'start'
        assert args == ['start', '--db', 'mydb']
        
        command, args = self.parser.detect_command_from_args(['--host', '0.0.0.0'])
        assert command == 'start'
        assert args == ['start', '--host', '0.0.0.0']
    
    def test_detect_default_start(self):
        """Test default to start command"""
        # Empty args should default to start
        command, args = self.parser.detect_command_from_args([])
        assert command == 'start'
        
        # Unknown patterns should default to start
        command, args = self.parser.detect_command_from_args(['--unknown'])
        assert command == 'start'
        assert args == ['start', '--unknown']
    
    def test_preprocess_args(self):
        """Test argument preprocessing"""
        # Test various preprocessing scenarios
        assert self.parser.preprocess_args([]) == ['start']
        assert self.parser.preprocess_args(['init', 'mydb']) == ['init', 'mydb']
        assert self.parser.preprocess_args(['--db', 'mydb']) == ['start', '--db', 'mydb']
    
    def test_is_legacy_usage(self):
        """Test legacy usage detection"""
        assert self.parser.is_legacy_usage([]) == True
        assert self.parser.is_legacy_usage(['--db', 'mydb']) == True
        assert self.parser.is_legacy_usage(['mydb', '--force']) == True
        assert self.parser.is_legacy_usage(['init', 'mydb']) == False
        assert self.parser.is_legacy_usage(['start']) == False


class TestDatabaseCommands:
    """Test database command functionality"""
    
    def setup_method(self):
        self.command_group = DatabaseCommandGroup()
    
    def test_command_registration(self):
        """Test that commands are properly registered"""
        assert 'init' in self.command_group.commands
        assert 'purge' in self.command_group.commands
        assert isinstance(self.command_group.commands['init'], InitCommand)
        assert isinstance(self.command_group.commands['purge'], PurgeCommand)
    
    @patch('erp.cli.database.asyncio.run')
    @patch('erp.cli.database.DatabaseRegistry')
    def test_init_command_basic(self, mock_registry, mock_asyncio_run):
        """Test basic init command functionality"""
        mock_asyncio_run.return_value = 0
        
        init_command = self.command_group.commands['init']
        
        # Mock arguments
        args = Mock()
        args.db_name = 'test_db'
        args.force = False
        args.demo = False
        args.no_create = False
        args.exit = True
        args.no_http = False
        args.verbose = False
        
        result = init_command.handle(args)
        assert mock_asyncio_run.called
    
    @patch('erp.cli.database.asyncio.run')
    def test_purge_command_basic(self, mock_asyncio_run):
        """Test basic purge command functionality"""
        mock_asyncio_run.return_value = 0
        
        purge_command = self.command_group.commands['purge']
        
        # Mock arguments
        args = Mock()
        args.force = True
        args.verbose = False
        
        result = purge_command.handle(args)
        assert mock_asyncio_run.called


class TestServerCommands:
    """Test server command functionality"""
    
    def setup_method(self):
        self.command_group = ServerCommandGroup()
    
    def test_command_registration(self):
        """Test that commands are properly registered"""
        assert 'start' in self.command_group.commands
        assert isinstance(self.command_group.commands['start'], StartCommand)
    
    @patch('erp.cli.server.asyncio.run')
    def test_start_command_basic(self, mock_asyncio_run):
        """Test basic start command functionality"""
        mock_asyncio_run.return_value = True
        
        start_command = self.command_group.commands['start']
        
        # Mock arguments
        args = Mock()
        args.db_name = None
        args.host = None
        args.port = None
        args.reload = False
        args.verbose = False
        
        result = start_command.handle(args)
        assert mock_asyncio_run.called
        assert result == 0


class TestERPCLIManager:
    """Test the main CLI manager"""
    
    def setup_method(self):
        self.manager = ERPCLIManager()
    
    def test_command_groups_registered(self):
        """Test that all command groups are registered"""
        assert 'database' in self.manager.command_groups
        assert 'server' in self.manager.command_groups
        assert isinstance(self.manager.command_groups['database'], DatabaseCommandGroup)
        assert isinstance(self.manager.command_groups['server'], ServerCommandGroup)
    
    def test_command_mapping(self):
        """Test command to group mapping"""
        # Test that commands map to correct groups
        args = Mock()
        args.command = 'init'
        
        # This would normally call the actual command, but we're just testing the mapping
        with patch.object(self.manager.command_groups['database'], 'handle_command') as mock_handle:
            mock_handle.return_value = 0
            result = self.manager._handle_command(args)
            mock_handle.assert_called_once_with('init', args)
    
    @patch('erp.cli.manager.setup_python_path')
    @patch('erp.cli.manager.setup_logging')
    @patch('erp.cli.manager.print_system_info')
    def test_run_with_args(self, mock_print_info, mock_setup_logging, mock_setup_path):
        """Test running CLI with arguments"""
        mock_setup_logging.return_value = Mock()
        
        with patch.object(self.manager, '_handle_command') as mock_handle:
            mock_handle.return_value = 0
            result = self.manager.run(['init', 'test_db', '--exit'])
            assert result == 0


class TestUtilities:
    """Test utility functions"""
    
    def test_setup_python_path(self):
        """Test Python path setup"""
        original_path = sys.path.copy()
        setup_python_path()
        # Path should be modified (exact test depends on current directory structure)
        assert len(sys.path) >= len(original_path)
    
    def test_validate_database_name(self):
        """Test database name validation"""
        # Valid names
        assert validate_database_name('mydb') == True
        assert validate_database_name('test_db') == True
        assert validate_database_name('db123') == True
        
        # Invalid names
        assert validate_database_name('') == False
        assert validate_database_name('123db') == False  # Can't start with number
        assert validate_database_name('my-db') == False  # No hyphens
        assert validate_database_name('my.db') == False  # No dots


class TestIntegration:
    """Integration tests for the complete CLI system"""
    
    @patch('erp.cli.manager.setup_python_path')
    @patch('erp.cli.manager.setup_logging')
    @patch('erp.cli.manager.print_system_info')
    @patch('erp.cli.database.asyncio.run')
    def test_legacy_init_pattern(self, mock_asyncio, mock_print_info, mock_setup_logging, mock_setup_path):
        """Test that legacy init patterns work correctly"""
        mock_setup_logging.return_value = Mock()
        mock_asyncio.return_value = 0
        
        manager = ERPCLIManager()
        
        # Test legacy pattern: python erp-bin mydb --force
        result = manager.run(['mydb', '--force', '--exit'])
        
        # Should succeed (exact behavior depends on mocked components)
        assert result in [0, 1]  # Allow for both success and controlled failure
    
    @patch('erp.cli.manager.setup_python_path')
    @patch('erp.cli.manager.setup_logging')
    @patch('erp.cli.manager.print_system_info')
    @patch('erp.cli.server.asyncio.run')
    def test_legacy_start_pattern(self, mock_asyncio, mock_print_info, mock_setup_logging, mock_setup_path):
        """Test that legacy start patterns work correctly"""
        mock_setup_logging.return_value = Mock()
        mock_asyncio.return_value = True
        
        manager = ERPCLIManager()
        
        # Test legacy pattern: python erp-bin --db mydb
        result = manager.run(['--db', 'mydb'])
        
        # Should succeed (exact behavior depends on mocked components)
        assert result in [0, 1]  # Allow for both success and controlled failure


if __name__ == '__main__':
    pytest.main([__file__])
