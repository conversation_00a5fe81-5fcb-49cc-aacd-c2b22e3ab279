"""
CRUD (Create, Read, Update, Delete) database operations
"""
import time
import asyncpg
from typing import Dict, Any, Optional, List
from ..connection.pool import ConnectionPool
from ..connection.sql_logger import SQLLogger


class CRUDOperations:
    """CRUD operations for database records"""

    def __init__(self, connection_pool: ConnectionPool, sql_logger: SQLLogger):
        self.pool = connection_pool
        self.sql_logger = sql_logger

    async def execute(self, query: str, *args) -> str:
        """Execute a query and return status"""
        start_time = time.perf_counter()

        async with self.pool.acquire_connection() as conn:
            result = await conn.execute(query, *args)

        duration = time.perf_counter() - start_time
        self.sql_logger.log_query(query, args, duration, f"Status: {result}")

        return result

    async def fetch(self, query: str, *args) -> List[asyncpg.Record]:
        """Fetch multiple rows"""
        start_time = time.perf_counter()

        async with self.pool.acquire_connection() as conn:
            result = await conn.fetch(query, *args)

        duration = time.perf_counter() - start_time
        self.sql_logger.log_query(query, args, duration, f"Rows: {len(result)}")

        return result

    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """Fetch single row"""
        start_time = time.perf_counter()

        async with self.pool.acquire_connection() as conn:
            result = await conn.fetchrow(query, *args)

        duration = time.perf_counter() - start_time
        self.sql_logger.log_query(query, args, duration, f"Row: {'Found' if result else 'None'}")

        return result

    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        start_time = time.perf_counter()

        async with self.pool.acquire_connection() as conn:
            result = await conn.fetchval(query, *args)

        duration = time.perf_counter() - start_time
        self.sql_logger.log_query(query, args, duration, f"Value: {result}")

        return result

    async def insert(self, table: str, data: Dict[str, Any]) -> Optional[str]:
        """Insert a record and return the ID"""
        if not data:
            self.sql_logger.logger.debug(f"Insert skipped for table {table}: no data provided")
            return None

        columns = list(data.keys())
        values = list(data.values())
        placeholders = [f"${i+1}" for i in range(len(values))]

        query = f"""
            INSERT INTO {table} ({', '.join(columns)})
            VALUES ({', '.join(placeholders)})
            RETURNING id
        """

        start_time = time.perf_counter()
        result = await self.fetchval(query, *values)
        duration = time.perf_counter() - start_time

        result_id = str(result) if result else None
        self.sql_logger.log_query(query, values, duration, f"Inserted ID: {result_id}")

        return result_id

    async def update(self, table: str, record_id: str, data: Dict[str, Any]) -> bool:
        """Update a record"""
        if not data:
            return False
        
        set_clauses = []
        values = []
        
        for i, (column, value) in enumerate(data.items(), 1):
            set_clauses.append(f"{column} = ${i}")
            values.append(value)
        
        query = f"""
            UPDATE {table}
            SET {', '.join(set_clauses)}
            WHERE id = ${len(values) + 1}
        """
        values.append(record_id)
        
        result = await self.execute(query, *values)
        return result.split()[-1] == '1'  # Check if one row was updated

    async def delete(self, table: str, record_id: str) -> bool:
        """Delete a record"""
        query = f"DELETE FROM {table} WHERE id = $1"
        result = await self.execute(query, record_id)
        return result.split()[-1] == '1'  # Check if one row was deleted

    async def exists(self, table: str, record_id: str) -> bool:
        """Check if a record exists"""
        query = f"SELECT 1 FROM {table} WHERE id = $1 LIMIT 1"
        result = await self.fetchval(query, record_id)
        return result is not None

    async def count(self, table: str, where_clause: str = "", params: List = None) -> int:
        """Count records in table"""
        query = f"SELECT COUNT(*) FROM {table}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        params = params or []
        result = await self.fetchval(query, *params)
        return result or 0
