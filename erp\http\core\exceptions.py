"""
HTTP-specific exceptions
"""


class HTTPError(Exception):
    """Base HTTP error"""
    def __init__(self, message: str, status_code: int = 500):
        super().__init__(message)
        self.status_code = status_code


class RouteError(HTTPError):
    """Route-related error"""
    pass


class AuthenticationError(HTTPError):
    """Authentication error"""
    def __init__(self, message: str = "Authentication required"):
        super().__init__(message, 401)


class AuthorizationError(HTTPError):
    """Authorization error"""
    def __init__(self, message: str = "Access denied"):
        super().__init__(message, 403)


class ValidationError(HTTPError):
    """Validation error"""
    def __init__(self, message: str = "Validation failed"):
        super().__init__(message, 400)
