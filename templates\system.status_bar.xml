<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_status_bar" xml:space="preserve">

    <!-- Enhanced Status Bar Component -->
    <t t-name="system.status_bar">
        <div class="bg-white border border-gray-200 rounded-xl p-6 mb-8 shadow-sm">
            <!-- Top Row: System Status and Search -->
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <div t-att-class="'inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold shadow-sm ' + ('bg-green-100 text-green-800 border border-green-200' if config.is_multi_db_mode else 'bg-yellow-100 text-yellow-800 border border-yellow-200')">
                        <i t-att-class="'fas mr-2 ' + ('fa-layer-group' if config.is_multi_db_mode else 'fa-database')"></i>
                        <span t-esc="'Multi-Database Mode' if config.is_multi_db_mode else 'Single-Database Mode'"/>
                    </div>
                    <t t-if="config.db_filter">
                        <div class="inline-flex items-center px-3 py-2 bg-blue-50 text-blue-800 rounded-lg text-sm font-medium border border-blue-200">
                            <i class="fas fa-filter mr-2"></i>
                            <span>Filter: </span>
                            <code class="ml-1 bg-blue-100 px-2 py-1 rounded text-xs" t-esc="config.db_filter"/>
                        </div>
                    </t>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="Search databases..."
                               id="database-search" onkeyup="filterDatabases()"/>
                    </div>
                </div>
            </div>

            <!-- Bottom Row: Database Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-100">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-database text-blue-600"></i>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-gray-900" t-esc="len(databases)"/>
                        <div class="text-sm text-gray-500">Total Databases</div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600"></i>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-gray-900" t-esc="len([db for db in databases if db['init_status'] == 'ready'])"/>
                        <div class="text-sm text-gray-500">Ready</div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-memory text-purple-600"></i>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-gray-900" t-esc="len([db for db in databases if db['has_memory_registry']])"/>
                        <div class="text-sm text-gray-500">With Registry</div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-plug text-orange-600"></i>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-gray-900" t-esc="sum(db['active_connections'] for db in databases)"/>
                        <div class="text-sm text-gray-500">Active Connections</div>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
