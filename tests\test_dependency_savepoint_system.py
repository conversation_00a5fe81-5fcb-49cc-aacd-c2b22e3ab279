"""
Tests for the enhanced dependency-aware savepoint system

This module tests the new savepoint functionality that ensures:
1. Each addon gets its own savepoint
2. Dependencies are installed with their own savepoints
3. Dependency savepoints are committed when dependencies succeed
4. Main addon failures only rollback the main addon, not dependencies
5. Proper cleanup and rollback behavior
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch

from erp.addons.managers.dependency_manager import DependencyManager
from erp.addons.managers.savepoint_manager import SavepointManager, SavepointState
from erp.addons.managers.addon_manager import AddonManager
from erp.addons.managers.addon_state import AddonStateManager, AddonInfo, AddonState
from erp.addons.manifest import AddonManifest
from erp.addons.exceptions import CircularDependencyError, MissingDependencyError


class TestDependencyResolution:
    """Test enhanced dependency resolution"""
    
    def setup_method(self):
        """Setup test environment"""
        self.state_manager = AddonStateManager()
        self.dependency_manager = DependencyManager(self.state_manager)
        
        # Create test addons with dependencies
        # A depends on B and C
        # B depends on D
        # C has no dependencies
        # D has no dependencies
        self.setup_test_addons()
    
    def setup_test_addons(self):
        """Setup test addon hierarchy"""
        # Create mock manifests
        manifest_a = Mock(spec=AddonManifest)
        manifest_a.depends = ['B', 'C']
        manifest_a.installable = True
        
        manifest_b = Mock(spec=AddonManifest)
        manifest_b.depends = ['D']
        manifest_b.installable = True
        
        manifest_c = Mock(spec=AddonManifest)
        manifest_c.depends = []
        manifest_c.installable = True
        
        manifest_d = Mock(spec=AddonManifest)
        manifest_d.depends = []
        manifest_d.installable = True
        
        # Create addon infos
        addon_a = AddonInfo('A', manifest_a, AddonState.UNINSTALLED, available_version='1.0.0')
        addon_b = AddonInfo('B', manifest_b, AddonState.UNINSTALLED, available_version='1.0.0')
        addon_c = AddonInfo('C', manifest_c, AddonState.UNINSTALLED, available_version='1.0.0')
        addon_d = AddonInfo('D', manifest_d, AddonState.UNINSTALLED, available_version='1.0.0')
        
        # Add to state manager
        self.state_manager._addon_states = {
            'A': addon_a,
            'B': addon_b,
            'C': addon_c,
            'D': addon_d
        }
        
        # Build dependency graphs
        self.dependency_manager.build_dependency_graphs()
    
    def test_enhanced_dependency_resolution(self):
        """Test that dependencies are resolved correctly"""
        plan = self.dependency_manager.get_enhanced_install_order(['A'])
        
        # Should include A and all its dependencies
        assert set(plan.installation_order) == {'A', 'B', 'C', 'D'}
        
        # A should be explicit, others should be dependencies
        assert plan.explicit_addons == {'A'}
        assert plan.dependency_addons == {'B', 'C', 'D'}
        
        # Installation order should respect dependencies
        # D should come before B, B and C should come before A
        order = plan.installation_order
        assert order.index('D') < order.index('B')
        assert order.index('B') < order.index('A')
        assert order.index('C') < order.index('A')
    
    def test_dependency_levels(self):
        """Test dependency level calculation"""
        levels = self.dependency_manager.get_dependency_levels(['A'])
        
        # Level 0: D, C (no dependencies)
        # Level 1: B (depends on D)
        # Level 2: A (depends on B and C)
        assert 'D' in levels[0]
        assert 'C' in levels[0]
        assert 'B' in levels[1]
        assert 'A' in levels[2]
    
    def test_circular_dependency_detection(self):
        """Test circular dependency detection"""
        # Add circular dependency: D depends on A
        self.state_manager._addon_states['D'].dependencies = ['A']
        self.dependency_manager.build_dependency_graphs()
        
        with pytest.raises(CircularDependencyError):
            self.dependency_manager.get_enhanced_install_order(['A'])
    
    def test_missing_dependency_detection(self):
        """Test missing dependency detection"""
        # Add missing dependency
        self.state_manager._addon_states['A'].dependencies = ['B', 'C', 'MISSING']
        self.dependency_manager.build_dependency_graphs()
        
        with pytest.raises(MissingDependencyError):
            self.dependency_manager.get_enhanced_install_order(['A'])
    
    def test_installation_order_validation(self):
        """Test installation order validation"""
        # Valid order
        valid_order = ['D', 'C', 'B', 'A']
        is_valid, violations = self.dependency_manager.validate_installation_order(valid_order)
        assert is_valid
        assert len(violations) == 0
        
        # Invalid order (A before B)
        invalid_order = ['D', 'C', 'A', 'B']
        is_valid, violations = self.dependency_manager.validate_installation_order(invalid_order)
        assert not is_valid
        assert len(violations) > 0


class TestSavepointManager:
    """Test savepoint management functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.savepoint_manager = SavepointManager()
        self.mock_env = Mock()
        self.mock_env.cr = AsyncMock()
    
    def test_savepoint_creation(self):
        """Test savepoint creation"""
        savepoint_name = self.savepoint_manager.create_savepoint('test_addon', is_dependency=False)
        
        assert savepoint_name.startswith('addon_install_test_addon_')
        
        # Check savepoint info
        info = self.savepoint_manager.get_savepoint_info('test_addon')
        assert info is not None
        assert info.addon_name == 'test_addon'
        assert info.is_dependency == False
        assert info.state == SavepointState.CREATED
    
    @pytest.mark.asyncio
    async def test_successful_dependency_installation(self):
        """Test successful dependency installation with immediate commit"""
        async def mock_install():
            return True
        
        success = await self.savepoint_manager.execute_with_savepoint(
            'dep_addon', self.mock_env, mock_install, is_dependency=True
        )
        
        assert success
        
        # Verify savepoint was created and committed
        self.mock_env.cr.savepoint.assert_called_once()
        self.mock_env.cr.release_savepoint.assert_called_once()
        
        # Check savepoint state
        info = self.savepoint_manager.get_savepoint_info('dep_addon')
        assert info.state == SavepointState.COMMITTED
    
    @pytest.mark.asyncio
    async def test_failed_installation_rollback(self):
        """Test failed installation with rollback"""
        async def mock_install():
            raise Exception("Installation failed")
        
        success = await self.savepoint_manager.execute_with_savepoint(
            'failing_addon', self.mock_env, mock_install, is_dependency=False
        )
        
        assert not success
        
        # Verify rollback was called
        self.mock_env.cr.rollback_to_savepoint.assert_called_once()
        self.mock_env.cr.release_savepoint.assert_called_once()
        
        # Check savepoint state
        info = self.savepoint_manager.get_savepoint_info('failing_addon')
        assert info.state == SavepointState.ROLLED_BACK
    
    def test_installation_summary(self):
        """Test installation summary generation"""
        # Create some savepoints with different states
        self.savepoint_manager.create_savepoint('committed_addon')
        self.savepoint_manager.create_savepoint('rolled_back_addon')
        self.savepoint_manager.create_savepoint('pending_addon')
        
        # Manually set states for testing
        self.savepoint_manager._savepoints[
            self.savepoint_manager._addon_savepoints['committed_addon']
        ].state = SavepointState.COMMITTED
        
        self.savepoint_manager._savepoints[
            self.savepoint_manager._addon_savepoints['rolled_back_addon']
        ].state = SavepointState.ROLLED_BACK
        
        summary = self.savepoint_manager.get_installation_summary()
        
        assert 'committed_addon' in summary['committed']
        assert 'rolled_back_addon' in summary['rolled_back']
        assert 'pending_addon' in summary['pending']


class TestIntegratedInstallation:
    """Test integrated installation with dependency-aware savepoints"""
    
    def setup_method(self):
        """Setup test environment"""
        self.addon_manager = AddonManager()
        
        # Mock the installers
        self.addon_manager.installer = AsyncMock()
        self.addon_manager.installer.install_addon = AsyncMock(return_value=True)
        
        self.addon_manager.base_installer = AsyncMock()
        self.addon_manager.base_installer.install_base_module = AsyncMock(return_value=True)
        
        # Setup test addons
        self.setup_test_addons()
    
    def setup_test_addons(self):
        """Setup test addon hierarchy for integration tests"""
        # Similar to TestDependencyResolution but with real AddonManager
        manifest_a = Mock(spec=AddonManifest)
        manifest_a.depends = ['B', 'C']
        manifest_a.installable = True
        
        manifest_b = Mock(spec=AddonManifest)
        manifest_b.depends = ['D']
        manifest_b.installable = True
        
        manifest_c = Mock(spec=AddonManifest)
        manifest_c.depends = []
        manifest_c.installable = True
        
        manifest_d = Mock(spec=AddonManifest)
        manifest_d.depends = []
        manifest_d.installable = True
        
        # Create addon infos
        addon_a = AddonInfo('A', manifest_a, AddonState.UNINSTALLED, available_version='1.0.0')
        addon_b = AddonInfo('B', manifest_b, AddonState.UNINSTALLED, available_version='1.0.0')
        addon_c = AddonInfo('C', manifest_c, AddonState.UNINSTALLED, available_version='1.0.0')
        addon_d = AddonInfo('D', manifest_d, AddonState.UNINSTALLED, available_version='1.0.0')
        
        # Add to state manager
        self.addon_manager.state_manager._addon_states = {
            'A': addon_a,
            'B': addon_b,
            'C': addon_c,
            'D': addon_d
        }
        
        # Build dependency graphs
        self.addon_manager.dependency_manager.build_dependency_graphs()
    
    @pytest.mark.asyncio
    async def test_successful_installation_with_dependencies(self):
        """Test successful installation of addon with dependencies"""
        # Mock environment
        mock_env = Mock()
        mock_env.cr = AsyncMock()
        
        # Mock importlib to avoid actual imports
        with patch('importlib.import_module'):
            success = await self.addon_manager.install_addon('A', env=mock_env)
        
        assert success
        
        # Verify all addons are marked as installed
        assert self.addon_manager.state_manager.get_addon_info('A').state == AddonState.INSTALLED
        assert self.addon_manager.state_manager.get_addon_info('B').state == AddonState.INSTALLED
        assert self.addon_manager.state_manager.get_addon_info('C').state == AddonState.INSTALLED
        assert self.addon_manager.state_manager.get_addon_info('D').state == AddonState.INSTALLED
        
        # Verify dependency tracking
        assert self.addon_manager.state_manager.get_addon_info('A').installation_reason == 'explicit'
        assert self.addon_manager.state_manager.get_addon_info('B').installation_reason == 'dependency'
        assert self.addon_manager.state_manager.get_addon_info('C').installation_reason == 'dependency'
        assert self.addon_manager.state_manager.get_addon_info('D').installation_reason == 'dependency'
    
    @pytest.mark.asyncio
    async def test_partial_failure_preserves_dependencies(self):
        """Test that dependency installations are preserved when main addon fails"""
        # Mock environment
        mock_env = Mock()
        mock_env.cr = AsyncMock()
        
        # Make addon A fail but dependencies succeed
        def mock_install_side_effect(addon_name, env):
            if addon_name == 'A':
                raise Exception("Main addon installation failed")
            return True
        
        self.addon_manager.installer.install_addon.side_effect = mock_install_side_effect
        
        with patch('importlib.import_module'):
            success = await self.addon_manager.install_addon('A', env=mock_env)
        
        assert not success
        
        # Dependencies should still be installed
        assert self.addon_manager.state_manager.get_addon_info('B').state == AddonState.INSTALLED
        assert self.addon_manager.state_manager.get_addon_info('C').state == AddonState.INSTALLED
        assert self.addon_manager.state_manager.get_addon_info('D').state == AddonState.INSTALLED
        
        # Main addon should be broken
        assert self.addon_manager.state_manager.get_addon_info('A').state == AddonState.BROKEN


if __name__ == '__main__':
    pytest.main([__file__])
