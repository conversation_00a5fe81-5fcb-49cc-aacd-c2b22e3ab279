"""
Test suite for database manager functionality

This module tests:
- DatabaseManager initialization and configuration
- Connection pool management
- CRUD operation delegation
- Transaction handling
- Schema management operations
"""
import pytest
from unittest.mock import patch

from erp.database import DatabaseManager


class TestDatabaseManager:
    """Test DatabaseManager functionality"""
    
    def test_database_manager_initialization(self):
        """Test DatabaseManager initialization"""
        manager = DatabaseManager("test_db")
        
        assert manager.db_name == "test_db"
        assert hasattr(manager, 'pool')
        assert hasattr(manager, 'sql_logger')
        assert hasattr(manager, 'transactions')
        assert hasattr(manager, 'crud')
        assert hasattr(manager, 'schema')
    
    @pytest.mark.asyncio
    async def test_database_manager_pool_operations(self):
        """Test pool creation and closing"""
        manager = DatabaseManager("test_db")
        
        with patch.object(manager.pool, 'create_pool') as mock_create:
            with patch.object(manager.pool, 'close_pool') as mock_close:
                await manager.create_pool()
                mock_create.assert_called_once()
                
                await manager.close_pool()
                mock_close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_database_manager_crud_delegation(self):
        """Test that CRUD operations are properly delegated"""
        manager = DatabaseManager("test_db")
        
        with patch.object(manager.crud, 'execute') as mock_execute:
            with patch.object(manager.crud, 'fetch') as mock_fetch:
                with patch.object(manager.crud, 'fetchrow') as mock_fetchrow:
                    with patch.object(manager.crud, 'fetchval') as mock_fetchval:
                        
                        await manager.execute("SELECT 1")
                        mock_execute.assert_called_once_with("SELECT 1")
                        
                        await manager.fetch("SELECT * FROM test")
                        mock_fetch.assert_called_once_with("SELECT * FROM test")
                        
                        await manager.fetchrow("SELECT * FROM test LIMIT 1")
                        mock_fetchrow.assert_called_once_with("SELECT * FROM test LIMIT 1")
                        
                        await manager.fetchval("SELECT COUNT(*) FROM test")
                        mock_fetchval.assert_called_once_with("SELECT COUNT(*) FROM test")
