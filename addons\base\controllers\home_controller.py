"""
Home controller for base addon
Handles /home route with current context information JSON response
"""
import time
from datetime import datetime
from fastapi.responses import JSONResponse
from erp.http import route, RouteType, AuthType, Controller
from erp.logging import get_logger
from erp.database.memory import MemoryRegistryManager

logger = get_logger(__name__)


class HomeController(Controller):
    """Home controller that returns current context information"""

    @route('/home', type=RouteType.HTTP, auth=AuthType.PUBLIC, methods=['GET'])
    async def home(self, request):
        """
        Home route that returns current context information only
        """
        try:
            # Get current environment from self.env
            current_env = self.env

            # Get current database from environment
            current_db = self.env.cr.db_name if self.env.cr else None

            # Get current user from environment
            current_user = self.env.uid

            # Get context variables from environment
            context_vars = self.env.context

            # Get current database connection details
            db_connection_info = await self._get_current_db_connection_info(current_env)

            # Get current memory registry details
            registry_info = await self._get_current_registry_info(current_env)

            return JSONResponse(content={
                "status": "success",
                "message": "ERP System Home",
                "timestamp": datetime.now().isoformat(),
                "current_environment": self._format_environment_info(current_env),
                "current_database": current_db,
                "current_user": current_user,
                "context_vars": context_vars,
                "database_connection": db_connection_info,
                "memory_registry": registry_info
            })

        except Exception as e:
            logger.error(f"Error getting context information: {e}")
            return JSONResponse(content={
                "status": "error",
                "message": f"Failed to get context information: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }, status_code=500)

    def _format_environment_info(self, env):
        """Format environment information"""
        if not env:
            return None

        try:
            return {
                "uuid": env.uuid,
                "database": env.cr.db_name if env.cr else None,
                "user_id": env.uid,
                "context": env.context,
                "has_memory_registry": env.memory_registry is not None,
                "models_loaded": len(env._models) if hasattr(env, '_models') else 0
            }
        except Exception as e:
            logger.error(f"Error formatting environment info: {e}")
            return {"error": str(e)}

    async def _get_current_db_connection_info(self, env):
        """Get current database connection information"""
        if not env or not env.cr:
            return None

        try:
            db_name = env.cr.db_name

            # Get database manager from registry
            from erp.database.registry import DatabaseRegistry
            db_manager = None
            try:
                db_manager = await DatabaseRegistry.get_database(db_name)
            except Exception as db_error:
                return {
                    "database_name": db_name,
                    "error": f"Could not get database manager: {str(db_error)}",
                    "cursor_active": True if env.cr else False
                }

            # Get connection pool info if available
            pool_info = {}
            if db_manager and hasattr(db_manager, 'pool') and db_manager.pool:
                try:
                    pool_info = {
                        "pool_size": db_manager.pool.get_size() if hasattr(db_manager.pool, 'get_size') else "unknown",
                        "pool_max_size": db_manager.pool.get_max_size() if hasattr(db_manager.pool, 'get_max_size') else "unknown",
                        "pool_min_size": db_manager.pool.get_min_size() if hasattr(db_manager.pool, 'get_min_size') else "unknown"
                    }
                except Exception as pool_error:
                    pool_info = {"error": str(pool_error)}

            return {
                "database_name": db_name,
                "connection_pool": pool_info,
                "cursor_active": True if env.cr else False
            }
        except Exception as e:
            logger.error(f"Error getting database connection info: {e}")
            return {"error": str(e)}

    async def _get_current_registry_info(self, env):
        """Get current memory registry information"""
        if not env or not env.cr:
            return None

        try:
            db_name = env.cr.db_name

            # Try to get memory registry for current database
            registry = await MemoryRegistryManager.get_registry(db_name)
            if not registry:
                return {"status": "no_registry_found"}

            # Get registry stats
            stats = registry.get_stats()

            # Get installed modules
            installed_modules = await registry.get_installed_modules()

            # Get routes count
            routes = await registry.get_routes()

            return {
                "database": db_name,
                "stats": stats,
                "installed_modules_count": len(installed_modules),
                "routes_count": len(routes),
                "active_environments": registry.get_active_environments_count(),
                "uptime_seconds": time.time() - registry.created_at,
                "created_at": datetime.fromtimestamp(registry.created_at).isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting registry info: {e}")
            return {"error": str(e)}