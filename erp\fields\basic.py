"""
Basic field types for ERP models
"""
from datetime import datetime, date
from decimal import Decimal
from .base import Field, FieldValidationError


class Char(Field):
    """Character field"""

    def __init__(self, size=None, translate=False, **kwargs):
        super().__init__(**kwargs)
        self.size = size
        self.translate = translate

    def get_sql_type(self):
        if self.size:
            return f"VARCHAR({self.size})"
        return "TEXT"

    def _validate_value(self, value):
        """Validate character field value"""
        if not isinstance(value, str):
            try:
                value = str(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to string")

        # Check for required field with empty string
        if self.required and value == "":
            raise FieldValidationError(f"Field '{self.string or 'unknown'}' is required")

        if self.size and len(value) > self.size:
            raise FieldValidationError(f"String too long: {len(value)} > {self.size}")

        return value


class Text(Field):
    """Text field for longer content"""

    def __init__(self, translate=False, **kwargs):
        super().__init__(**kwargs)
        self.translate = translate

    def get_sql_type(self):
        return "TEXT"

    def _validate_value(self, value):
        """Validate text field value"""
        if not isinstance(value, str):
            try:
                value = str(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to string")
        return value


class Integer(Field):
    """Integer field"""

    def __init__(self, min_value=None, max_value=None, **kwargs):
        super().__init__(**kwargs)
        self.min_value = min_value
        self.max_value = max_value

    def get_sql_type(self):
        return "INTEGER"

    def _validate_value(self, value):
        """Validate integer field value"""
        if not isinstance(value, int):
            try:
                value = int(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to integer")

        if self.min_value is not None and value < self.min_value:
            raise FieldValidationError(f"Value {value} is less than minimum {self.min_value}")

        if self.max_value is not None and value > self.max_value:
            raise FieldValidationError(f"Value {value} is greater than maximum {self.max_value}")

        return value


class Float(Field):
    """Float field"""

    def __init__(self, digits=None, min_value=None, max_value=None, **kwargs):
        super().__init__(**kwargs)
        self.digits = digits  # (precision, scale) tuple
        self.min_value = min_value
        self.max_value = max_value

    def get_sql_type(self):
        if self.digits:
            precision, scale = self.digits
            return f"NUMERIC({precision},{scale})"
        return "REAL"

    def _validate_value(self, value):
        """Validate float field value"""
        if not isinstance(value, (int, float, Decimal)):
            try:
                value = float(value)
            except (ValueError, TypeError):
                raise FieldValidationError(f"Cannot convert {type(value).__name__} to float")

        if self.min_value is not None and value < self.min_value:
            raise FieldValidationError(f"Value {value} is less than minimum {self.min_value}")

        if self.max_value is not None and value > self.max_value:
            raise FieldValidationError(f"Value {value} is greater than maximum {self.max_value}")

        # Apply precision if specified
        if self.digits:
            _, scale = self.digits
            value = round(float(value), scale)

        return value


class Boolean(Field):
    """Boolean field"""

    def get_sql_type(self):
        return "BOOLEAN"

    def _validate_value(self, value):
        """Validate boolean field value"""
        if isinstance(value, bool):
            return value

        # Convert common truthy/falsy values
        if isinstance(value, str):
            value_lower = value.lower()
            if value_lower in ('true', '1', 'yes', 'on'):
                return True
            elif value_lower in ('false', '0', 'no', 'off', ''):
                return False

        # Convert numeric values
        if isinstance(value, (int, float)):
            return bool(value)

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to boolean")


class Date(Field):
    """Date field"""

    def get_sql_type(self):
        return "DATE"

    def _validate_value(self, value):
        """Validate date field value"""
        if isinstance(value, datetime):
            return value.date()

        if isinstance(value, date):
            return value

        if isinstance(value, str):
            try:
                # Try common date formats
                for fmt in ('%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S'):
                    try:
                        parsed = datetime.strptime(value, fmt)
                        return parsed.date()
                    except ValueError:
                        continue
                raise ValueError("No matching date format found")
            except ValueError:
                raise FieldValidationError(f"Cannot parse date from string: {value}")

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to date")


class Datetime(Field):
    """Datetime field"""

    def __init__(self, **kwargs):
        if 'default' not in kwargs:
            kwargs['default'] = lambda: datetime.now()
        super().__init__(**kwargs)

    def get_sql_type(self):
        return "TIMESTAMP"

    def _validate_value(self, value):
        """Validate datetime field value"""
        if isinstance(value, datetime):
            return value

        if isinstance(value, date):
            return datetime.combine(value, datetime.min.time())

        if isinstance(value, str):
            try:
                # Try common datetime formats
                for fmt in ('%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f',
                           '%Y-%m-%d', '%d/%m/%Y %H:%M:%S', '%m/%d/%Y %H:%M:%S'):
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue
                raise ValueError("No matching datetime format found")
            except ValueError:
                raise FieldValidationError(f"Cannot parse datetime from string: {value}")

        raise FieldValidationError(f"Cannot convert {type(value).__name__} to datetime")


class Selection(Field):
    """Selection field"""

    def __init__(self, selection, **kwargs):
        super().__init__(**kwargs)
        self.selection = selection

    def get_sql_type(self):
        return "VARCHAR(255)"

    def _validate_value(self, value):
        """Validate selection field value"""
        if value is None:
            return None

        # Get valid values from selection
        valid_values = []
        if isinstance(self.selection, list):
            for item in self.selection:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    valid_values.append(item[0])
                else:
                    valid_values.append(item)
        elif callable(self.selection):
            # Dynamic selection - call the function
            selection_result = self.selection()
            for item in selection_result:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    valid_values.append(item[0])
                else:
                    valid_values.append(item)

        if value not in valid_values:
            raise FieldValidationError(f"Invalid selection value: {value}. Valid values: {valid_values}")

        return value

    def get_selection_items(self):
        """Get selection items as list of (value, label) tuples"""
        if isinstance(self.selection, list):
            items = []
            for item in self.selection:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    items.append((item[0], item[1]))
                else:
                    items.append((item, item))
            return items
        elif callable(self.selection):
            selection_result = self.selection()
            items = []
            for item in selection_result:
                if isinstance(item, (list, tuple)) and len(item) >= 2:
                    items.append((item[0], item[1]))
                else:
                    items.append((item, item))
            return items
        return []
