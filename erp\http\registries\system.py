"""
System route registry implementation
Handles system-level routes that are not database-dependent
"""

import asyncio
from typing import Dict, List, Optional, Set
from collections import defaultdict

from ..interfaces import IRouteRegistry, RouteInfo, RouteScope, RouteRegistrationError
from ...logging import get_logger

logger = get_logger(__name__)


class SystemRouteRegistry(IRouteRegistry):
    """Registry for system-level routes"""
    
    def __init__(self):
        self._routes: Dict[str, List[RouteInfo]] = defaultdict(list)
        self._route_lookup: Dict[str, RouteInfo] = {}  # path:method -> RouteInfo
        self._lock = asyncio.Lock()
        self._sources: Set[str] = set()
    
    async def register_route(self, route_info: RouteInfo) -> bool:
        """Register a system route"""
        if route_info.scope != RouteScope.SYSTEM:
            raise RouteRegistrationError(f"Invalid scope for system registry: {route_info.scope}")
        
        async with self._lock:
            try:
                # Check for conflicts
                for method in route_info.methods:
                    lookup_key = f"{route_info.path}:{method.upper()}"
                    if lookup_key in self._route_lookup:
                        existing = self._route_lookup[lookup_key]
                        logger.warning(
                            f"Route conflict: {method} {route_info.path} "
                            f"(existing: {existing.source}, new: {route_info.source})"
                        )
                        # Allow override for now, but log it
                
                # Register the route
                self._routes[route_info.path].append(route_info)
                
                # Update lookup table
                for method in route_info.methods:
                    lookup_key = f"{route_info.path}:{method.upper()}"
                    self._route_lookup[lookup_key] = route_info
                
                # Track source
                if route_info.source:
                    self._sources.add(route_info.source)
                
                logger.debug(
                    f"Registered system route: {route_info.methods} {route_info.path} "
                    f"from {route_info.source or 'unknown'}"
                )
                return True
                
            except Exception as e:
                logger.error(f"Failed to register system route {route_info.path}: {e}")
                raise RouteRegistrationError(f"Registration failed: {e}")

    def _register_route_sync(self, route_info: RouteInfo) -> bool:
        """Register a system route synchronously (for use during module import)"""
        if route_info.scope != RouteScope.SYSTEM:
            raise RouteRegistrationError(f"Invalid scope for system registry: {route_info.scope}")

        try:
            # Check for conflicts
            for method in route_info.methods:
                lookup_key = f"{route_info.path}:{method.upper()}"
                if lookup_key in self._route_lookup:
                    existing = self._route_lookup[lookup_key]
                    logger.warning(
                        f"Route conflict: {method} {route_info.path} "
                        f"(existing: {existing.source}, new: {route_info.source})"
                    )
                    # Allow override for now, but log it

            # Register the route
            self._routes[route_info.path].append(route_info)

            # Update lookup table
            for method in route_info.methods:
                lookup_key = f"{route_info.path}:{method.upper()}"
                self._route_lookup[lookup_key] = route_info

            # Track source
            if route_info.source:
                self._sources.add(route_info.source)

            logger.debug(
                f"Registered system route (sync): {route_info.methods} {route_info.path} "
                f"from {route_info.source or 'unknown'}"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to register system route {route_info.path}: {e}")
            raise RouteRegistrationError(f"Registration failed: {e}")

    async def unregister_route(self, path: str, methods: Optional[List[str]] = None) -> bool:
        """Unregister a system route"""
        async with self._lock:
            try:
                if path not in self._routes:
                    return False
                
                if methods is None:
                    # Remove all methods for this path
                    route_infos = self._routes[path]
                    for route_info in route_infos:
                        for method in route_info.methods:
                            lookup_key = f"{path}:{method.upper()}"
                            self._route_lookup.pop(lookup_key, None)
                    
                    del self._routes[path]
                    logger.debug(f"Unregistered all methods for system route: {path}")
                else:
                    # Remove specific methods
                    remaining_routes = []
                    for route_info in self._routes[path]:
                        remaining_methods = [m for m in route_info.methods if m.upper() not in [m.upper() for m in methods]]
                        if remaining_methods:
                            # Create new route info with remaining methods
                            new_route_info = RouteInfo(
                                path=route_info.path,
                                handler=route_info.handler,
                                methods=remaining_methods,
                                route_type=route_info.route_type,
                                auth=route_info.auth,
                                scope=route_info.scope,
                                metadata=route_info.metadata,
                                source=route_info.source
                            )
                            remaining_routes.append(new_route_info)
                        
                        # Remove from lookup
                        for method in methods:
                            lookup_key = f"{path}:{method.upper()}"
                            self._route_lookup.pop(lookup_key, None)
                    
                    if remaining_routes:
                        self._routes[path] = remaining_routes
                    else:
                        del self._routes[path]
                    
                    logger.debug(f"Unregistered methods {methods} for system route: {path}")
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to unregister system route {path}: {e}")
                return False
    
    async def get_routes(self) -> Dict[str, List[RouteInfo]]:
        """Get all registered system routes"""
        async with self._lock:
            return {path: routes.copy() for path, routes in self._routes.items()}
    
    async def get_route(self, path: str, method: str) -> Optional[RouteInfo]:
        """Get specific route by path and method"""
        lookup_key = f"{path}:{method.upper()}"
        return self._route_lookup.get(lookup_key)
    
    async def clear(self) -> None:
        """Clear all system routes"""
        async with self._lock:
            self._routes.clear()
            self._route_lookup.clear()
            self._sources.clear()
            logger.debug("Cleared all system routes")
    
    async def get_routes_by_source(self, source: str) -> Dict[str, List[RouteInfo]]:
        """Get routes from a specific source"""
        async with self._lock:
            result = defaultdict(list)
            for path, route_list in self._routes.items():
                for route_info in route_list:
                    if route_info.source == source:
                        result[path].append(route_info)
            return dict(result)
    
    async def unregister_routes_by_source(self, source: str) -> int:
        """Unregister all routes from a specific source"""
        async with self._lock:
            removed_count = 0
            paths_to_remove = []
            
            for path, route_list in self._routes.items():
                remaining_routes = []
                for route_info in route_list:
                    if route_info.source == source:
                        # Remove from lookup
                        for method in route_info.methods:
                            lookup_key = f"{path}:{method.upper()}"
                            self._route_lookup.pop(lookup_key, None)
                        removed_count += 1
                    else:
                        remaining_routes.append(route_info)
                
                if remaining_routes:
                    self._routes[path] = remaining_routes
                else:
                    paths_to_remove.append(path)
            
            # Remove empty paths
            for path in paths_to_remove:
                del self._routes[path]
            
            # Remove source from tracking
            self._sources.discard(source)
            
            logger.debug(f"Unregistered {removed_count} routes from source: {source}")
            return removed_count
    
    def get_sources(self) -> Set[str]:
        """Get all tracked sources"""
        return self._sources.copy()
    
    def get_stats(self) -> Dict[str, int]:
        """Get registry statistics"""
        total_routes = sum(len(routes) for routes in self._routes.values())
        return {
            'total_paths': len(self._routes),
            'total_routes': total_routes,
            'total_sources': len(self._sources)
        }

    def get_system_route_paths(self) -> Set[str]:
        """
        Get all registered system route paths for middleware use.

        Returns:
            Set of exact route paths that should skip database middleware
        """
        # Return only registered routes - FastAPI automatic routes come from configuration
        return set(self._routes.keys())




# Global system route registry instance
_system_registry = SystemRouteRegistry()


def get_system_route_registry() -> SystemRouteRegistry:
    """Get the global system route registry"""
    return _system_registry


async def reset_system_route_registry():
    """Reset the global system route registry (mainly for testing)"""
    global _system_registry
    await _system_registry.clear()
    _system_registry = SystemRouteRegistry()
