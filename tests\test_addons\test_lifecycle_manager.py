"""
Test suite for AddonLifecycleManager

This module tests the centralized addon lifecycle manager functionality,
including temporary model registry creation, cleanup, and context management.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from erp.addons.lifecycle_manager import (
    AddonLifecycleManager, 
    get_addon_lifecycle_manager, 
    reset_addon_lifecycle_manager
)
from erp.models.lifecycle_manager import ModelRegistry


class TestAddonLifecycleManager:
    """Test AddonLifecycleManager functionality"""

    def setup_method(self):
        """Setup for each test method"""
        # Reset the global manager before each test
        reset_addon_lifecycle_manager()

    def teardown_method(self):
        """Cleanup after each test method"""
        # Reset the global manager after each test
        reset_addon_lifecycle_manager()

    def test_lifecycle_manager_initialization(self):
        """Test AddonLifecycleManager initialization"""
        manager = AddonLifecycleManager()
        
        assert manager._active_registries == {}
        assert manager._lock is not None
        assert manager.logger is not None

    def test_get_addon_lifecycle_manager_singleton(self):
        """Test that get_addon_lifecycle_manager returns singleton"""
        manager1 = get_addon_lifecycle_manager()
        manager2 = get_addon_lifecycle_manager()
        
        assert manager1 is manager2
        assert isinstance(manager1, AddonLifecycleManager)

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_create_temporary_model_registry(self, mock_create_registry):
        """Test creating temporary model registry"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Test successful creation
        result = manager.create_temporary_model_registry('test_addon')
        
        assert result is mock_registry
        assert 'test_addon' in manager._active_registries
        assert manager._active_registries['test_addon'] is mock_registry
        mock_create_registry.assert_called_once_with('test_addon')

    def test_create_temporary_model_registry_empty_name(self):
        """Test creating temporary model registry with empty name"""
        manager = AddonLifecycleManager()
        
        with pytest.raises(ValueError, match="addon_name cannot be empty or None"):
            manager.create_temporary_model_registry('')
        
        with pytest.raises(ValueError, match="addon_name cannot be empty or None"):
            manager.create_temporary_model_registry(None)

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_create_temporary_model_registry_existing(self, mock_create_registry):
        """Test creating temporary model registry when one already exists"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Create first registry
        result1 = manager.create_temporary_model_registry('test_addon')
        
        # Try to create second registry for same addon
        result2 = manager.create_temporary_model_registry('test_addon')
        
        assert result1 is result2
        assert mock_create_registry.call_count == 1  # Should only be called once

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_cleanup_temporary_model_registry(self, mock_create_registry):
        """Test cleaning up temporary model registry"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Create registry
        manager.create_temporary_model_registry('test_addon')
        assert 'test_addon' in manager._active_registries
        
        # Cleanup registry
        result = manager.cleanup_temporary_model_registry('test_addon')
        
        assert result is True
        assert 'test_addon' not in manager._active_registries
        mock_registry.clear.assert_called_once()

    def test_cleanup_temporary_model_registry_nonexistent(self):
        """Test cleaning up non-existent temporary model registry"""
        manager = AddonLifecycleManager()
        
        result = manager.cleanup_temporary_model_registry('nonexistent_addon')
        
        assert result is False

    def test_cleanup_temporary_model_registry_empty_name(self):
        """Test cleaning up temporary model registry with empty name"""
        manager = AddonLifecycleManager()
        
        result1 = manager.cleanup_temporary_model_registry('')
        result2 = manager.cleanup_temporary_model_registry(None)
        
        assert result1 is False
        assert result2 is False

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_cleanup_temporary_model_registry_error(self, mock_create_registry):
        """Test cleanup when registry.clear() raises exception"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_registry.clear.side_effect = Exception("Clear failed")
        mock_create_registry.return_value = mock_registry
        
        # Create registry
        manager.create_temporary_model_registry('test_addon')
        
        # Cleanup should handle exception gracefully
        result = manager.cleanup_temporary_model_registry('test_addon')
        
        assert result is False
        assert 'test_addon' not in manager._active_registries

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_get_active_registry(self, mock_create_registry):
        """Test getting active registry"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Test when registry doesn't exist
        result = manager.get_active_registry('test_addon')
        assert result is None
        
        # Create registry and test again
        manager.create_temporary_model_registry('test_addon')
        result = manager.get_active_registry('test_addon')
        assert result is mock_registry

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_has_active_registry(self, mock_create_registry):
        """Test checking if active registry exists"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Test when registry doesn't exist
        assert manager.has_active_registry('test_addon') is False
        
        # Create registry and test again
        manager.create_temporary_model_registry('test_addon')
        assert manager.has_active_registry('test_addon') is True

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_get_active_addon_names(self, mock_create_registry):
        """Test getting list of active addon names"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Test when no registries exist
        assert manager.get_active_addon_names() == []
        
        # Create registries
        manager.create_temporary_model_registry('addon1')
        manager.create_temporary_model_registry('addon2')
        
        active_names = manager.get_active_addon_names()
        assert set(active_names) == {'addon1', 'addon2'}

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_cleanup_all_registries(self, mock_create_registry):
        """Test cleaning up all registries"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Create multiple registries
        manager.create_temporary_model_registry('addon1')
        manager.create_temporary_model_registry('addon2')
        manager.create_temporary_model_registry('addon3')
        
        assert len(manager._active_registries) == 3
        
        # Cleanup all
        cleaned_count = manager.cleanup_all_registries()
        
        assert cleaned_count == 3
        assert len(manager._active_registries) == 0

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    def test_get_status_report(self, mock_create_registry):
        """Test getting status report"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Test empty status
        status = manager.get_status_report()
        expected = {
            'active_registries_count': 0,
            'active_addon_names': [],
            'manager_status': 'active'
        }
        assert status == expected
        
        # Create registries and test again
        manager.create_temporary_model_registry('addon1')
        manager.create_temporary_model_registry('addon2')
        
        status = manager.get_status_report()
        assert status['active_registries_count'] == 2
        assert set(status['active_addon_names']) == {'addon1', 'addon2'}
        assert status['manager_status'] == 'active'

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    @pytest.mark.asyncio
    async def test_temporary_registry_context_success(self, mock_create_registry):
        """Test temporary registry context manager success case"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Test context manager
        async with manager.temporary_registry_context('test_addon') as registry:
            assert registry is mock_registry
            assert manager.has_active_registry('test_addon')
        
        # Registry should be cleaned up after context
        assert not manager.has_active_registry('test_addon')
        mock_registry.clear.assert_called_once()

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    @pytest.mark.asyncio
    async def test_temporary_registry_context_exception(self, mock_create_registry):
        """Test temporary registry context manager with exception"""
        manager = AddonLifecycleManager()
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        
        # Test context manager with exception
        with pytest.raises(ValueError, match="Test exception"):
            async with manager.temporary_registry_context('test_addon') as registry:
                assert registry is mock_registry
                assert manager.has_active_registry('test_addon')
                raise ValueError("Test exception")
        
        # Registry should still be cleaned up after exception
        assert not manager.has_active_registry('test_addon')
        mock_registry.clear.assert_called_once()

    def test_reset_addon_lifecycle_manager(self):
        """Test resetting the global manager"""
        # Get manager and create some registries
        manager1 = get_addon_lifecycle_manager()
        with patch('erp.addons.lifecycle_manager.create_addon_model_registry'):
            manager1.create_temporary_model_registry('test_addon')
        
        # Reset and get new manager
        reset_addon_lifecycle_manager()
        manager2 = get_addon_lifecycle_manager()
        
        # Should be a new instance with clean state
        assert manager2 is not manager1
        assert len(manager2._active_registries) == 0


class TestAddonLifecycleManagerIntegration:
    """Integration tests for AddonLifecycleManager with other components"""

    def setup_method(self):
        """Setup for each test method"""
        reset_addon_lifecycle_manager()

    def teardown_method(self):
        """Cleanup after each test method"""
        reset_addon_lifecycle_manager()

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    @patch('erp.addons.installers.BaseModuleInstaller._get_lifecycle_manager')
    def test_base_module_installer_integration(self, mock_get_manager, mock_create_registry):
        """Test BaseModuleInstaller integration with lifecycle manager"""
        from erp.addons.installers import BaseModuleInstaller

        # Setup mocks
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_create_registry.return_value = mock_registry
        mock_manager = MagicMock(spec=AddonLifecycleManager)
        mock_manager.temporary_registry_context.return_value.__aenter__ = AsyncMock(return_value=mock_registry)
        mock_manager.temporary_registry_context.return_value.__aexit__ = AsyncMock(return_value=None)
        mock_get_manager.return_value = mock_manager

        installer = BaseModuleInstaller()
        lifecycle_manager = installer._get_lifecycle_manager()

        assert lifecycle_manager is mock_manager
        mock_get_manager.assert_called_once()

    @patch('erp.addons.lifecycle_manager.create_addon_model_registry')
    @patch('erp.utils.schema.get_addon_lifecycle_manager')
    def test_schema_synchronization_integration(self, mock_get_manager, mock_create_registry):
        """Test schema synchronization integration with lifecycle manager"""
        # Setup mocks
        mock_registry = MagicMock(spec=ModelRegistry)
        mock_registry.all.return_value = {'test.model': MagicMock()}
        mock_create_registry.return_value = mock_registry
        mock_manager = MagicMock(spec=AddonLifecycleManager)
        mock_manager.create_temporary_model_registry.return_value = mock_registry
        mock_get_manager.return_value = mock_manager

        # Import here to avoid circular imports during test setup
        from erp.utils.schema import SchemaComparator

        # Test that schema sync uses the lifecycle manager
        with patch.object(SchemaComparator, 'sync_model_tables') as mock_sync:
            mock_sync.return_value = {'sync_successful': True}
            # This would be called during actual schema sync
            # We're just testing the integration point exists

        # Verify the lifecycle manager would be used
        assert mock_get_manager is not None

    @pytest.mark.asyncio
    async def test_context_manager_cleanup_on_exception(self):
        """Test that context manager properly cleans up on exceptions"""
        manager = get_addon_lifecycle_manager()

        with patch('erp.addons.lifecycle_manager.create_addon_model_registry') as mock_create:
            mock_registry = MagicMock(spec=ModelRegistry)
            mock_create.return_value = mock_registry

            # Test that cleanup happens even with exception
            with pytest.raises(RuntimeError, match="Test error"):
                async with manager.temporary_registry_context('test_addon') as registry:
                    assert registry is mock_registry
                    raise RuntimeError("Test error")

            # Verify cleanup was called
            mock_registry.clear.assert_called_once()
            assert not manager.has_active_registry('test_addon')

    @pytest.mark.asyncio
    async def test_multiple_concurrent_registries(self):
        """Test handling multiple concurrent registries"""
        manager = get_addon_lifecycle_manager()

        with patch('erp.addons.lifecycle_manager.create_addon_model_registry') as mock_create:
            mock_registry1 = MagicMock(spec=ModelRegistry)
            mock_registry2 = MagicMock(spec=ModelRegistry)
            mock_create.side_effect = [mock_registry1, mock_registry2]

            # Create multiple registries concurrently
            async with manager.temporary_registry_context('addon1') as registry1:
                async with manager.temporary_registry_context('addon2') as registry2:
                    assert registry1 is mock_registry1
                    assert registry2 is mock_registry2
                    assert manager.has_active_registry('addon1')
                    assert manager.has_active_registry('addon2')

                # addon2 should be cleaned up
                assert manager.has_active_registry('addon1')
                assert not manager.has_active_registry('addon2')

            # Both should be cleaned up
            assert not manager.has_active_registry('addon1')
            assert not manager.has_active_registry('addon2')

            # Verify both registries were cleaned up
            mock_registry1.clear.assert_called_once()
            mock_registry2.clear.assert_called_once()

    def test_thread_safety(self):
        """Test thread safety of the lifecycle manager"""
        import threading
        import time

        manager = get_addon_lifecycle_manager()
        results = []
        errors = []

        def create_and_cleanup_registry(addon_name):
            try:
                with patch('erp.addons.lifecycle_manager.create_addon_model_registry') as mock_create:
                    mock_registry = MagicMock(spec=ModelRegistry)
                    mock_create.return_value = mock_registry

                    # Create registry
                    registry = manager.create_temporary_model_registry(addon_name)
                    results.append(f"created_{addon_name}")

                    # Small delay to increase chance of race conditions
                    time.sleep(0.01)

                    # Cleanup registry
                    success = manager.cleanup_temporary_model_registry(addon_name)
                    if success:
                        results.append(f"cleaned_{addon_name}")

            except Exception as e:
                errors.append(f"{addon_name}: {e}")

        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_and_cleanup_registry, args=[f"addon_{i}"])
            threads.append(thread)

        # Start all threads
        for thread in threads:
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Verify no errors occurred
        assert len(errors) == 0, f"Thread safety errors: {errors}"

        # Verify all operations completed
        assert len(results) == 10  # 5 creates + 5 cleanups

        # Verify no registries are left active
        assert len(manager.get_active_addon_names()) == 0
