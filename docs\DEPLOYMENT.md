# Deployment Guide

## Docker Setup

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Python 3.12+ with virtual environment
- Git

### Quick Start

#### 1. Start Database Services
```bash
# Start PostgreSQL and pgAdmin in Docker
docker-compose up -d

# Check services are running
docker-compose ps
```

#### 2. Install Python Dependencies
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

#### 3. Run Application Locally
```bash
# Start the ERP server
python erp-bin start
```

## Database Modes

### Single Database Mode
When `db_name` is specified in configuration:
```ini
[options]
db_name = erp_main
list_db = False
```

### Multi-Database Mode
When `db_filter` is used instead:
```ini
[options]
db_filter = ^(erp_.*|test_.*)$
list_db = True
```

## Configuration Files

### Main Configuration (erp.conf)
```ini
[options]
# Server configuration
http_interface = 127.0.0.1
http_port = 8069

# Database configuration
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp

# Multi-database support
list_db = True
db_filter = ^(erp_.*|test_.*)$

# Addon configuration
addons_path = addons

# Logging configuration
log_level = DEBUG
log_file = logs/erp.log
```

### Example Configurations

#### Multi-Database Setup
```ini
# erp.conf.example-multi-db
[options]
http_interface = 127.0.0.1
http_port = 8069
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp
list_db = True
db_filter = ^(erp_.*|test_.*)$
addons_path = addons
log_level = INFO
log_file = logs/erp.log
```

#### Enhanced Logging Setup
```ini
# erp.conf.example-logging
[options]
http_interface = 127.0.0.1
http_port = 8069
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp
db_name = erp_main
addons_path = addons
log_level = DEBUG
log_file = logs/erp.log
```

## Docker Services

### PostgreSQL Container
- **Image**: postgres:16-alpine
- **Port**: 5432
- **Database**: erp
- **User**: erp
- **Password**: erp

### pgAdmin Container
- **Image**: dpage/pgadmin4:latest
- **Port**: 5050
- **Email**: <EMAIL>
- **Password**: admin

### Access URLs
- **ERP Application**: http://localhost:8069
- **pgAdmin**: http://localhost:5050

## Production Deployment

### Environment Variables
```bash
# Database configuration
export DB_HOST=your-db-host
export DB_PORT=5432
export DB_USER=erp_user
export DB_PASSWORD=secure_password

# Server configuration
export HTTP_INTERFACE=0.0.0.0
export HTTP_PORT=8069

# Logging
export LOG_LEVEL=INFO
export LOG_FILE=/var/log/erp/erp.log
```

### Systemd Service
```ini
# /etc/systemd/system/erp.service
[Unit]
Description=ERP System
After=network.target postgresql.service

[Service]
Type=simple
User=erp
Group=erp
WorkingDirectory=/opt/erp
Environment=PATH=/opt/erp/venv/bin
ExecStart=/opt/erp/venv/bin/python erp-bin start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### Nginx Configuration
```nginx
# /etc/nginx/sites-available/erp
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /opt/erp/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## Database Management

### Initial Setup
```sql
-- Create database and user
CREATE DATABASE erp_main;
CREATE USER erp WITH PASSWORD 'erp';
GRANT ALL PRIVILEGES ON DATABASE erp_main TO erp;

-- For multi-database setup
CREATE DATABASE erp_production;
CREATE DATABASE erp_staging;
CREATE DATABASE test_db;
```

### Backup and Restore
```bash
# Backup database
pg_dump -h localhost -U erp -d erp_main > backup.sql

# Restore database
psql -h localhost -U erp -d erp_main < backup.sql
```

## Monitoring

### Log Files
- **Application logs**: `logs/erp.log`
- **Access logs**: Included in application logs
- **Error logs**: Included in application logs

### Health Checks
```bash
# Check server status
curl http://localhost:8069/health

# Check database connectivity
python -c "
import asyncio
from erp.database.registry import DatabaseRegistry
async def check():
    registry = DatabaseRegistry()
    try:
        async with registry.get_database('erp_main') as db:
            result = await db.execute('SELECT 1')
            print('Database OK')
    except Exception as e:
        print(f'Database Error: {e}')
asyncio.run(check())
"
```

### Performance Monitoring
- Request timing in logs
- Database query performance
- Memory usage monitoring
- Error rate tracking

## Security

### Database Security
- Use strong passwords
- Limit database user privileges
- Enable SSL connections in production
- Regular security updates

### Application Security
- Run as non-root user
- Use environment variables for secrets
- Enable HTTPS in production
- Regular dependency updates

### Network Security
- Firewall configuration
- VPN access for management
- Rate limiting
- DDoS protection

## Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check PostgreSQL is running
docker-compose ps postgres

# Check connection
psql -h localhost -U erp -d erp_main
```

#### Port Already in Use
```bash
# Find process using port 8069
lsof -i :8069

# Kill process if needed
kill -9 <PID>
```

#### Permission Denied
```bash
# Fix file permissions
chmod +x erp-bin
chown -R erp:erp /opt/erp
```

### Log Analysis
```bash
# View recent logs
tail -f logs/erp.log

# Search for errors
grep "ERROR" logs/erp.log

# Filter by component
grep "database" logs/erp.log
```

## Scaling

### Horizontal Scaling
- Multiple application instances
- Load balancer configuration
- Session management
- Database connection pooling

### Vertical Scaling
- Increase server resources
- Optimize database queries
- Cache implementation
- Performance tuning

### Database Scaling
- Read replicas
- Connection pooling
- Query optimization
- Index management
