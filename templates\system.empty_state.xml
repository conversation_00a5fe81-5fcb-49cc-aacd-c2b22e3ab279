<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_empty_state" xml:space="preserve">

    <!-- Compact Reusable Empty State Component -->
    <t t-name="system.empty_state">
        <div class="text-center py-16 px-6 bg-gray-50/50 border-2 border-dashed border-gray-200 rounded-2xl">
            <!-- Icon with subtle animation -->
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl shadow-lg animate-pulse">
                <i t-att-class="icon_class or 'fas fa-database'"></i>
            </div>

            <!-- Title and description -->
            <h3 class="text-2xl font-semibold text-gray-900 mb-3" t-esc="title or 'No items found'"/>
            <p class="text-gray-600 mb-8 max-w-md mx-auto" t-esc="description or 'No items are currently available.'"/>
        </div>
    </t>

    <!-- Database-specific empty state (backward compatibility) -->
    <t t-name="system.database_empty_state">
        <div class="text-center py-16 px-6 bg-gray-50/50 border-2 border-dashed border-gray-200 rounded-2xl">
            <!-- Icon with subtle animation -->
            <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl shadow-lg animate-pulse">
                <i class="fas fa-database"></i>
            </div>

            <!-- Title and description -->
            <h3 class="text-2xl font-semibold text-gray-900 mb-3">No databases found</h3>
            <p class="text-gray-600 mb-8 max-w-md mx-auto">Welcome to your ERP system! Create your first database to begin managing your business operations.</p>

            <!-- Action buttons -->
            <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button class="inline-flex items-center px-6 py-3 rounded-lg font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-md" onclick="showCreateDatabaseModal()">
                    <i class="fas fa-plus mr-2"></i>
                    <span>Create Your First Database</span>
                </button>
                <button class="inline-flex items-center px-4 py-2 rounded-lg font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors shadow-sm" onclick="refreshDatabases()">
                    <i class="fas fa-sync-alt mr-2"></i>
                    <span>Refresh List</span>
                </button>
            </div>
        </div>
    </t>

</templates>
