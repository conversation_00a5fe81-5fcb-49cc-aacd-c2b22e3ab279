<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_create_database_modal" xml:space="preserve">

    <!-- Create Database Modal Component -->
    <t t-name="system.create_database_modal">
        <div id="createDatabaseModal" class="modal fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div class="bg-white rounded-xl p-8 w-full max-w-md mx-4 shadow-2xl border border-gray-200">
                <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                    <div class="flex items-center space-x-2 text-xl font-semibold text-gray-900">
                        <i class="fas fa-plus-circle text-blue-600"></i>
                        <span>Create New Database</span>
                    </div>
                    <button type="button" class="p-2 hover:bg-gray-100 rounded-md transition-colors text-gray-400 hover:text-gray-600" onclick="hideCreateDatabaseModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="createDatabaseForm" onsubmit="handleCreateDatabase(event)">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2" for="dbName">Database Name *</label>
                        <input type="text" id="dbName" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                               placeholder="Enter database name" required="required"
                               pattern="[a-zA-Z][a-zA-Z0-9_]*"
                               title="Database name must start with a letter and contain only letters, numbers, and underscores"/>
                    </div>
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2" for="dbLanguage">Default Language</label>
                        <select id="dbLanguage" name="language" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                            <option value="en_US">English (US)</option>
                            <option value="en_GB">English (UK)</option>
                            <option value="fr_FR">French</option>
                            <option value="de_DE">German</option>
                            <option value="es_ES">Spanish</option>
                        </select>
                    </div>
                    <div class="mb-6">
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" id="loadDemo" name="demo" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"/>
                            <span class="text-sm text-gray-700">Install demo data</span>
                        </label>
                    </div>
                    <div class="flex space-x-3 pt-6 border-t border-gray-200">
                        <button type="button" class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" onclick="hideCreateDatabaseModal()">
                            Cancel
                        </button>
                        <button type="submit" class="flex-1 inline-flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Create Database
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </t>

</templates>
