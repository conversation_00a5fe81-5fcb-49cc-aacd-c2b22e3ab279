"""
Controller components package
Provides modular controller functionality through mixins and specialized components
"""

from .base import BaseController
from .mixins import (
    RequestMixin, 
    ResponseMixin, 
    TemplateMixin, 
    DatabaseMixin, 
    AuthMixin
)
from .controller import (
    Controller,
    MinimalController,
    APIController,
    TemplateController,
    DatabaseController
)
from .registry import ControllerRegistry, get_controller_registry

__all__ = [
    'BaseController',
    'RequestMixin',
    'ResponseMixin', 
    'TemplateMixin',
    'DatabaseMixin',
    'AuthMixin',
    'Controller', 'MinimalController', 'APIController', 'TemplateController', 'DatabaseController',
    'ControllerRegistry',
    'get_controller_registry'
]
