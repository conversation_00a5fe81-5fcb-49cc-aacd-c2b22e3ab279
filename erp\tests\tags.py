"""
Test tags and decorators for the ERP test framework
"""
from typing import Any, Callable, Union


class Tags:
    """Standard test tags for categorizing tests"""
    
    # Test types
    UNIT = 'unit'
    INTEGRATION = 'integration'
    FUNCTIONAL = 'functional'
    PERFORMANCE = 'performance'
    
    # Component areas
    BASE = 'base'
    ADDON = 'addon'
    MODEL = 'model'
    FIELD = 'field'
    DATABASE = 'database'
    MEMORY = 'memory'
    REGISTRY = 'registry'
    HTTP = 'http'
    ROUTE = 'route'
    API = 'api'
    AUTH = 'auth'
    SECURITY = 'security'
    
    # Special categories
    SLOW = 'slow'
    EXTERNAL = 'external'


def tag(*tags: Union[str, Tags]) -> Callable:
    """
    Decorator to tag test classes or methods with categories.
    
    Args:
        *tags: Tag names or Tags enum values
        
    Returns:
        Decorated test class or method
        
    Example:
        @tag(Tags.UNIT, Tags.MODEL)
        class TestMyModel(TestCase):
            pass
            
        @tag('custom_tag', Tags.SLOW)
        def test_slow_operation(self):
            pass
    """
    def decorator(test_item: Any) -> Any:
        # Convert tags to strings
        tag_strings = []
        for t in tags:
            if isinstance(t, str):
                tag_strings.append(t)
            else:
                tag_strings.append(str(t))
        
        # Add tags to the test item
        if hasattr(test_item, '_test_tags'):
            test_item._test_tags.extend(tag_strings)
        else:
            test_item._test_tags = tag_strings
            
        return test_item
    
    return decorator


def get_test_tags(test_item: Any) -> list[str]:
    """
    Get the tags associated with a test class or method.
    
    Args:
        test_item: Test class or method
        
    Returns:
        List of tag strings
    """
    return getattr(test_item, '_test_tags', [])


def has_tag(test_item: Any, tag_name: str) -> bool:
    """
    Check if a test item has a specific tag.
    
    Args:
        test_item: Test class or method
        tag_name: Tag name to check for
        
    Returns:
        True if the test item has the tag
    """
    return tag_name in get_test_tags(test_item)


def filter_by_tags(test_items: list, include_tags: list[str] = None, 
                   exclude_tags: list[str] = None) -> list:
    """
    Filter test items by tags.
    
    Args:
        test_items: List of test classes or methods
        include_tags: Tags that must be present
        exclude_tags: Tags that must not be present
        
    Returns:
        Filtered list of test items
    """
    filtered = []
    
    for item in test_items:
        item_tags = get_test_tags(item)
        
        # Check include tags
        if include_tags:
            if not any(tag in item_tags for tag in include_tags):
                continue
                
        # Check exclude tags
        if exclude_tags:
            if any(tag in item_tags for tag in exclude_tags):
                continue
                
        filtered.append(item)
    
    return filtered
