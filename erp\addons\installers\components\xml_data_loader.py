"""
XML Data Loading Component

This module provides a reusable component for loading XML data files
for addons. It can be used by various installers to load data files
consistently across different addon types.
"""

from typing import Dict, Any, List, TYPE_CHECKING
from ....logging import get_logger
from ...utils.path_resolver import get_addon_path_resolver

if TYPE_CHECKING:
    from ....database.connection.manager import DatabaseManager


class XMLDataLoader:
    """
    Reusable component for loading XML data files for addons.
    
    This component handles:
    - Reading addon manifests to get data file lists
    - Loading data files using the DataLoader
    - Error handling and reporting
    - Path resolution using the addon path resolver
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.path_resolver = get_addon_path_resolver()
    
    async def load_addon_data_files(
        self,
        db_manager: 'DatabaseManager',
        addon_name: str,
        strict_xmlid_validation: bool = True
    ) -> Dict[str, Any]:
        """
        Load all data files for an addon.

        Args:
            db_manager: Database manager for raw SQL operations
            addon_name: Name of the addon to load data for
            strict_xmlid_validation: Whether to enable strict XML ID validation

        Returns:
            Dictionary with loading results
        """
        try:
            from erp.data.loader import DataLoader
            
            # Find addon path
            addon_path = self.path_resolver.find_addon_path(addon_name)
            if not addon_path:
                self.logger.error(f"{addon_name} addon not found in any configured addon path")
                return {
                    'success': False,
                    'error': f"Addon {addon_name} not found",
                    'total_loaded': 0,
                    'files_processed': 0
                }

            # Read manifest to get data files
            manifest_path = self.path_resolver.get_addon_manifest_path(addon_name)
            if not manifest_path:
                self.logger.error(f"{addon_name} manifest not found")
                return {
                    'success': False,
                    'error': f"Manifest for {addon_name} not found",
                    'total_loaded': 0,
                    'files_processed': 0
                }

            # Load manifest
            manifest = self._load_manifest(manifest_path)
            if not manifest:
                return {
                    'success': False,
                    'error': f"Failed to load manifest for {addon_name}",
                    'total_loaded': 0,
                    'files_processed': 0
                }

            data_files = manifest.get('data', [])
            if not data_files:
                self.logger.debug(f"No data files specified in {addon_name} manifest")
                return {
                    'success': True,
                    'total_loaded': 0,
                    'files_processed': 0,
                    'message': 'No data files to load'
                }

            # Create data loader with database manager and configuration
            config = {
                'strict_xmlid_validation': strict_xmlid_validation,
                'continue_on_error': True
            }
            loader = DataLoader(db_manager, config)

            # Load each data file
            total_loaded = 0
            files_processed = 0
            errors = []
            
            for data_file in data_files:
                result = await self._load_single_data_file(loader, addon_name, data_file)
                
                if result['success']:
                    total_loaded += result.get('loaded', 0)
                    files_processed += 1
                    self.logger.debug(f"✓ Loaded {result.get('loaded', 0)} records from {data_file}")
                else:
                    errors.append(f"Failed to load {data_file}: {result.get('error', 'Unknown error')}")
                    self.logger.error(f"Failed to load data file {data_file}: {result.get('error')}")

            self.logger.info(f"✓ Loaded {total_loaded} total records from {files_processed}/{len(data_files)} data files for {addon_name}")
            
            return {
                'success': len(errors) == 0,
                'total_loaded': total_loaded,
                'files_processed': files_processed,
                'total_files': len(data_files),
                'errors': errors
            }

        except Exception as e:
            self.logger.error(f"Failed to load {addon_name} XML data: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_loaded': 0,
                'files_processed': 0
            }
    
    async def _load_single_data_file(self, loader, addon_name: str, data_file: str) -> Dict[str, Any]:
        """
        Load a single data file for an addon.
        
        Args:
            loader: DataLoader instance
            addon_name: Name of the addon
            data_file: Relative path to the data file
            
        Returns:
            Dictionary with loading result for this file
        """
        try:
            file_path = self.path_resolver.get_addon_data_files_path(addon_name, data_file)
            if not file_path:
                return {
                    'success': False,
                    'error': f"Data file not found: {data_file}",
                    'loaded': 0
                }

            result = await loader.load_data_file(file_path, addon_name)
            
            # Handle any errors from the data loader
            if result.get('errors'):
                error_messages = []
                for error in result['errors']:
                    error_messages.append(str(error))
                    self.logger.error(f"Data loading error in {data_file}: {error}")
                
                return {
                    'success': False,
                    'error': '; '.join(error_messages),
                    'loaded': result.get('loaded', 0)
                }
            
            return {
                'success': True,
                'loaded': result.get('loaded', 0)
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'loaded': 0
            }
    
    def _load_manifest(self, manifest_path: str) -> Dict[str, Any]:
        """
        Load and parse an addon manifest file.

        Args:
            manifest_path: Path to the manifest file

        Returns:
            Parsed manifest dictionary, or None if failed
        """
        try:
            import ast

            with open(manifest_path, 'r') as f:
                manifest_content = f.read()

            # Parse the manifest as a Python literal (dictionary)
            manifest = ast.literal_eval(manifest_content)
            return manifest

        except Exception as e:
            self.logger.error(f"Failed to load manifest from {manifest_path}: {e}")
            return None
    
    async def validate_data_files_exist(self, addon_name: str) -> Dict[str, Any]:
        """
        Validate that all data files specified in the manifest exist.
        
        Args:
            addon_name: Name of the addon to validate
            
        Returns:
            Dictionary with validation results
        """
        try:
            # Read manifest
            manifest_path = self.path_resolver.get_addon_manifest_path(addon_name)
            if not manifest_path:
                return {
                    'valid': False,
                    'error': f"Manifest for {addon_name} not found"
                }

            manifest = self._load_manifest(manifest_path)
            if not manifest:
                return {
                    'valid': False,
                    'error': f"Failed to load manifest for {addon_name}"
                }

            data_files = manifest.get('data', [])
            missing_files = []
            
            for data_file in data_files:
                file_path = self.path_resolver.get_addon_data_files_path(addon_name, data_file)
                if not file_path:
                    missing_files.append(data_file)
            
            return {
                'valid': len(missing_files) == 0,
                'total_files': len(data_files),
                'missing_files': missing_files,
                'addon_name': addon_name
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }
    
    def get_addon_data_files_list(self, addon_name: str) -> List[str]:
        """
        Get the list of data files for an addon from its manifest.
        
        Args:
            addon_name: Name of the addon
            
        Returns:
            List of data file paths, or empty list if none found
        """
        try:
            manifest_path = self.path_resolver.get_addon_manifest_path(addon_name)
            if not manifest_path:
                return []

            manifest = self._load_manifest(manifest_path)
            if not manifest:
                return []

            return manifest.get('data', [])
            
        except Exception as e:
            self.logger.error(f"Failed to get data files list for {addon_name}: {e}")
            return []
