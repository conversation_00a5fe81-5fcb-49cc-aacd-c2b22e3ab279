"""
Backward compatibility layer for ERP CLI
Handles legacy argument patterns and command detection
"""
import argparse
import sys
from typing import List, Optional, Tuple


class CompatibilityArgumentParser:
    """
    Argument parser that provides backward compatibility for erp-bin usage patterns.
    
    Supports both:
    - New explicit command style: python erp-bin start --db mydb
    - Legacy implicit style: python erp-bin --db mydb (defaults to start)
    """
    
    def __init__(self):
        self.parser = None
        self.legacy_patterns = {
            # Legacy patterns that should default to 'start' command
            'server_args': ['--db', '--host', '--port', '--reload'],
            'init_indicators': ['init'],  # If 'init' is first positional arg
            'purge_indicators': ['purge']  # If 'purge' is first positional arg
        }
    
    def detect_command_from_args(self, args: List[str]) -> Tuple[str, List[str]]:
        """
        Detect the intended command from arguments and return (command, modified_args).
        
        Args:
            args: Raw command line arguments
            
        Returns:
            Tuple of (detected_command, modified_args_list)
        """
        if not args:
            return 'start', args
        
        # Check if first argument is a known command
        first_arg = args[0]
        known_commands = ['init', 'start', 'purge', '--help', '-h', '--version']
        
        if first_arg in known_commands:
            # Explicit command provided
            if first_arg in ['--help', '-h']:
                return 'help', args
            elif first_arg == '--version':
                return 'version', args
            else:
                return first_arg, args
        
        # Check for legacy patterns
        if self._has_init_pattern(args):
            return 'init', args
        elif self._has_purge_pattern(args):
            return 'purge', args
        elif self._has_server_pattern(args):
            # Legacy server start pattern - inject 'start' command
            return 'start', ['start'] + args
        else:
            # Default to start command for unknown patterns
            return 'start', ['start'] + args
    
    def _has_init_pattern(self, args: List[str]) -> bool:
        """Check if arguments match database initialization pattern"""
        # Look for patterns like: mydb --force, mydb --demo, etc.
        if not args:
            return False
        
        # If first arg doesn't start with '-' and isn't a known command, 
        # it might be a database name for init
        first_arg = args[0]
        if not first_arg.startswith('-') and first_arg not in ['start', 'purge']:
            # Check if there are init-specific flags
            init_flags = ['--force', '--demo', '--no-create', '--exit', '--no-http']
            return any(flag in args for flag in init_flags)
        
        return False
    
    def _has_purge_pattern(self, args: List[str]) -> bool:
        """Check if arguments match database purge pattern"""
        # Look for purge-specific flags without explicit purge command
        purge_flags = ['--force']
        return len(args) == 1 and args[0] in purge_flags
    
    def _has_server_pattern(self, args: List[str]) -> bool:
        """Check if arguments match server start pattern"""
        # Look for server-specific flags
        server_flags = ['--db', '--host', '--port', '--reload']
        return any(arg in server_flags for arg in args)
    
    def preprocess_args(self, args: Optional[List[str]] = None) -> List[str]:
        """
        Preprocess arguments to ensure compatibility with new command structure.
        
        Args:
            args: Command line arguments (defaults to sys.argv[1:])
            
        Returns:
            Preprocessed arguments list
        """
        if args is None:
            args = sys.argv[1:]
        
        # Handle empty args
        if not args:
            return ['start']
        
        # Detect command and modify args if needed
        command, modified_args = self.detect_command_from_args(args)
        
        return modified_args
    
    def is_legacy_usage(self, args: List[str]) -> bool:
        """Check if the arguments represent legacy usage patterns"""
        if not args:
            return True  # No args is legacy (default to start)
        
        first_arg = args[0]
        explicit_commands = ['init', 'start', 'purge']
        
        # If first arg is not an explicit command, it's legacy usage
        return first_arg not in explicit_commands
    
    def convert_legacy_init_args(self, args: List[str]) -> List[str]:
        """
        Convert legacy init arguments to new format.
        
        Legacy: python erp-bin mydb --force
        New:    python erp-bin init mydb --force
        """
        if not args or args[0].startswith('-'):
            return args
        
        # If first arg looks like a database name, prepend 'init'
        if not args[0] in ['start', 'purge', 'init']:
            return ['init'] + args
        
        return args
    
    def get_help_text(self) -> str:
        """Get comprehensive help text including legacy patterns"""
        return """
ERP System Command Line Interface

USAGE:
    python erp-bin <command> [options]
    python erp-bin [legacy-options]  # Backward compatibility

COMMANDS:
    init <db_name>     Initialize database
    start              Start HTTP server  
    purge              Remove all databases
    
LEGACY COMPATIBILITY:
    python erp-bin mydb --force        # Same as: python erp-bin init mydb --force
    python erp-bin --db mydb           # Same as: python erp-bin start --db mydb
    python erp-bin --host 0.0.0.0      # Same as: python erp-bin start --host 0.0.0.0
    python erp-bin                     # Same as: python erp-bin start

EXAMPLES:
    # Database initialization
    python erp-bin init mydb
    python erp-bin init mydb --force --demo
    
    # Server management  
    python erp-bin start
    python erp-bin start --db mydb
    python erp-bin start --host 0.0.0.0 --port 8080
    
    # Database cleanup
    python erp-bin purge
    python erp-bin purge --force
    
    # Legacy patterns (still supported)
    python erp-bin mydb --force         # Initialize database
    python erp-bin --db mydb --reload   # Start server
"""


def create_compatibility_parser() -> argparse.ArgumentParser:
    """Create a parser that handles both new and legacy argument patterns"""
    parser = argparse.ArgumentParser(
        description='ERP System Command Line Interface',
        prog='erp-bin',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        add_help=False  # We'll handle help ourselves for better compatibility
    )
    
    # Add global options that work in both modes
    parser.add_argument('--quiet', action='store_true',
                       help='Suppress banner output')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    parser.add_argument('--version', action='store_true',
                       help='Show version information')
    parser.add_argument('--help', '-h', action='store_true',
                       help='Show help message')
    
    return parser


def handle_version_and_help(args: List[str]) -> bool:
    """
    Handle --version and --help flags immediately.
    
    Returns:
        True if version/help was handled (should exit), False otherwise
    """
    if '--version' in args:
        from .utils import print_version
        print_version()
        return True
    
    if '--help' in args or '-h' in args:
        compat = CompatibilityArgumentParser()
        print(compat.get_help_text())
        return True
    
    return False
