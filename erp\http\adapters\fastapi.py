"""
FastAPI route adapter implementation
Handles adaptation of routes for FastAPI framework
"""

from typing import Any, Callable, Dict, List

from .base import BaseRouteAdapter
from ..interfaces import RouteInfo
from ..metadata import RouteType
from ..services import get_route_handler_factory
from ..middleware import get_middleware_pipeline
from ...logging import get_logger

logger = get_logger(__name__)


class FastAPIRouteAdapter(BaseRouteAdapter):
    """Adapter for FastAPI framework"""
    
    def __init__(self):
        super().__init__("fastapi")
        self.handler_factory = get_route_handler_factory()
        self.middleware_pipeline = get_middleware_pipeline()
    
    async def adapt_route(self, route_info: RouteInfo) -> Dict[str, Any]:
        """Adapt route for FastAPI"""
        try:
            self._validate_route_info(route_info)
            
            # Create route handler
            route_handler = self.handler_factory.create_handler(route_info)
            
            # Create FastAPI-compatible wrapper
            fastapi_handler = await self._create_fastapi_wrapper(route_handler, route_info)
            
            # Prepare route configuration
            route_config = {
                'path': self._normalize_path(route_info.path),
                'endpoint': fastapi_handler,
                'methods': self._normalize_methods(route_info.methods),
                'tags': self._extract_tags(route_info),
                'summary': self._extract_summary(route_info),
                'description': self._extract_description(route_info),
                'name': route_info.metadata.get('name', f"{route_info.path}_{route_info.methods[0]}"),
                'response_model': route_info.metadata.get('response_model'),
                'status_code': route_info.metadata.get('status_code', 200),
                'dependencies': route_info.metadata.get('dependencies', []),
                'include_in_schema': route_info.metadata.get('include_in_schema', True)
            }
            
            # Add route type specific configuration
            if route_info.route_type == RouteType.JSON:
                route_config['response_class'] = self._get_json_response_class()
            
            return route_config
            
        except Exception as e:
            logger.error(f"Error adapting route {route_info.path}: {e}")
            raise
    
    async def _create_fastapi_wrapper(self, route_handler: Any, route_info: RouteInfo) -> Callable:
        """Create FastAPI-compatible wrapper function"""
        
        async def fastapi_endpoint(request):
            """FastAPI endpoint wrapper"""
            try:
                # Execute through middleware pipeline
                response = await self.middleware_pipeline.execute_pipeline(
                    request, 
                    route_info, 
                    lambda req: route_handler.handle_request(req, route_info)
                )
                
                return response
                
            except Exception as e:
                logger.error(f"Error in FastAPI endpoint {route_info.path}: {e}")
                return await self._create_error_response(e, route_info, request)
        
        # Set function metadata for FastAPI
        fastapi_endpoint.__name__ = self._generate_function_name(route_info)
        fastapi_endpoint.__doc__ = self._extract_description(route_info)
        
        # Add parameter annotations if needed
        await self._add_parameter_annotations(fastapi_endpoint, route_info)
        
        return fastapi_endpoint
    
    def _generate_function_name(self, route_info: RouteInfo) -> str:
        """Generate a valid function name for the route"""
        # Convert path to valid function name
        name = route_info.path.replace('/', '_').replace('-', '_').replace('<', '').replace('>', '')
        if name.startswith('_'):
            name = name[1:]
        if not name:
            name = 'root'
        
        # Add method prefix
        method = route_info.methods[0].lower()
        return f"{method}_{name}"
    
    async def _add_parameter_annotations(self, func: Callable, route_info: RouteInfo) -> None:
        """Add parameter annotations to function for FastAPI"""
        try:
            # Get parameter definitions from route metadata
            params = route_info.metadata.get('parameters', {})
            
            if params:
                # This is a simplified implementation
                # In a real system, you'd want to dynamically modify the function signature
                pass
                
        except Exception as e:
            logger.debug(f"Error adding parameter annotations: {e}")
    
    async def _create_error_response(self, error: Exception, route_info: RouteInfo, request: Any = None) -> Any:
        """Create route-aware error response for FastAPI"""
        try:
            from ..core.error_detection import ErrorResponseFactory

            # Determine status code based on error type
            status_code = 500
            if isinstance(error, ValueError):
                status_code = 400
            elif isinstance(error, PermissionError):
                status_code = 403
            elif isinstance(error, FileNotFoundError):
                status_code = 404

            # Use the new error response factory if we have request context
            if request:
                return ErrorResponseFactory.create_error_response(
                    error=error,
                    request=request,
                    route_info=route_info,
                    status_code=status_code
                )

            # Fallback to JSON response if no request context
            from fastapi.responses import JSONResponse
            error_data = {
                'error': str(error),
                'path': route_info.path,
                'type': type(error).__name__
            }
            return JSONResponse(content=error_data, status_code=status_code)

        except Exception as e:
            logger.error(f"Error creating error response: {e}")
            # Ultimate fallback to simple response
            from fastapi.responses import JSONResponse
            return JSONResponse(content={'error': 'Internal server error'}, status_code=500)
    
    def _get_json_response_class(self):
        """Get JSON response class for FastAPI"""
        try:
            from fastapi.responses import JSONResponse
            return JSONResponse
        except ImportError:
            return None


class FastAPIResponseTransformer:
    """Transforms responses for FastAPI compatibility"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def transform_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Transform response for FastAPI"""
        try:
            # If already a FastAPI response, return as-is
            if self._is_fastapi_response(response):
                return response
            
            # Transform based on route type
            if route_info.route_type == RouteType.JSON:
                return self._transform_json_response(response)
            else:
                return self._transform_http_response(response)
                
        except Exception as e:
            self.logger.error(f"Error transforming response: {e}")
            return response
    
    def _is_fastapi_response(self, response: Any) -> bool:
        """Check if response is already a FastAPI response"""
        try:
            response_types = ['Response', 'JSONResponse', 'HTMLResponse', 'PlainTextResponse']
            return any(resp_type in str(type(response)) for resp_type in response_types)
        except:
            return False
    
    def _transform_json_response(self, response: Any) -> Any:
        """Transform response for JSON routes"""
        try:
            from fastapi.responses import JSONResponse
            
            if isinstance(response, dict):
                return JSONResponse(content=response)
            elif isinstance(response, (list, tuple)):
                return JSONResponse(content=list(response))
            else:
                return JSONResponse(content={'result': response})
                
        except Exception as e:
            self.logger.error(f"Error transforming JSON response: {e}")
            from fastapi.responses import JSONResponse
            return JSONResponse(content={'error': 'Response transformation error'}, status_code=500)
    
    def _transform_http_response(self, response: Any) -> Any:
        """Transform response for HTTP routes"""
        try:
            from fastapi.responses import HTMLResponse, JSONResponse, PlainTextResponse
            
            if isinstance(response, str):
                if response.strip().startswith('<'):
                    return HTMLResponse(content=response)
                else:
                    return PlainTextResponse(content=response)
            elif isinstance(response, dict):
                return JSONResponse(content=response)
            else:
                return PlainTextResponse(content=str(response))
                
        except Exception as e:
            self.logger.error(f"Error transforming HTTP response: {e}")
            from fastapi.responses import PlainTextResponse
            return PlainTextResponse(content='Response transformation error', status_code=500)


class FastAPIIntegrationManager:
    """Manages FastAPI integration for routes"""
    
    def __init__(self):
        self.adapter = FastAPIRouteAdapter()
        self.transformer = FastAPIResponseTransformer()
        self.logger = get_logger(__name__)
    
    async def register_routes_with_app(self, app: Any, routes: Dict[str, List[RouteInfo]]) -> None:
        """Register routes with FastAPI app"""
        try:
            from fastapi import APIRouter
            
            # Create routers for different route types
            http_router = APIRouter()
            jsonrpc_router = APIRouter(prefix="/jsonrpc")
            
            registered_count = 0
            
            for path, route_list in routes.items():
                for route_info in route_list:
                    try:
                        # Adapt route for FastAPI
                        route_config = await self.adapter.adapt_route(route_info)
                        
                        # Register with appropriate router
                        if route_info.route_type == RouteType.JSON:
                            jsonrpc_router.add_api_route(**route_config)
                        else:
                            http_router.add_api_route(**route_config)
                        
                        registered_count += 1
                        
                    except Exception as e:
                        self.logger.error(f"Failed to register route {path}: {e}")
            
            # Include routers in app
            app.include_router(http_router)
            app.include_router(jsonrpc_router)
            
            self.logger.info(f"Registered {registered_count} routes with FastAPI")
            
        except Exception as e:
            self.logger.error(f"Error registering routes with FastAPI: {e}")
            raise


# Global integration manager
_integration_manager = FastAPIIntegrationManager()


def get_fastapi_integration_manager() -> FastAPIIntegrationManager:
    """Get the global FastAPI integration manager"""
    return _integration_manager
