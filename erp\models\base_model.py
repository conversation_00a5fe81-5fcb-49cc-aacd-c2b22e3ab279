"""
Base model class with core functionality
"""
import uuid
from datetime import datetime
from typing import Dict, Any

from ..fields import Char, Datetime, Many2One
from ..logging import get_logger
from ..api import depends
from .metaclass import ModelMeta


class BaseModel(metaclass=ModelMeta):
    """Base model class with database integration"""

    _name = None  # Model name (to be overridden in subclasses)
    _description = None  # Model description
    _table = None  # Database table name (defaults to _name with dots replaced by underscores)
    _auto_create_table = True  # Whether to auto-create database table
    _transient = False  # Whether this is a transient model
    _abstract = False  # Whether this is an abstract model
    _fields = {}  # Field definitions (populated by metaclass)

    # Common fields for all models
    id = Char(string='ID', required=True, readonly=True, default=lambda: str(uuid.uuid4()))
    name = Char(string='Name', required=True)
    display_name = Char(string='Display Name', compute='_compute_display_name', store=False)
    createAt = Datetime(string='Created At', readonly=True, default=lambda: datetime.now())
    updateAt = Datetime(string='Updated At', readonly=True, default=lambda: datetime.now())
    create_uid = Many2One('res.users', string='Created by', readonly=True)
    update_uid = Many2One('res.users', string='Last Updated by', readonly=True)
    __last_update = Datetime(string='Last Update', readonly=True, store=False,
                            help='Internal field for recordset cache purposes')

    def __init__(self, **kwargs):
        """Initialize model instance"""
        self._values = {}
        self._is_new_record = True
        self._logger = get_logger(f"{__name__}.{self._name or self.__class__.__name__}")
        
        # Set default values for all fields
        for field_name, field in self._fields.items():
            if field_name in kwargs:
                setattr(self, field_name, kwargs[field_name])
            else:
                default_value = field.get_default_value()
                if default_value is not None:
                    self._values[field_name] = default_value

    def __getattr__(self, name):
        """Get field value with support for relational fields"""
        if name in self._fields:
            field = self._fields[name]
            value = self._values.get(name)

            # Handle relational fields
            if hasattr(field, 'comodel_name'):
                from ..fields import Many2One, One2Many, Many2Many, One2One

                if isinstance(field, Many2One) or isinstance(field, One2One):
                    # Return a RecordSet for the related record
                    if value:
                        return self._get_related_record(field.comodel_name, value)
                    return None

                elif isinstance(field, (One2Many, Many2Many)):
                    # Return a RecordSet for related records
                    return self._get_related_records(field, name)

            return value
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def __setattr__(self, name, value):
        """Set field value with validation"""
        if name.startswith('_') or name in ('_values', '_is_new_record', '_logger'):
            super().__setattr__(name, value)
            return

        if name in self._fields:
            field = self._fields[name]
            # Validate the value
            validated_value = field.validate(value)
            self._values[name] = validated_value
        else:
            super().__setattr__(name, value)

    def _get_related_record(self, comodel_name, record_id):
        """Get a related record for Many2One/One2One fields"""

        # TODO: Implement proper model registry lookup
        # For now, return None to avoid mock implementations
        return None

    def _get_related_records(self, field, field_name):
        """Get related records for One2Many/Many2Many fields"""
        from .recordset import RecordSet

        # TODO: Implement proper database query for related records
        # For now, return empty RecordSet
        return RecordSet(None, [])

    @depends('name')
    def _compute_display_name(self):
        """Compute display name based on name field"""
        for record in self:
            record.display_name = record.name or 'New'

    @classmethod
    def _convert_data_to_db_columns(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert field names to database column names"""
        db_data = {}
        for field_name, value in data.items():
            if field_name in cls._fields:
                field = cls._fields[field_name]
                # Convert field name to database column name if needed
                db_column_name = field_name  # For now, use field name as-is
                db_data[db_column_name] = value
            else:
                db_data[field_name] = value
        return db_data

    def to_dict(self) -> Dict[str, Any]:
        """Convert record to dictionary"""
        return self._values.copy()

    def __repr__(self):
        name = self._values.get('name', 'Unknown')
        record_id = self._values.get('id', 'New')
        return f"<{self.__class__.__name__}({record_id}): {name}>"
