"""
HTTP routing system for ERP
Provides modular, extensible HTTP routing with Odoo-style decorators and JSON RPC 2.0 support
"""

# Core interfaces and types
from .interfaces import (
    IRouteRegistry, IRouteHandler, IRouteDiscovery, IRouteValidator,
    IMiddleware, IRouteAdapter, IRouteLifecycle, IHealthCheck,
    RouteInfo, RouteScope, RouteRegistrationError, RouteValidationError,
    RouteNotFoundError, MiddlewareError
)

# Metadata and enums
from .metadata import RouteType, HttpMethod, AuthType, RouteMetadata, create_route_metadata

# Decorators
from .decorators import route, systemRoute

# Core functionality
from .core import <PERSON>sonRpcHandler, JsonRpcError, JsonRpcRequest, JsonRpcResponse

# Authentication
from .auth import AuthType as AuthTypeEnum, require_auth

# Registries
from .registries import (
    SystemRouteRegistry, get_system_route_registry, reset_system_route_registry,
    DatabaseRouteRegistry, DatabaseRouteManager, get_database_route_manager
)

# Controllers
from .controllers import (
    BaseController, Controller, MinimalController, APIController,
    TemplateController, DatabaseController, ControllerRegistry, get_controller_registry
)

# Middleware
from .middleware import (
    MiddlewarePipeline, get_middleware_pipeline, BaseMiddleware,
    CorsMiddleware, RequestProcessingMiddleware, ResponseProcessingMiddleware
)
from .auth import AuthMiddleware

# Adapters
from .adapters import FastAPIRouteAdapter, FastAPIResponseTransformer, BaseRouteAdapter

# Services
from .services import (
    RouteDiscoveryService, RouteOrganizer, get_route_discovery_service, get_route_organizer,
    RouteValidator, get_route_validator,
    RouteHealthChecker, HealthStatus, get_health_checker,
    RouteHandlerFactory, RouteInfoFactory, get_route_handler_factory, get_route_info_factory
)

# Integration
from .integration import setup_http_routes



__all__ = [
    # Core interfaces
    'IRouteRegistry', 'IRouteHandler', 'IRouteDiscovery', 'IRouteValidator',
    'IMiddleware', 'IRouteAdapter', 'IRouteLifecycle', 'IHealthCheck',
    'RouteInfo', 'RouteScope',

    # Exceptions
    'RouteRegistrationError', 'RouteValidationError', 'RouteNotFoundError', 'MiddlewareError',

    # Metadata and enums
    'RouteType', 'HttpMethod', 'AuthType', 'RouteMetadata', 'create_route_metadata',

    # Decorators
    'route', 'systemRoute',

    # Registries
    'SystemRouteRegistry', 'get_system_route_registry', 'reset_system_route_registry',
    'DatabaseRouteRegistry', 'DatabaseRouteManager', 'get_database_route_manager',

    # Controllers
    'BaseController', 'Controller', 'MinimalController', 'APIController',
    'TemplateController', 'DatabaseController', 'ControllerRegistry', 'get_controller_registry',

    # Factories
    'RouteHandlerFactory', 'RouteInfoFactory', 'get_route_handler_factory', 'get_route_info_factory',

    # Middleware
    'MiddlewarePipeline', 'get_middleware_pipeline', 'BaseMiddleware',
    'AuthMiddleware', 'CorsMiddleware', 'RequestProcessingMiddleware', 'ResponseProcessingMiddleware',

    # Adapters
    'FastAPIRouteAdapter', 'FastAPIResponseTransformer', 'BaseRouteAdapter',

    # Services
    'RouteDiscoveryService', 'RouteOrganizer', 'get_route_discovery_service', 'get_route_organizer',
    'RouteValidator', 'get_route_validator',
    'RouteHealthChecker', 'HealthStatus', 'get_health_checker',

    # JSON RPC
    'JsonRpcHandler', 'JsonRpcError', 'JsonRpcRequest', 'JsonRpcResponse',

    # Authentication
    'require_auth',
]
