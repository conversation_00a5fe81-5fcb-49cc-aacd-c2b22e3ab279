"""
Core timezone utilities
Contains fundamental timezone operations and validation
"""
from typing import List, Optional
from datetime import datetime, timezone as dt_timezone

# Try to import zoneinfo (Python 3.9+), fallback to pytz
try:
    from zoneinfo import ZoneInfo, available_timezones
    ZONEINFO_AVAILABLE = True
except ImportError:
    try:
        import pytz
        ZONEINFO_AVAILABLE = False
    except ImportError:
        raise ImportError("Neither zoneinfo nor pytz is available. Please install pytz for Python < 3.9")


def get_fallback_timezones() -> List[str]:
    """
    Get fallback timezone list when zoneinfo/pytz is not available or has no data

    Returns:
        List of common timezone names
    """
    return [
        'UTC',
        'US/Eastern',
        'US/Central',
        'US/Mountain',
        'US/Pacific',
        'Europe/London',
        'Europe/Paris',
        'Europe/Berlin',
        'Europe/Rome',
        'Europe/Madrid',
        'Asia/Tokyo',
        'Asia/Shanghai',
        'Asia/Kolkata',
        'Asia/Dubai',
        'Australia/Sydney',
        'Australia/Melbourne',
        'America/New_York',
        'America/Chicago',
        'America/Denver',
        'America/Los_Angeles',
        'America/Toronto',
        'America/Mexico_City',
        'America/Sao_Paulo',
        'Africa/Cairo',
        'Africa/Johannesburg',
    ]


def get_all_timezones() -> List[str]:
    """
    Get all available timezone names

    Returns:
        List of timezone names sorted alphabetically
    """
    if ZONEINFO_AVAILABLE:
        zones = list(available_timezones())
        if not zones:
            # Fallback to hardcoded list if zoneinfo has no data
            return get_fallback_timezones()
        return sorted(zones)
    else:
        return sorted(list(pytz.all_timezones))


def get_common_timezones() -> List[str]:
    """
    Get commonly used timezone names

    Returns:
        List of common timezone names
    """
    if ZONEINFO_AVAILABLE:
        # For zoneinfo, we'll use our fallback list
        common = get_fallback_timezones()
        # Filter to only include timezones that actually exist
        all_zones = get_all_timezones()
        if all_zones:
            return [tz for tz in common if tz in all_zones]
        else:
            # If no zones available, return the fallback list
            return common
    else:
        try:
            return sorted(list(pytz.common_timezones))
        except:
            return get_fallback_timezones()


def validate_timezone(timezone_name: str) -> bool:
    """
    Validate if a timezone name is valid
    
    Args:
        timezone_name: Timezone name to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not timezone_name:
        return False
        
    try:
        if ZONEINFO_AVAILABLE:
            ZoneInfo(timezone_name)
        else:
            pytz.timezone(timezone_name)
        return True
    except Exception:
        return False


def get_timezone_offset(timezone_name: str, dt: Optional[datetime] = None) -> Optional[str]:
    """
    Get timezone offset for a given timezone and datetime
    
    Args:
        timezone_name: Timezone name
        dt: Datetime to get offset for (defaults to now)
        
    Returns:
        Offset string in format '+/-HH:MM' or None if invalid
    """
    if not validate_timezone(timezone_name):
        return None
        
    if dt is None:
        dt = datetime.now()
        
    try:
        if ZONEINFO_AVAILABLE:
            tz = ZoneInfo(timezone_name)
            localized_dt = dt.replace(tzinfo=tz)
        else:
            tz = pytz.timezone(timezone_name)
            localized_dt = tz.localize(dt)
            
        offset = localized_dt.strftime('%z')
        if offset:
            return f"{offset[:3]}:{offset[3:]}"
    except Exception:
        pass
        
    return None


def convert_timezone(dt: datetime, from_tz: str, to_tz: str) -> Optional[datetime]:
    """
    Convert datetime from one timezone to another
    
    Args:
        dt: Datetime to convert
        from_tz: Source timezone name
        to_tz: Target timezone name
        
    Returns:
        Converted datetime or None if conversion fails
    """
    if not validate_timezone(from_tz) or not validate_timezone(to_tz):
        return None
        
    try:
        if ZONEINFO_AVAILABLE:
            from_zone = ZoneInfo(from_tz)
            to_zone = ZoneInfo(to_tz)
            
            # If datetime is naive, assume it's in from_tz
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=from_zone)
            
            return dt.astimezone(to_zone)
        else:
            from_zone = pytz.timezone(from_tz)
            to_zone = pytz.timezone(to_tz)
            
            # If datetime is naive, localize it to from_tz
            if dt.tzinfo is None:
                dt = from_zone.localize(dt)
            
            return dt.astimezone(to_zone)
    except Exception:
        return None


def get_user_timezone_display(timezone_name: str) -> str:
    """
    Get a user-friendly display string for a timezone
    
    Args:
        timezone_name: Timezone name
        
    Returns:
        User-friendly timezone display string
    """
    if not timezone_name:
        return 'UTC'
        
    display_name = timezone_name.replace('_', ' ')
    offset = get_timezone_offset(timezone_name)
    
    if offset:
        return f"{display_name} (UTC{offset})"
    
    return display_name


def get_current_utc_time() -> datetime:
    """
    Get current UTC time
    
    Returns:
        Current datetime in UTC
    """
    return datetime.now(dt_timezone.utc)


def localize_datetime(dt: datetime, timezone_name: str) -> Optional[datetime]:
    """
    Localize a naive datetime to a specific timezone
    
    Args:
        dt: Naive datetime to localize
        timezone_name: Target timezone name
        
    Returns:
        Localized datetime or None if failed
    """
    if not validate_timezone(timezone_name):
        return None
    
    if dt.tzinfo is not None:
        # Already timezone-aware
        return dt
        
    try:
        if ZONEINFO_AVAILABLE:
            tz = ZoneInfo(timezone_name)
            return dt.replace(tzinfo=tz)
        else:
            tz = pytz.timezone(timezone_name)
            return tz.localize(dt)
    except Exception:
        return None


def normalize_timezone_name(timezone_name: str) -> Optional[str]:
    """
    Normalize timezone name to standard format
    
    Args:
        timezone_name: Timezone name to normalize
        
    Returns:
        Normalized timezone name or None if invalid
    """
    if not timezone_name:
        return None
    
    # Handle common aliases
    aliases = {
        'utc': 'UTC',
        'gmt': 'GMT',
        'est': 'US/Eastern',
        'cst': 'US/Central',
        'mst': 'US/Mountain',
        'pst': 'US/Pacific',
    }
    
    normalized = aliases.get(timezone_name.lower(), timezone_name)
    
    if validate_timezone(normalized):
        return normalized
    
    return None


def get_timezone_info(timezone_name: str) -> Optional[dict]:
    """
    Get comprehensive information about a timezone
    
    Args:
        timezone_name: Timezone name
        
    Returns:
        Dictionary with timezone information or None if invalid
    """
    if not validate_timezone(timezone_name):
        return None
    
    try:
        now = datetime.now()
        offset = get_timezone_offset(timezone_name, now)
        display_name = get_user_timezone_display(timezone_name)
        
        return {
            'name': timezone_name,
            'display_name': display_name,
            'offset': offset,
            'is_dst': _is_dst_active(timezone_name, now),
            'valid': True
        }
    except Exception:
        return None


def _is_dst_active(timezone_name: str, dt: datetime) -> bool:
    """
    Check if daylight saving time is active for a timezone at a given datetime
    
    Args:
        timezone_name: Timezone name
        dt: Datetime to check
        
    Returns:
        True if DST is active, False otherwise
    """
    try:
        if ZONEINFO_AVAILABLE:
            tz = ZoneInfo(timezone_name)
            localized_dt = dt.replace(tzinfo=tz)
            return localized_dt.dst() is not None and localized_dt.dst().total_seconds() > 0
        else:
            tz = pytz.timezone(timezone_name)
            localized_dt = tz.localize(dt)
            return localized_dt.dst().total_seconds() > 0
    except Exception:
        return False
