"""
Database schema operations
"""
import asyncpg
from typing import Dict, List, Any
from ..connection.pool import <PERSON><PERSON>ool
from ..connection.sql_logger import SQLLogger


class SchemaOperations:
    """Database schema management operations"""

    def __init__(self, connection_pool: ConnectionPool, sql_logger: SQLLogger):
        self.pool = connection_pool
        self.sql_logger = sql_logger

    async def execute(self, query: str, *args) -> str:
        """Execute a query and return status"""
        async with self.pool.acquire_connection() as conn:
            result = await conn.execute(query, *args)
        self.sql_logger.log_query(query, args, result_info=f"Status: {result}")
        return result

    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        async with self.pool.acquire_connection() as conn:
            result = await conn.fetchval(query, *args)
        self.sql_logger.log_query(query, args, result_info=f"Value: {result}")
        return result

    async def fetch(self, query: str, *args) -> List[asyncpg.Record]:
        """Fetch multiple rows"""
        async with self.pool.acquire_connection() as conn:
            result = await conn.fetch(query, *args)
        self.sql_logger.log_query(query, args, result_info=f"Rows: {len(result)}")
        return result

    async def create_table(self, table_name: str, columns: Dict[str, str]):
        """Create a table with specified columns"""
        column_definitions = []
        for column_name, column_type in columns.items():
            column_definitions.append(f"{column_name} {column_type}")
        
        query = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {', '.join(column_definitions)}
            )
        """
        
        await self.execute(query)

    async def drop_table(self, table_name: str):
        """Drop a table"""
        query = f"DROP TABLE IF EXISTS {table_name}"
        await self.execute(query)

    async def table_exists(self, table_name: str) -> bool:
        """Check if a table exists"""
        query = """
            SELECT 1 FROM information_schema.tables
            WHERE table_name = $1 AND table_schema = 'public'
            LIMIT 1
        """
        result = await self.fetchval(query, table_name)
        return result is not None

    async def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a table"""
        query = """
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = $1 AND table_schema = 'public'
            ORDER BY ordinal_position
        """
        rows = await self.fetch(query, table_name)
        return [dict(row) for row in rows]
