"""
Test suite for Char field functionality

This module tests:
- Char field initialization and size parameter
- Size validation and handling
- SQL type generation
"""
from erp.fields import Char


class TestCharField:
    """Test Char field functionality"""

    def test_char_field_initialization(self):
        """Test Char field initialization"""
        field = Char(string="Name", size=100, required=True)

        assert field.string == "Name"
        assert field.size == 100
        assert field.required is True

    def test_char_field_validation_size(self):
        """Test Char field size validation"""
        field = Char(size=10)

        # Should pass for string within size limit
        result = field.validate("short")
        assert result == "short"

        # Should handle string exceeding size limit
        long_string = "a" * 15
        result = field.validate(long_string)
        # Implementation may truncate or raise error - test based on actual behavior
        assert isinstance(result, str)

    def test_char_field_sql_type(self):
        """Test Char field SQL type generation"""
        field = Char(size=100)
        sql_type = field.get_sql_type()

        assert "VARCHAR" in sql_type or "CHAR" in sql_type
