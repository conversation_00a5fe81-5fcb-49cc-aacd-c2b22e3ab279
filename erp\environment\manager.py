"""
Environment manager for creating and managing environments
"""
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from .core import Environment
from .cursor import DatabaseCursor
from ..context import ContextManager
from ..logging import get_logger


class EnvironmentManager:
    """
    Manager for creating and managing environments
    """
    
    @classmethod
    async def create_environment(
        cls,
        db_name: str,
        uid: int,
        context: Dict[str, Any] = None
    ) -> Environment:
        """Create a new environment"""
        from ..database.registry import DatabaseRegistry
        from ..database.memory import DatabaseFilterProcessor
        
        # Get logger for manager-level logging
        logger = get_logger(f"{__name__}.EnvironmentManager")
        logger.debug(f"Creating new environment for DB: {db_name}, UID: {uid}")
        
        # Check if memory registry should be created/used for this database
        memory_registry = await DatabaseFilterProcessor.ensure_registry_for_environment(db_name)
        
        # Get database manager
        db_manager = await DatabaseRegistry.get_database(db_name)
        
        # Create cursor
        cr = DatabaseCursor(db_manager, db_name)
        
        # Create environment with memory registry if applicable
        env = Environment(cr, uid, context, memory_registry)
        
        # Register environment with memory registry if available
        if memory_registry:
            await memory_registry.register_environment(env)
            logger.info(f"Environment {env.uuid} registered with memory registry for {db_name}")
        
        # Log successful creation at manager level
        logger.info(f"Environment {env.uuid} successfully created via EnvironmentManager")
        
        return env
    
    @classmethod
    def get_current_environment(cls) -> Optional[Environment]:
        """Get current environment from context"""
        return ContextManager.get_environment()
    
    @classmethod
    async def with_environment(
        cls, 
        db_name: str, 
        uid: int, 
        context: Dict[str, Any] = None
    ):
        """Context manager for running code with specific environment"""
        env = await cls.create_environment(db_name, uid, context)
        return ContextManager.environment.run(env)
    
    @classmethod
    @asynccontextmanager
    async def transaction(cls, env: Environment = None):
        """Context manager for database transactions"""
        if env is None:
            env = cls.get_current_environment()
            if env is None:
                raise RuntimeError("No environment found in context")
        
        async with env.cr._get_connection():
            await env.cr.begin()
            try:
                yield env
                await env.cr.commit()
            except Exception:
                await env.cr.rollback()
                raise
