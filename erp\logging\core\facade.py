"""
Logging Facade Module
Simplified interface for common logging operations
"""
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path

from ..config import LoggingConfig
from .core import get_logging_system, LoggingSystem
from ..monitoring import PerformanceMonitor
from ..coordination import get_logging_coordinator, LoggingCoordinator
from ..middleware import get_global_pipeline, LoggingPipeline
from ..utils import log_performance, log_structured, LogContext
from ..utils import log_method_calls, log_database_operations, log_api_calls, log_security_events


class LoggingFacade:
    """Simplified facade for logging operations"""
    
    def __init__(self):
        self._system: Optional[LoggingSystem] = None
        self._monitor: Optional[PerformanceMonitor] = None
        self._coordinator: Optional[LoggingCoordinator] = None
        self._pipeline: Optional[LoggingPipeline] = None
    
    @property
    def system(self) -> LoggingSystem:
        """Get logging system instance"""
        if self._system is None:
            self._system = get_logging_system()
        return self._system
    
    @property
    def monitor(self) -> PerformanceMonitor:
        """Get performance monitor instance"""
        if self._monitor is None:
            self._monitor = PerformanceMonitor()
        return self._monitor
    
    @property
    def coordinator(self) -> LoggingCoordinator:
        """Get logging coordinator instance"""
        if self._coordinator is None:
            self._coordinator = get_logging_coordinator()
        return self._coordinator
    
    @property
    def pipeline(self) -> LoggingPipeline:
        """Get logging pipeline instance"""
        if self._pipeline is None:
            self._pipeline = get_global_pipeline()
        return self._pipeline
    
    def initialize(self, config: Optional[Union[LoggingConfig, Dict[str, Any], str, Path]] = None):
        """
        Initialize logging system with various configuration sources
        
        Args:
            config: Configuration as LoggingConfig object, dict, file path, or None for defaults
        """
        if config is None:
            self.system.initialize()
        elif isinstance(config, LoggingConfig):
            self.system.initialize(config)
        elif isinstance(config, dict):
            self.system.initialize_from_dict(config)
        elif isinstance(config, (str, Path)):
            self.system.initialize_from_file(str(config))
        else:
            raise ValueError(f"Unsupported configuration type: {type(config)}")
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger by name"""
        return self.system.get_logger(name)
    
    def start_monitoring(self, interval: float = 60.0):
        """Start performance monitoring"""
        self.monitor.interval = interval
        self.monitor.start_monitoring()
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitor.stop_monitoring()
    
    def record_request(self, response_time: float, is_error: bool = False):
        """Record a request for monitoring"""
        self.monitor.get_app_collector().record_request(response_time, is_error)
    
    def increment_connections(self):
        """Increment active connection count"""
        self.monitor.get_app_collector().increment_connections()
    
    def decrement_connections(self):
        """Decrement active connection count"""
        self.monitor.get_app_collector().decrement_connections()
    
    def operation_context(self, operation_id: str, **context):
        """Context manager for operation tracking"""
        return self.coordinator.operation_context(operation_id, **context)
    
    def quiet_operation(self, *logger_names, level: int = logging.WARNING):
        """Context manager for quiet operations"""
        return self.coordinator.quiet_operation(*logger_names, level=level)
    
    def set_rate_limit(self, logger_name: str, max_messages: int, time_window: int):
        """Set rate limit for a logger"""
        self.coordinator.set_rate_limit(logger_name, max_messages, time_window)
    
    def get_performance_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """Get performance statistics"""
        return self.monitor.get_storage().get_summary_stats(minutes)
    
    def get_coordination_stats(self) -> Dict[str, Any]:
        """Get coordination statistics"""
        return self.coordinator.get_coordination_stats()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            'initialized': self.system.is_initialized(),
            'monitoring_active': self.monitor.monitoring if self._monitor else False,
            'performance_stats': self.get_performance_stats(60),
            'coordination_stats': self.get_coordination_stats(),
            'pipeline_enabled': self.pipeline.enabled,
            'middleware_count': len(self.pipeline.middleware)
        }
    
    def shutdown(self):
        """Shutdown all logging components"""
        if self._monitor and self._monitor.monitoring:
            self._monitor.stop_monitoring()
        
        if self._pipeline:
            self._pipeline.shutdown()
        
        if self._system:
            self._system.shutdown()


# Global facade instance
_facade: Optional[LoggingFacade] = None


def get_logging_facade() -> LoggingFacade:
    """Get the global logging facade instance"""
    global _facade
    if _facade is None:
        _facade = LoggingFacade()
    return _facade


# Convenience functions that delegate to the facade
def initialize_logging(config: Optional[Union[LoggingConfig, Dict[str, Any], str, Path]] = None):
    """Initialize logging system"""
    get_logging_facade().initialize(config)


def get_logger(name: str) -> logging.Logger:
    """Get a logger by name"""
    return get_logging_facade().get_logger(name)


def start_performance_monitoring(interval: float = 60.0):
    """Start performance monitoring"""
    get_logging_facade().start_monitoring(interval)


def stop_performance_monitoring():
    """Stop performance monitoring"""
    get_logging_facade().stop_monitoring()


def record_request(response_time: float, is_error: bool = False):
    """Record a request for monitoring"""
    get_logging_facade().record_request(response_time, is_error)


def operation_context(operation_id: str, **context):
    """Context manager for operation tracking"""
    return get_logging_facade().operation_context(operation_id, **context)


def quiet_operation(*logger_names, level: int = logging.WARNING):
    """Context manager for quiet operations"""
    return get_logging_facade().quiet_operation(*logger_names, level=level)


def get_system_status() -> Dict[str, Any]:
    """Get overall logging system status"""
    return get_logging_facade().get_system_status()


def shutdown_logging():
    """Shutdown logging system"""
    get_logging_facade().shutdown()





# Export utility functions and decorators for convenience
__all__ = [
    # Main facade
    'LoggingFacade',
    'get_logging_facade',
    
    # Convenience functions
    'initialize_logging',
    'get_logger',
    'start_performance_monitoring',
    'stop_performance_monitoring',
    'record_request',
    'operation_context',
    'quiet_operation',
    'get_system_status',
    'shutdown_logging',

    
    # Utility functions
    'log_performance',
    'log_structured',
    'LogContext',
    
    # Decorators
    'log_method_calls',
    'log_database_operations',
    'log_api_calls',
    'log_security_events',
]
