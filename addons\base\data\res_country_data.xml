<?xml version="1.0" encoding="utf-8"?>
<data>
    <!-- Country Data -->
    
    <!-- India -->
    <record id="country_in" model="res.country">
        <field name="name">India</field>
        <field name="code">IN</field>
        <field name="phone_code">91</field>
        <field name="address_format">%(name)s
%(street)s
%(city)s - %(zip)s
%(state_name)s
%(country_name)s</field>
    </record>

    <!-- United States -->
    <record id="country_us" model="res.country">
        <field name="name">United States</field>
        <field name="code">US</field>
        <field name="phone_code">1</field>
        <field name="address_format">%(name)s
%(street)s
%(city)s, %(state_code)s %(zip)s
%(country_name)s</field>
    </record>

    <!-- United Kingdom -->
    <record id="country_gb" model="res.country">
        <field name="name">United Kingdom</field>
        <field name="code">GB</field>
        <field name="phone_code">44</field>
        <field name="address_format">%(name)s
%(street)s
%(city)s %(zip)s
%(country_name)s</field>
    </record>

    <!-- Canada -->
    <record id="country_ca" model="res.country">
        <field name="name">Canada</field>
        <field name="code">CA</field>
        <field name="phone_code">1</field>
        <field name="address_format">%(name)s
%(street)s
%(city)s, %(state_code)s %(zip)s
%(country_name)s</field>
    </record>

    <!-- Australia -->
    <record id="country_au" model="res.country">
        <field name="name">Australia</field>
        <field name="code">AU</field>
        <field name="phone_code">61</field>
        <field name="address_format">%(name)s
%(street)s
%(city)s %(state_code)s %(zip)s
%(country_name)s</field>
    </record>

    <!-- Germany -->
    <record id="country_de" model="res.country">
        <field name="name">Germany</field>
        <field name="code">DE</field>
        <field name="phone_code">49</field>
        <field name="address_format">%(name)s
%(street)s
%(zip)s %(city)s
%(country_name)s</field>
    </record>

    <!-- France -->
    <record id="country_fr" model="res.country">
        <field name="name">France</field>
        <field name="code">FR</field>
        <field name="phone_code">33</field>
        <field name="address_format">%(name)s
%(street)s
%(zip)s %(city)s
%(country_name)s</field>
    </record>

    <!-- Japan -->
    <record id="country_jp" model="res.country">
        <field name="name">Japan</field>
        <field name="code">JP</field>
        <field name="phone_code">81</field>
        <field name="address_format">%(zip)s
%(state_name)s %(city)s
%(street)s
%(name)s
%(country_name)s</field>
    </record>

    <!-- China -->
    <record id="country_cn" model="res.country">
        <field name="name">China</field>
        <field name="code">CN</field>
        <field name="phone_code">86</field>
        <field name="address_format">%(country_name)s
%(state_name)s %(city)s
%(street)s
%(name)s
%(zip)s</field>
    </record>

    <!-- Brazil -->
    <record id="country_br" model="res.country">
        <field name="name">Brazil</field>
        <field name="code">BR</field>
        <field name="phone_code">55</field>
        <field name="address_format">%(name)s
%(street)s
%(city)s - %(state_code)s
%(zip)s
%(country_name)s</field>
    </record>

    <!-- Singapore -->
    <record id="country_sg" model="res.country">
        <field name="name">Singapore</field>
        <field name="code">SG</field>
        <field name="phone_code">65</field>
        <field name="address_format">%(name)s
%(street)s
Singapore %(zip)s</field>
    </record>

    <!-- United Arab Emirates -->
    <record id="country_ae" model="res.country">
        <field name="name">United Arab Emirates</field>
        <field name="code">AE</field>
        <field name="phone_code">971</field>
        <field name="address_format">%(name)s
%(street)s
%(city)s
%(country_name)s</field>
    </record>

</data>
