[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests addons
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
markers =
    unit: Unit tests
    integration: Integration tests
    functional: Functional tests
    slow: Slow running tests
    database: Tests requiring database
    addon: Addon-specific tests
    api: API endpoint tests
    model: Model tests
    field: Field tests
    route: Route tests
    registry: Registry tests
    memory: Memory registry tests
    auth: Authentication tests
    security: Security tests
    performance: Performance tests
    base: Base addon tests
    external: Tests requiring external dependencies


    ignore::PendingDeprecationWarning
    ignore::pytest.PytestCollectionWarning
    error::UserWarning
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
