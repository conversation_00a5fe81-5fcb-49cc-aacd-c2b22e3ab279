"""
Test suite for the new modular structure of erp/models and erp/fields

This module tests:
- Import functionality of all new modular components
- Functionality preservation after reorganization
- No regressions in existing functionality
- Proper separation of concerns
"""


class TestModularModelsStructure:
    """Test the new modular structure of erp/models"""

    def test_metaclass_import_and_functionality(self):
        """Test that ModelMeta can be imported and works correctly"""
        from erp.models.metaclass import ModelMeta
        from erp.fields import Char, Integer
        
        # Test that metaclass properly handles field definitions
        class TestModel(metaclass=ModelMeta):
            name = Char(string='Name')
            value = Integer(string='Value')
        
        # Check that fields are properly collected
        assert hasattr(TestModel, '_fields')
        assert 'name' in TestModel._fields
        assert 'value' in TestModel._fields
        assert isinstance(TestModel._fields['name'], Char)
        assert isinstance(TestModel._fields['value'], Integer)

    def test_base_model_import_and_functionality(self):
        """Test that BaseModel can be imported and works correctly"""
        from erp.models.base_model import BaseModel
        from erp.fields import Char
        
        class TestModel(BaseModel):
            _name = 'test.model'
            test_field = Char(string='Test Field')
        
        # Test instance creation
        instance = TestModel(name='Test', test_field='Test Value')
        assert instance._values['name'] == 'Test'
        assert instance._values['test_field'] == 'Test Value'
        assert hasattr(instance, 'id')
        assert hasattr(instance, 'createAt')

    def test_model_types_import_and_functionality(self):
        """Test that all model types can be imported and work correctly"""
        from erp.models.model_types import AbstractModel, TransientModel, Model
        
        # Test AbstractModel
        class TestAbstractModel(AbstractModel):
            _name = 'test.abstract'
        
        assert TestAbstractModel._abstract is True
        assert TestAbstractModel._auto_create_table is False
        
        # Test TransientModel
        class TestTransientModel(TransientModel):
            _name = 'test.transient'
        
        assert TestTransientModel._transient is True
        assert TestTransientModel._auto_create_table is True
        assert hasattr(TestTransientModel, '_transient_vacuum')
        
        # Test Model
        class TestStandardModel(Model):
            _name = 'test.standard'
        
        assert TestStandardModel._auto_create_table is True
        assert TestStandardModel._transient is False

    def test_crud_operations_mixin(self):
        """Test that CRUDOperationsMixin provides expected methods"""
        from erp.models.crud_operations import CRUDOperationsMixin
        
        # Check that all expected methods exist
        assert hasattr(CRUDOperationsMixin, 'create')
        assert hasattr(CRUDOperationsMixin, 'write')
        assert hasattr(CRUDOperationsMixin, 'unlink')
        assert hasattr(CRUDOperationsMixin, 'search')
        assert hasattr(CRUDOperationsMixin, 'browse')

    def test_lifecycle_manager_import_and_functionality(self):
        """Test that ModelRegistry (lifecycle manager) works correctly"""
        from erp.models.lifecycle_manager import ModelRegistry, create_addon_model_registry
        
        # Test registry creation
        registry = ModelRegistry('test_addon')
        assert registry.addon_name == 'test_addon'
        assert hasattr(registry, 'discover_models')
        assert hasattr(registry, 'all')
        assert hasattr(registry, 'get')
        
        # Test factory function
        registry2 = create_addon_model_registry('test_addon2')
        assert isinstance(registry2, ModelRegistry)
        assert registry2.addon_name == 'test_addon2'

    def test_model_discovery_functionality(self):
        """Test that ModelDiscovery works correctly"""
        from erp.models.model_discovery import ModelDiscovery
        
        discovery = ModelDiscovery('test_addon')
        assert discovery.addon_name == 'test_addon'
        assert hasattr(discovery, 'discover_addon_models')

    def test_model_registration_functionality(self):
        """Test that ModelRegistration works correctly"""
        from erp.models.model_registration import ModelRegistration
        from erp.models.base_model import BaseModel
        
        registration = ModelRegistration('test_addon')
        assert registration.addon_name == 'test_addon'
        
        # Test model registration
        class TestModel(BaseModel):
            _name = 'test.model'
        
        models = {'test.model': TestModel}
        registration.register_models(models)
        
        assert registration.has_models()
        assert registration.get_model_count() == 1
        assert registration.get_model('test.model') == TestModel
        assert 'test.model' in registration.get_model_names()


class TestModularFieldsStructure:
    """Test the new modular structure of erp/fields"""

    def test_base_field_classes_import(self):
        """Test that base field classes can be imported"""
        from erp.fields.base import Field, RelationalField
        
        # Test Field class
        field = Field(string='Test Field')
        assert field.string == 'Test Field'
        assert hasattr(field, 'validate')
        assert hasattr(field, 'get_sql_type')
        
        # Test RelationalField class
        rel_field = RelationalField('test.model')
        assert rel_field.comodel_name == 'test.model'

    def test_many2one_field_import_and_functionality(self):
        """Test Many2One field import and functionality"""
        from erp.fields.many2one import Many2One
        
        field = Many2One('res.partner', ondelete='cascade')
        assert field.comodel_name == 'res.partner'
        assert field.ondelete == 'cascade'
        assert field.get_sql_type() == 'UUID'

    def test_one2many_field_import_and_functionality(self):
        """Test One2Many field import and functionality"""
        from erp.fields.one2many import One2Many
        
        field = One2Many('res.partner', 'parent_id')
        assert field.comodel_name == 'res.partner'
        assert field.inverse_name == 'parent_id'
        assert field.store is False
        assert field.get_sql_type() is None

    def test_many2many_field_import_and_functionality(self):
        """Test Many2Many field import and functionality"""
        from erp.fields.many2many import Many2Many
        
        field = Many2Many('res.partner')
        assert field.comodel_name == 'res.partner'
        assert field.store is False
        assert field.get_sql_type() is None
        assert hasattr(field, 'get_relation_table_name')
        assert hasattr(field, 'get_intersection_table_schema')

    def test_one2one_field_import_and_functionality(self):
        """Test One2One field import and functionality"""
        from erp.fields.one2one import One2One
        
        field = One2One('res.partner')
        assert field.comodel_name == 'res.partner'
        assert field.unique is True
        assert hasattr(field, 'get_sql_constraints')

    def test_related_field_import_and_functionality(self):
        """Test Related field import and functionality"""
        from erp.fields.related import Related
        
        field = Related('partner_id.name')
        assert field.related == 'partner_id.name'
        assert field.related_path == ['partner_id', 'name']
        assert field.store is False
        assert field.readonly is True

    def test_reference_field_import_and_functionality(self):
        """Test Reference field import and functionality"""
        from erp.fields.reference import Reference
        
        selection = [('res.partner', 'Partner'), ('res.user', 'User')]
        field = Reference(selection=selection)
        assert field.selection == selection
        assert field.get_sql_type() == 'VARCHAR(255)'

    def test_fields_init_imports_all_modules(self):
        """Test that fields __init__.py imports from all new modules"""
        from erp.fields import (
            Many2One, One2Many, Many2Many, One2One, Related, Reference
        )
        
        # Test that all classes are available
        assert Many2One is not None
        assert One2Many is not None
        assert Many2Many is not None
        assert One2One is not None
        assert Related is not None
        assert Reference is not None


class TestBackwardCompatibility:
    """Test that the reorganization maintains backward compatibility"""

    def test_models_init_exports_all_classes(self):
        """Test that erp.models still exports all expected classes"""
        from erp.models import BaseModel, AbstractModel, TransientModel, Model
        from erp.models import RecordSet, ModelRegistry
        
        # All classes should be importable
        assert BaseModel is not None
        assert AbstractModel is not None
        assert TransientModel is not None
        assert Model is not None
        assert RecordSet is not None
        assert ModelRegistry is not None

    def test_fields_init_exports_all_classes(self):
        """Test that erp.fields still exports all expected classes"""
        from erp.fields import (
            Field, FieldValidationError, RelationalField,
            Char, Text, Integer, Float, Boolean, Date, Datetime, Selection,
            Binary, Json, Html,
            Many2One, One2Many, Many2Many, One2One, Related, Reference,
            Command
        )
        
        # All classes should be importable
        assert all([
            Field, FieldValidationError, RelationalField,
            Char, Text, Integer, Float, Boolean, Date, Datetime, Selection,
            Binary, Json, Html,
            Many2One, One2Many, Many2Many, One2One, Related, Reference,
            Command
        ])

    def test_existing_model_creation_still_works(self):
        """Test that existing model creation patterns still work"""
        from erp.models import Model
        from erp.fields import Char, Integer, Many2One
        
        class TestModel(Model):
            _name = 'test.model'
            _description = 'Test Model'
            
            name = Char(string='Name', required=True)
            value = Integer(string='Value')
            partner_id = Many2One('res.partner', string='Partner')
        
        # Test that model can be instantiated
        instance = TestModel(name='Test', value=42)
        assert instance.name == 'Test'
        assert instance.value == 42
