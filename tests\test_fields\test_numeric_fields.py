"""
Test suite for numeric field functionality

This module tests:
- Integer field functionality
- Boolean field functionality
- Numeric validation and type conversion
"""
from erp.fields import Integer, Boolean


class TestIntegerField:
    """Test Integer field functionality"""

    def test_integer_field_initialization(self):
        """Test Integer field initialization"""
        field = Integer(string="Count", default=0)

        assert field.string == "Count"
        assert field.default == 0

    def test_integer_field_validation(self):
        """Test Integer field validation"""
        field = Integer()

        # Should pass for integer values
        assert field.validate(42) == 42
        assert field.validate(0) == 0
        assert field.validate(-10) == -10

        # Should handle string numbers
        result = field.validate("123")
        assert result == 123 or result == "123"  # Depends on implementation


class TestBooleanField:
    """Test Boolean field functionality"""

    def test_boolean_field_initialization(self):
        """Test Boolean field initialization"""
        field = Boolean(string="Active", default=True)

        assert field.string == "Active"
        assert field.default is True

    def test_boolean_field_validation(self):
        """Test Boolean field validation"""
        field = Boolean()

        # Should pass for boolean values
        assert field.validate(True) is True
        assert field.validate(False) is False

        # Should handle truthy/falsy values
        result = field.validate(1)
        assert result is True or result == 1

        result = field.validate(0)
        assert result is False or result == 0
