"""
Field definitions for ERP models - Odoo-like field system

This package provides a comprehensive field system for ERP models with support for:
- Basic field types (Char, Text, Integer, Float, Boolean, Date, Datetime, Selection)
- Specialized field types (Binary, <PERSON><PERSON>, Html)
- Relational field types (Many2One, One2Many, Many2Many, One2One, Related, Reference)
- Command operations for relational fields
"""

# Import base classes
from .base import Field, FieldValidationError, RelationalField

# Import basic field types
from .basic import (
    Char, Text, Integer, Float, Boolean, Date, Datetime, Selection
)

# Import specialized field types
from .specialized import Binary, Json, Html

# Import relational field types
from .many2one import Many2One
from .one2many import One2Many
from .many2many import Many2Many
from .one2one import One2One
from .related import Related
from .reference import Reference

# Import command utilities
from .commands import (
    Command, get_intersection_table_name, validate_relational_command,
    check_relational_integrity
)

# Export all field types for easy importing
__all__ = [
    # Base classes
    'Field', 'FieldValidationError', 'RelationalField',

    # Basic field types
    'Char', 'Text', 'Integer', 'Float', 'Boolean', 'Date', 'Datetime',
    'Selection',

    # Specialized field types
    'Binary', 'Json', 'Html',

    # Relational field types
    'Many2One', 'One2Many', 'Many2Many', 'One2One', 'Related', 'Reference',

    # Utility classes and functions
    'Command', 'get_intersection_table_name', 'validate_relational_command',
    'check_relational_integrity'
]



"""
Available Field Types Summary:

Basic Fields:
- Char: Character field with optional size limit
- Text: Text field for longer content
- Integer: Integer field with optional min/max values
- Float: Float field with optional precision and min/max values
- Boolean: Boolean field with automatic conversion
- Date: Date field with automatic parsing
- Datetime: Datetime field with automatic parsing
- Selection: Selection field with predefined choices

Specialized Fields:
- Binary: Binary field for file storage
- Json: JSON field for structured data
- Html: HTML field with optional sanitization

Relational Fields:
- Many2One: Many-to-one relationship (foreign key)
- One2Many: One-to-many relationship (inverse of Many2One)
- Many2Many: Many-to-many relationship with intersection table
- One2One: One-to-one relationship (unique foreign key)
- Related: Computed field that follows relationships
- Reference: Reference field that can point to any model

Command Operations for Relational Fields:
- Command.CREATE: Create new related record
- Command.UPDATE: Update existing related record
- Command.DELETE: Delete related record
- Command.UNLINK: Unlink related record (don't delete)
- Command.LINK: Link existing record
- Command.CLEAR: Clear all links
- Command.SET: Replace all links with given IDs

Usage Examples:

class User(BaseModel):
    _name = 'res.users'

    name = Char(string='Name', required=True, size=100)
    email = Char(string='Email', size=255)
    login = Char(string='Login', required=True, size=64)
    active = Boolean(string='Active', default=True)
    groups_id = Many2Many('res.groups', string='Groups')

class Group(BaseModel):
    _name = 'res.groups'

    name = Char(string='Name', required=True)
    users = Many2Many('res.users', string='Users')
"""
