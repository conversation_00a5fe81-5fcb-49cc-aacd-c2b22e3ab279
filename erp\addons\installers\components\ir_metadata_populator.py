"""
IR Metadata Population Component

This module provides a reusable component for populating IR metadata
for addon models. It can be used by various installers to register
models and fields in the IR tables.
"""

import uuid
from datetime import datetime
from typing import Dict, Any, TYPE_CHECKING
from ....logging import get_logger

if TYPE_CHECKING:
    from ....database.manager import DatabaseManager


class IRMetadataPopulator:
    """
    Reusable component for populating IR metadata for addon models.
    
    This component handles:
    - Model registration in ir.model table
    - Field registration in ir.model.fields table
    - Schema extraction from model classes
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    async def populate_addon_ir_metadata(self, db_manager: 'DatabaseManager', addon_name: str, discovered_models: Dict[str, Any]) -> dict:
        """
        Populate IR metadata for addon models using discovered models.
        
        Args:
            db_manager: Database manager for operations
            addon_name: Name of the addon
            discovered_models: Dictionary of discovered model classes
            
        Returns:
            Dictionary with population results
        """
        try:
            from erp.utils.schema import SchemaGenerator
            
            if not discovered_models:
                self.logger.error(f"No models discovered for {addon_name} IR metadata population")
                return {'models_processed': 0, 'fields_processed': 0, 'success': False}

            models_processed = 0
            fields_processed = 0

            for model_name, model_class in discovered_models.items():
                try:
                    # Get model schema using the model class directly
                    model_schema = SchemaGenerator._extract_schema_from_model_class(model_class)

                    if not model_schema:
                        self.logger.error(f"Could not get schema for {addon_name} model: {model_name}")
                        continue

                    # Register model in ir.model table
                    await self._register_model_metadata(db_manager, model_name, model_schema, addon_name)
                    models_processed += 1

                    # Register model fields in ir.model.fields table
                    fields_count = await self._register_model_fields_metadata(db_manager, model_name, model_schema, addon_name)
                    fields_processed += fields_count

                    self.logger.debug(f"✓ Registered {addon_name} model {model_name} with {fields_count} fields")

                except Exception as e:
                    self.logger.error(f"Failed to register {addon_name} model {model_name}: {e}")
                    continue

            return {
                'status': 'success',
                'models_processed': models_processed,
                'fields_processed': fields_processed
            }

        except Exception as e:
            self.logger.error(f"Failed to populate {addon_name} IR metadata: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    async def _register_model_metadata(self, db_manager: 'DatabaseManager', model_name: str, model_schema: dict, addon_name: str) -> bool:
        """Register a model in ir.model table"""
        try:
            # Check if model already exists
            exists = await db_manager.fetchval(
                "SELECT EXISTS(SELECT 1 FROM ir_model WHERE model = $1)",
                model_name
            )

            if exists:
                self.logger.debug(f"{addon_name} model {model_name} already exists in ir.model, skipping")
                return True

            # Insert model metadata
            model_id = str(uuid.uuid4())
            now = datetime.now()
            table_name = model_name.replace('.', '_')

            await db_manager.execute("""
                INSERT INTO ir_model (id, name, model, info, description, table_name_db, state, transient, create_at, update_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            """,
                model_id,
                model_name,  # name
                model_name,  # model
                f"{addon_name} model for {model_name}",  # info
                f"{addon_name} system model for {model_name}",  # description
                table_name,  # table_name_db
                addon_name,  # state
                False,  # transient
                now,  # create_at
                now   # update_at
            )

            self.logger.debug(f"✓ Registered {addon_name} model metadata for {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to register {addon_name} model {model_name}: {e}")
            return False

    async def _register_model_fields_metadata(self, db_manager: 'DatabaseManager', model_name: str, model_schema: dict, addon_name: str) -> int:
        """Register model fields in ir.model.fields table"""
        try:
            # First, get the model_id for this model from ir_model table
            model_id = await db_manager.fetchval(
                "SELECT id FROM ir_model WHERE model = $1",
                model_name
            )

            if not model_id:
                self.logger.error(f"Model {model_name} not found in ir_model table. Cannot register fields.")
                return 0

            fields_registered = 0

            for field_name, field_info in model_schema.get('fields', {}).items():
                try:
                    # Check if field already exists
                    exists = await db_manager.fetchval(
                        "SELECT EXISTS(SELECT 1 FROM ir_model_fields WHERE model = $1 AND name = $2)",
                        model_name, field_name
                    )

                    if exists:
                        continue

                    # Insert field metadata
                    field_id = str(uuid.uuid4())
                    now = datetime.now()

                    await db_manager.execute("""
                        INSERT INTO ir_model_fields (
                            id, model, name, field_description, ttype, relation, required,
                            readonly, translate, state, create_at, update_at, model_id
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    """,
                        field_id,
                        model_name,
                        field_name,
                        field_info.get('string', field_name),
                        field_info.get('type', 'char'),
                        field_info.get('relation'),
                        field_info.get('required', False),
                        field_info.get('readonly', False),
                        field_info.get('translate', False),
                        addon_name,  # state
                        now,  # create_at
                        now,   # update_at
                        model_id  # model_id - the required field
                    )

                    fields_registered += 1

                except Exception as e:
                    self.logger.error(f"Failed to register field {field_name} for {addon_name} model {model_name}: {e}")
                    continue

            return fields_registered

        except Exception as e:
            self.logger.error(f"Failed to register fields for {addon_name} model {model_name}: {e}")
            return 0
    
    async def remove_addon_ir_metadata(self, db_manager: 'DatabaseManager', addon_name: str) -> bool:
        """
        Remove IR metadata for an addon.
        
        Args:
            db_manager: Database manager for operations
            addon_name: Name of the addon to remove metadata for
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Remove model fields first (due to foreign key constraints)
            await db_manager.execute(
                "DELETE FROM ir_model_fields WHERE state = $1",
                addon_name
            )
            
            # Remove models
            await db_manager.execute(
                "DELETE FROM ir_model WHERE state = $1",
                addon_name
            )
            
            self.logger.info(f"✓ Removed IR metadata for addon: {addon_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to remove IR metadata for {addon_name}: {e}")
            return False
    
    async def validate_ir_metadata(self, db_manager: 'DatabaseManager', addon_name: str, expected_models: list) -> bool:
        """
        Validate that IR metadata exists for the expected models.
        
        Args:
            db_manager: Database manager for operations
            addon_name: Name of the addon
            expected_models: List of expected model names
            
        Returns:
            True if all expected models are registered, False otherwise
        """
        try:
            for model_name in expected_models:
                exists = await db_manager.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM ir_model WHERE model = $1 AND state = $2)",
                    model_name, addon_name
                )
                
                if not exists:
                    self.logger.error(f"Model {model_name} not found in IR metadata for {addon_name}")
                    return False
            
            self.logger.debug(f"✓ All expected models found in IR metadata for {addon_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to validate IR metadata for {addon_name}: {e}")
            return False
