#!/usr/bin/env python3
"""
Single-Command Virtual Environment Manager
==========================================

This is the ultimate single script for managing your Python virtual environment.
It provides a simple interface for all virtual environment operations.

Usage:
    python env.py                    # Setup/verify virtual environment
    python env.py activate          # Show activation instructions
    python env.py run <command>     # Run command in virtual environment
    python env.py recreate          # Recreate virtual environment
    python env.py status            # Show environment status

Examples:
    python env.py                           # Setup environment
    python env.py activate                  # Get activation commands
    python env.py run python --version      # Run python in venv
    python env.py run pip install requests  # Install package in venv
    python env.py run python app.py         # Run your application
    python env.py recreate                  # Start fresh

This script automatically delegates to the comprehensive setup_env.py script
while providing a simpler command-line interface.
"""

import sys
import subprocess
from pathlib import Path


def print_help():
    """Print help information."""
    print(__doc__)


def main():
    """Main entry point with simplified command interface."""
    
    # Get the directory where this script is located
    script_dir = Path(__file__).parent.resolve()
    setup_script = script_dir / "setup_env.py"
    
    # Ensure setup_env.py exists
    if not setup_script.exists():
        print("❌ Error: setup_env.py not found in the same directory")
        print(f"Expected location: {setup_script}")
        sys.exit(1)
    
    # Parse simple command structure
    if len(sys.argv) == 1:
        # No arguments - run setup
        cmd = [sys.executable, str(setup_script)]
    
    elif len(sys.argv) == 2:
        action = sys.argv[1].lower()
        
        if action in ["help", "-h", "--help"]:
            print_help()
            sys.exit(0)
        
        elif action == "activate":
            cmd = [sys.executable, str(setup_script), "--activate"]
        
        elif action == "recreate":
            cmd = [sys.executable, str(setup_script), "--force-recreate"]
        
        elif action == "status":
            cmd = [sys.executable, str(setup_script), "--activate"]
        
        elif action == "run":
            print("❌ Error: 'run' command requires additional arguments")
            print("Usage: python env.py run <command>")
            print("Example: python env.py run python --version")
            sys.exit(1)
        
        else:
            print(f"❌ Error: Unknown command '{action}'")
            print("Run 'python env.py help' for usage information")
            sys.exit(1)
    
    elif len(sys.argv) >= 3 and sys.argv[1].lower() == "run":
        # Run command in virtual environment
        run_args = sys.argv[2:]
        cmd = [sys.executable, str(setup_script), "--run"] + run_args
    
    else:
        print("❌ Error: Invalid command structure")
        print("Run 'python env.py help' for usage information")
        sys.exit(1)
    
    # Execute the command
    try:
        result = subprocess.run(cmd, cwd=script_dir)
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error executing command: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
