<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_database_list_enhanced" xml:space="preserve">

    <!-- Enhanced Database List Page Template - Main Template -->
    <t t-name="system.database_list_enhanced">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <!-- Tailwind CSS CDN -->
                <script src="https://cdn.tailwindcss.com"></script>
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet"/>
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
            </head>
            <body class="bg-gray-50 font-sans min-h-screen">
                <!-- App Header Component -->
                <t t-include="system.header"/>

                <!-- Main Content -->
                <main class="max-w-7xl mx-auto px-6 py-8">
                    <!-- Page Header -->
                    <div class="mb-8">
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">Database Selection</h1>
                        <p class="text-lg text-gray-600">Choose a database to access your ERP system and manage your business operations.</p>
                    </div>

                    <!-- Enhanced Status Bar Component -->
                    <t t-include="system.status_bar"/>

                    <!-- Database List -->
                    <div id="database-list">
                        <t t-if="databases and len(databases) > 0">
                            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mb-8">
                                <t t-foreach="databases" t-as="db">
                                    <t t-include="system.database_card"/>
                                </t>
                            </div>
                        </t>

                        <!-- Compact Empty State Component -->
                        <t t-if="not databases or len(databases) == 0">
                            <t t-include="system.database_empty_state"/>
                        </t>
                    </div>

                    <!-- Footer Component -->
                    <t t-include="system.footer"/>
                </main>

                <!-- Create Database Modal Component -->
                <t t-include="system.create_database_modal"/>

                <!-- Remove Database Modal Component -->
                <t t-include="system.database_remove_modal"/>

                <!-- JavaScript -->
                <script src="/static/js/database_list_enhanced.js"></script>
            </body>
        </html>
    </t>

</templates>
