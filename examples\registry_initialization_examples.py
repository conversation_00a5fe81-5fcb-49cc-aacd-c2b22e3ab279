"""
Examples of DatabaseMemoryRegistry initialization from different contexts
"""
import asyncio
from erp.database.memory.registry_manager import MemoryRegistryManager
from erp.logging import get_logger

logger = get_logger(__name__)


async def initialize_registry_for_job(db_name: str):
    """
    Example: Initialize registry for a background job
    """
    try:
        logger.info(f"Job starting - initializing registry for database: {db_name}")
        
        # Clean initialization with delay and proper error handling
        registry = await MemoryRegistryManager.initialize_registry_with_delay(
            db_name, 
            delay_seconds=3.0
        )
        
        logger.info(f"Registry initialized successfully for job on database: {db_name}")
        
        # Use the registry for job operations
        modules = await registry.get_installed_modules()
        logger.info(f"Job found {len(modules)} installed modules")
        
        return registry
        
    except Exception as e:
        logger.error(f"Job failed - registry initialization error for {db_name}: {e}")
        raise


async def initialize_registry_for_cron(db_name: str):
    """
    Example: Initialize registry for a cron job
    """
    try:
        logger.info(f"Cron job starting - initializing registry for database: {db_name}")
        
        # Clean initialization with delay and proper error handling
        registry = await MemoryRegistryManager.initialize_registry_with_delay(
            db_name, 
            delay_seconds=2.0  # Shorter delay for cron jobs
        )
        
        logger.info(f"Registry initialized successfully for cron job on database: {db_name}")
        
        # Use the registry for cron operations
        stats = registry.get_stats()
        logger.info(f"Cron job - registry stats: {stats}")
        
        return registry
        
    except Exception as e:
        logger.error(f"Cron job failed - registry initialization error for {db_name}: {e}")
        raise


async def initialize_registry_for_api_service(db_name: str):
    """
    Example: Initialize registry for an API service
    """
    try:
        logger.info(f"API service starting - initializing registry for database: {db_name}")
        
        # Clean initialization with delay and proper error handling
        registry = await MemoryRegistryManager.initialize_registry_with_delay(
            db_name, 
            delay_seconds=1.0  # Faster for API services
        )
        
        logger.info(f"Registry initialized successfully for API service on database: {db_name}")
        
        # Use the registry for API operations
        routes = await registry.get_routes()
        logger.info(f"API service found {len(routes)} registered routes")
        
        return registry
        
    except Exception as e:
        logger.error(f"API service failed - registry initialization error for {db_name}: {e}")
        raise


async def check_existing_registry(db_name: str):
    """
    Example: Check if registry already exists before initialization
    """
    try:
        # Check if registry already exists
        if await MemoryRegistryManager.has_registry(db_name):
            logger.info(f"Registry already exists for database: {db_name}")
            registry = await MemoryRegistryManager.get_registry(db_name)
            return registry
        else:
            logger.info(f"No existing registry found for database: {db_name}")
            # Initialize with delay
            return await MemoryRegistryManager.initialize_registry_with_delay(db_name)
            
    except Exception as e:
        logger.error(f"Registry check/initialization failed for {db_name}: {e}")
        raise


async def main():
    """
    Example usage from different contexts
    """
    db_name = "test_database"
    
    try:
        # Example 1: Background job
        await initialize_registry_for_job(db_name)
        
        # Example 2: Cron job (registry already exists, so it will be reused)
        await initialize_registry_for_cron(db_name)
        
        # Example 3: API service (registry already exists, so it will be reused)
        await initialize_registry_for_api_service(db_name)
        
        # Example 4: Check existing registry
        await check_existing_registry(db_name)
        
    except Exception as e:
        logger.error(f"Example execution failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())