[options]
# =============================================================================
# ERP SYSTEM CONFIGURATION - COMPREHENSIVE LOGGING EXAMPLE
# =============================================================================
# This example shows all available logging configuration options

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
http_port = 8069
http_interface = 127.0.0.1
server_type = asgi

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp
db_name = erp_db
list_db = false
db_filter = .*
db_pool_min_size = 10
db_pool_max_size = 20

# =============================================================================
# ADDONS CONFIGURATION
# =============================================================================
addons_path = addons

# =============================================================================
# COMPREHENSIVE LOGGING CONFIGURATION
# =============================================================================

# Basic logging settings
log_level = debug
log_file = logs/erp.log
log_date_format = %Y-%m-%d %H:%M:%S

# Console logging
log_console_enabled = true
log_console_level = info
log_console_format = %(asctime)s [%(levelname)s] %(name)s: %(message)s
log_colored_console = true

# File logging
log_file_enabled = true
log_file_level = debug
log_file_format = %(asctime)s [%(levelname)s] %(name)s: %(message)s | %(pathname)s:%(lineno)d | User:%(user_id)s | DB:%(database)s
log_file_rotating = true
log_file_max_bytes = 52428800
log_file_backup_count = 10
log_file_json = false

# Timed rotating file (alternative to size-based)
log_file_timed_rotating = false
log_file_when = midnight
log_file_interval = 1

# JSON structured logging
log_json_enabled = true
log_json_include_extra = true

# Database logging (for critical events)
log_database_enabled = true
log_database_level = warning
log_database_table = system_logs
log_database_buffer_size = 50

# Performance logging
log_performance_enabled = true
log_performance_threshold = 0.5

# Security logging
log_security_enabled = true
log_security_level = warning

# Rate limiting (prevent log flooding)
log_rate_limit_enabled = true
log_rate_limit_max_records = 1000
log_rate_limit_time_window = 60

# Duplicate filtering (reduce noise)
log_duplicate_filter_enabled = true
log_duplicate_max_count = 3
log_duplicate_time_window = 300

# Module filtering
log_module_filter_enabled = true
log_module_filter_include = erp.*,addons.*
log_module_filter_exclude = erp.tests.*,*.migrations.*

# Handlers to use (comma-separated)
log_handlers = console,file,database

# =============================================================================
# ADVANCED LOGGING CONFIGURATION
# =============================================================================

# Logger-specific configurations
[loggers]
# Database logger
erp.database.level = debug
erp.database.handlers = file,database
erp.database.propagate = false

# Security logger
erp.security.level = warning
erp.security.handlers = console,file,database
erp.security.propagate = false

# Performance logger
erp.performance.level = info
erp.performance.handlers = file
erp.performance.propagate = false

# API logger
erp.routes.level = info
erp.routes.handlers = console,file
erp.routes.propagate = true

# Addon logger
addons.level = info
addons.handlers = console,file
addons.propagate = true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
admin_passwd = admin

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
cors_origins = *
