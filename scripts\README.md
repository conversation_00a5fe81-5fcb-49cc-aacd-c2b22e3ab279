# Scripts Directory

This directory contains setup and utility scripts for the ERP system.

## Files

### Environment Setup
- **`setup_env.py`** - Comprehensive virtual environment setup script
  - Creates and manages Python virtual environments
  - Installs dependencies from requirements.txt
  - Cross-platform compatibility (Windows, macOS, Linux)
  - Automatic environment validation and repair

- **`setup.bat`** - Windows batch script for quick setup
  - Double-click to run setup on Windows
  - Calls setup_env.py with appropriate parameters

- **`activate_env.bat`** - Windows activation helper
  - Quick activation script for Windows users
  - Generated automatically by setup_env.py

## Usage

### Initial Setup
```bash
# From project root
python scripts/setup_env.py
```

### Windows Quick Setup
Double-click `scripts/setup.bat` in Windows Explorer.

### Manual Activation (Windows)
```cmd
scripts\activate_env.bat
```

## See Also
- `docs/DEVELOPMENT_GUIDE.md` - Complete development setup guide
- `env.py` - Simple command interface for environment management
