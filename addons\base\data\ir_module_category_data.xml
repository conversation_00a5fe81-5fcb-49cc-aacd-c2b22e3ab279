<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">
    
    <!-- Module Categories - Load these first as they are referenced by groups -->
    <record id="module_category_administration" model="ir.module.category">
        <field name="name">Administration</field>
        <field name="description">User access to administration features</field>
        <field name="sequence">1</field>
        <field name="visible">True</field>
    </record>
    
    <record id="module_category_base" model="ir.module.category">
        <field name="name">Base</field>
        <field name="description">Base system functionality</field>
        <field name="sequence">2</field>
        <field name="visible">True</field>
    </record>
    
    <record id="module_category_user_types" model="ir.module.category">
        <field name="name">User Types</field>
        <field name="description">Different types of users</field>
        <field name="sequence">3</field>
        <field name="visible">True</field>
        <field name="exclusive">True</field>
    </record>
    
</data>
