@echo off
echo Running Python code formatting...
echo.

echo [1/2] Running isort to sort imports...
python -m isort erp/ tests/ examples/ scripts/ --check-only --diff
if %errorlevel% neq 0 (
    echo.
    echo Fixing import sorting...
    python -m isort erp/ tests/ examples/ scripts/
)

echo.
echo [2/2] Running black to format code...
python -m black erp/ tests/ examples/ scripts/ --check --diff
if %errorlevel% neq 0 (
    echo.
    echo Fixing code formatting...
    python -m black erp/ tests/ examples/ scripts/
)

echo.
echo Code formatting completed!
