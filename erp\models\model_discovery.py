"""
Model discovery functionality for addon operations
"""
import importlib
import inspect
from typing import Dict, Type
from pathlib import Path

from ..logging import get_logger
from .base_model import BaseModel


class ModelDiscovery:
    """Handles discovery of models within addon directories"""
    
    def __init__(self, addon_name: str):
        """
        Initialize model discovery for a specific addon
        
        Args:
            addon_name: Name of the addon to discover models for
        """
        self.addon_name = addon_name
        self.logger = get_logger(f"{__name__}.{addon_name}")
        
    def discover_addon_models(self) -> Dict[str, Type[BaseModel]]:
        """
        Discover all models in the addon by recursively scanning all Python modules
        within the addon directory for BaseModel subclasses
        
        Returns:
            Dictionary mapping model names to model classes
        """
        self.logger.info(f"Discovering models for addon: {self.addon_name}")
        models = {}
        
        try:
            addon_package = self._import_addon_package()
            if not addon_package:
                return models
                
            # Get the addon directory path
            addon_dir = Path(addon_package.__file__).parent
            self.logger.debug(f"Scanning addon directory: {addon_dir}")

            # Recursively scan all Python files in the addon directory
            visited_modules = set()
            addon_module_path = self._get_addon_module_path()
            models = self._scan_directory_recursive(addon_dir, addon_module_path, visited_modules)
            
            self.logger.info(f"Discovered {len(models)} models for addon: {self.addon_name}")
            return models

        except Exception as e:
            self.logger.error(f"Failed to discover models for addon {self.addon_name}: {e}")
            raise
    
    def _import_addon_package(self):
        """Import the addon package using different possible paths"""
        # Try different addon import paths
        addon_module_paths = [
            f"addons.{self.addon_name}",
            f"erp.addons.{self.addon_name}"
        ]

        for path in addon_module_paths:
            try:
                return importlib.import_module(path)
            except ImportError:
                continue

        self.logger.warning(f"Addon {self.addon_name} not found in any import path")
        return None
    
    def _get_addon_module_path(self) -> str:
        """Get the module path for the addon"""
        addon_module_paths = [
            f"addons.{self.addon_name}",
            f"erp.addons.{self.addon_name}"
        ]

        for path in addon_module_paths:
            try:
                importlib.import_module(path)
                return path
            except ImportError:
                continue
        
        return f"addons.{self.addon_name}"  # Default fallback

    def _scan_directory_recursive(self, directory: Path, base_module_path: str, visited_modules: set) -> Dict[str, Type[BaseModel]]:
        """
        Recursively scan a directory for Python modules containing BaseModel subclasses

        Args:
            directory: Directory path to scan
            base_module_path: Base module path for imports
            visited_modules: Set of already visited module names to avoid duplicates
            
        Returns:
            Dictionary mapping model names to model classes
        """
        models = {}
        
        try:
            # Find all Python files in the directory and subdirectories
            for py_file in directory.rglob("*.py"):
                # Skip files that start with __ (like __init__.py, __pycache__, etc.)
                if py_file.name.startswith("__"):
                    continue

                # Skip hidden files and directories
                if any(part.startswith('.') for part in py_file.parts):
                    continue

                # Skip __pycache__ directories
                if '__pycache__' in py_file.parts:
                    continue

                # Calculate relative path from the addon directory
                relative_path = py_file.relative_to(directory)

                # Convert file path to module name
                module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
                module_name = f"{base_module_path}.{'.'.join(module_parts)}"

                # Skip if already visited
                if module_name in visited_modules:
                    continue

                visited_modules.add(module_name)

                # Try to import and scan the module
                try:
                    module = importlib.import_module(module_name)
                    module_models = self._scan_module_for_models(module)
                    models.update(module_models)
                    self.logger.debug(f"Scanned module: {module_name}")
                except Exception as e:
                    self.logger.debug(f"Failed to import {module_name}: {e}")

        except Exception as e:
            self.logger.warning(f"Error scanning directory {directory}: {e}")
            
        return models
    
    def _scan_module_for_models(self, module) -> Dict[str, Type[BaseModel]]:
        """
        Scan a module for BaseModel subclasses

        Args:
            module: Python module to scan
            
        Returns:
            Dictionary mapping model names to model classes
        """
        models = {}
        
        for name in dir(module):
            obj = getattr(module, name)

            # Check if it's a class that inherits from BaseModel
            if (inspect.isclass(obj) and
                issubclass(obj, BaseModel) and
                obj is not BaseModel and
                # Exclude the base hierarchy classes themselves
                obj.__name__ not in ('AbstractModel', 'TransientModel', 'Model') and
                hasattr(obj, '_name') and
                obj._name):

                model_name = obj._name
                models[model_name] = obj

                # Log model type for debugging
                model_type = "Abstract" if getattr(obj, '_abstract', False) else \
                           "Transient" if getattr(obj, '_transient', False) else \
                           "Standard"
                self.logger.debug(f"Discovered {model_type} model: {model_name} ({obj.__name__})")
        
        return models
