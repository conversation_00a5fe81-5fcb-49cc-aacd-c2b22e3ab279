#!/usr/bin/env python3
"""
ERP System - Command Line Interface
Modular CLI tool for database and server management
"""
import sys
from pathlib import Path

# Add current directory to Python path
erp_root = Path(__file__).parent
sys.path.insert(0, str(erp_root))

# Import the modular CLI system
from erp.cli import ERPCLIManager


def main():
    """Main entry point for the ERP CLI"""
    try:
        # Create and run the CLI manager
        manager = ERPCLIManager()
        return manager.run()
    except KeyboardInterrupt:
        print("\n🛑 Operation cancelled by user")
        return 130
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
