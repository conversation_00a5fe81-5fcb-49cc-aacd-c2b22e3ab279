"""
Test suite for HTML error response functionality

This module tests:
- HTMLErrorResponse class functionality
- Template-based error page generation
- Fallback to inline HTML when templates are unavailable
- Error page context and content validation
"""
from unittest.mock import MagicMock, patch
from datetime import datetime

from fastapi.responses import HTMLResponse
from erp.http.core.html_errors import HTMLErrorResponse


class TestHTMLErrorResponse:
    """Test HTMLErrorResponse class"""

    def test_get_template_manager_success(self):
        """Test successful template manager retrieval"""
        mock_manager = MagicMock()

        with patch('erp.templates.manager.get_template_manager', return_value=mock_manager):
            result = HTMLErrorResponse._get_template_manager()
            assert result == mock_manager

    def test_get_template_manager_import_error(self):
        """Test template manager retrieval with import error"""
        with patch('erp.templates.manager.get_template_manager', side_effect=ImportError):
            result = HTMLErrorResponse._get_template_manager()
            assert result is None

    def test_create_error_page_with_templates(self):
        """Test error page creation using templates"""
        # Mock template manager
        mock_manager = MagicMock()
        mock_manager.render_template.return_value = "<html>Template rendered</html>"
        
        # Mock error
        error = ValueError("Test error message")
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=mock_manager):
            response = HTMLErrorResponse.create_error_page(
                error=error,
                status_code=400,
                title="Custom Error",
                include_stacktrace=True,
                request_info={"method": "GET", "path": "/test"}
            )
        
        # Verify response
        assert isinstance(response, HTMLResponse)
        assert response.status_code == 400
        assert response.body.decode() == "<html>Template rendered</html>"
        
        # Verify template was called with correct context
        mock_manager.render_template.assert_called_once()
        call_args = mock_manager.render_template.call_args
        assert call_args[0][0] == 'error.detailed'  # template name
        
        context = call_args[0][1]
        assert context['title'] == "Custom Error"
        assert context['error_message'] == "Test error message"
        assert context['error_type'] == "ValueError"
        assert context['status_code'] == 400
        assert context['include_stacktrace'] is True
        assert context['request_info'] == {"method": "GET", "path": "/test"}
        assert 'stacktrace' in context
        assert 'timestamp' in context

    def test_create_error_page_template_fallback(self):
        """Test error page creation with template fallback"""
        # Mock template manager that raises an exception
        mock_manager = MagicMock()
        mock_manager.render_template.side_effect = Exception("Template error")
        
        error = RuntimeError("Test error")
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=mock_manager):
            response = HTMLErrorResponse.create_error_page(
                error=error,
                status_code=500,
                include_stacktrace=False
            )
        
        # Verify response uses fallback HTML
        assert isinstance(response, HTMLResponse)
        assert response.status_code == 500
        content = response.body.decode()
        assert "<!DOCTYPE html>" in content
        assert "Test error" in content
        assert "RuntimeError" in content

    def test_create_error_page_no_template_manager(self):
        """Test error page creation without template manager"""
        error = Exception("No template manager")
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=None):
            response = HTMLErrorResponse.create_error_page(
                error=error,
                status_code=503
            )
        
        # Verify response uses fallback HTML
        assert isinstance(response, HTMLResponse)
        assert response.status_code == 503
        content = response.body.decode()
        assert "<!DOCTYPE html>" in content
        assert "No template manager" in content

    def test_create_simple_error_page_with_templates(self):
        """Test simple error page creation using templates"""
        mock_manager = MagicMock()
        mock_manager.render_template.return_value = "<html>Simple template</html>"
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=mock_manager):
            response = HTMLErrorResponse.create_simple_error_page(
                message="Simple error message",
                status_code=404,
                title="Not Found"
            )
        
        # Verify response
        assert isinstance(response, HTMLResponse)
        assert response.status_code == 404
        assert response.body.decode() == "<html>Simple template</html>"
        
        # Verify template was called with correct context
        mock_manager.render_template.assert_called_once()
        call_args = mock_manager.render_template.call_args
        assert call_args[0][0] == 'error.simple'  # template name
        
        context = call_args[0][1]
        assert context['title'] == "Not Found"
        assert context['error_message'] == "Simple error message"
        assert context['status_code'] == 404
        assert 'timestamp' in context

    def test_create_simple_error_page_fallback(self):
        """Test simple error page creation with fallback"""
        mock_manager = MagicMock()
        mock_manager.render_template.side_effect = Exception("Template error")
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=mock_manager):
            response = HTMLErrorResponse.create_simple_error_page(
                message="Fallback message",
                status_code=400
            )
        
        # Verify response uses fallback HTML
        assert isinstance(response, HTMLResponse)
        assert response.status_code == 400
        content = response.body.decode()
        assert "<!DOCTYPE html>" in content
        assert "Fallback message" in content
        assert "Error 400" in content

    def test_error_page_stacktrace_generation(self):
        """Test stacktrace generation in error pages"""
        try:
            # Create an error with a real stacktrace
            raise ValueError("Test error with stacktrace")
        except ValueError as e:
            with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=None):
                response = HTMLErrorResponse.create_error_page(
                    error=e,
                    status_code=500,
                    include_stacktrace=True
                )
            
            content = response.body.decode()
            assert "ValueError" in content
            assert "Test error with stacktrace" in content
            # Should contain stacktrace in fallback HTML
            assert "Traceback" in content or "test_error_page_stacktrace_generation" in content

    def test_error_page_without_stacktrace(self):
        """Test error page without stacktrace"""
        error = Exception("No stacktrace")
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=None):
            response = HTMLErrorResponse.create_error_page(
                error=error,
                status_code=500,
                include_stacktrace=False
            )
        
        content = response.body.decode()
        assert "No stacktrace" in content
        # Should not contain actual stacktrace content in fallback HTML
        assert "<div class=\"stacktrace-section\">" not in content
        assert "<pre class=\"stacktrace\">" not in content

    def test_html_error_response_direct_usage(self):
        """Test direct usage of HTMLErrorResponse class"""
        error = RuntimeError("Direct test")

        with patch.object(HTMLErrorResponse, 'create_error_page') as mock_create:
            mock_create.return_value = HTMLResponse("test", status_code=500)

            response = HTMLErrorResponse.create_error_page(
                error=error,
                status_code=500,
                request_info={"test": "data"},
                include_stacktrace=True
            )

            mock_create.assert_called_once_with(
                error=error,
                status_code=500,
                request_info={"test": "data"},
                include_stacktrace=True
            )

    def test_error_page_default_values(self):
        """Test error page creation with default values"""
        error = Exception("Default test")
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=None):
            response = HTMLErrorResponse.create_error_page(error=error)
        
        assert response.status_code == 500
        content = response.body.decode()
        assert "Error 500" in content
        assert "Default test" in content

    def test_simple_error_page_default_values(self):
        """Test simple error page creation with default values"""
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=None):
            response = HTMLErrorResponse.create_simple_error_page("Default simple")
        
        assert response.status_code == 500
        content = response.body.decode()
        assert "Error 500" in content
        assert "Default simple" in content

    @patch('erp.http.core.html_errors.datetime')
    def test_timestamp_formatting(self, mock_datetime):
        """Test timestamp formatting in error pages"""
        # Mock datetime to return a fixed time
        fixed_time = datetime(2024, 1, 15, 10, 30, 45)
        mock_datetime.now.return_value = fixed_time
        
        mock_manager = MagicMock()
        mock_manager.render_template.return_value = "<html>test</html>"
        
        with patch.object(HTMLErrorResponse, '_get_template_manager', return_value=mock_manager):
            HTMLErrorResponse.create_error_page(Exception("test"))
        
        # Verify timestamp was formatted correctly
        call_args = mock_manager.render_template.call_args[0][1]
        assert call_args['timestamp'] == "2024-01-15 10:30:45"
