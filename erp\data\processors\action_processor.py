"""
Action Processor for ERP system

Specialized processor for handling actions (window actions, server actions, etc.)
with proper validation and reference resolution.
"""

from typing import Dict, List, Any, Optional

from .base import BaseDataProcessor
from ..sql_helpers import SQLHelpers, ModelSQLHelpers
from ..xmlid_manager import XMLIDManager


class ActionProcessor(BaseDataProcessor):
    """
    Processor for actions (ir.actions.*)
    
    Handles creation and updating of various action types with proper
    validation and reference resolution.
    """
    
    def __init__(self, db_manager, name: str = "ActionProcessor"):
        super().__init__(db_manager, name)
        
        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_manager = XMLIDManager(db_manager)
        
        # Supported action models
        self.supported_action_models = {
            'ir.actions.act_window',
            'ir.actions.server',
            'ir.actions.report',
            'ir.actions.url',
            'ir.actions.client'
        }
        
        # Action type mappings
        self.action_type_mapping = {
            'act_window': 'ir.actions.act_window',
            'server': 'ir.actions.server',
            'report': 'ir.actions.report',
            'url': 'ir.actions.url',
            'client': 'ir.actions.client'
        }
    
    def can_process(self, item: Dict[str, Any]) -> bool:
        """Check if this processor can handle the given item"""
        if not isinstance(item, dict):
            return False
        
        model = item.get('model')
        element_type = item.get('element_type')
        
        # Check if it's an action model
        if model in self.supported_action_models:
            return True
        
        # Check if it's an action element type
        if element_type in ['act_window', 'function']:
            return True
        
        return False
    
    def get_supported_models(self) -> List[str]:
        """Get list of models this processor supports"""
        return list(self.supported_action_models)
    
    def get_processing_order(self) -> int:
        """Get processing order (process actions before menus)"""
        return 25
    
    async def _process_item(self, item: Dict[str, Any], **kwargs) -> bool:
        """Process a single action item"""
        xml_id = item.get('xml_id')
        model = item.get('model')
        values = item.get('values', {})
        noupdate = item.get('noupdate', False)
        element_type = item.get('element_type')
        
        try:
            # Determine action model from element type if needed
            if element_type and not model:
                model = self.action_type_mapping.get(element_type)
                if not model:
                    self.result.add_error(f"Unknown action element type: {element_type}")
                    return False
            
            # Handle special element types
            if element_type:
                values = await self._convert_element_attributes(item, values, element_type)
            
            # Validate action data
            validation_result = await self._validate_action_data(values, model)
            if not validation_result['valid']:
                for error in validation_result['errors']:
                    self.result.add_error(f"Action validation error for {xml_id}: {error}")
                return False
            
            # Process action-specific fields
            processed_values = await self._process_action_values(values, model)
            if processed_values is None:
                return False
            
            # Check if action exists
            existing_record_id = None
            if xml_id:
                existing_record_id = await self._find_record_by_xmlid(xml_id)
            
            if existing_record_id:
                # Update existing action
                if not noupdate:
                    success = await self._update_action(model, existing_record_id, processed_values)
                    if success:
                        self.logger.debug(f"Updated action {xml_id}")
                        return True
                else:
                    self.logger.debug(f"Skipped updating action {xml_id} (noupdate=True)")
                    return True
            else:
                # Create new action
                new_record_id = await self._create_action(model, processed_values)
                if new_record_id:
                    # Store XML ID mapping
                    if xml_id:
                        await self._store_xmlid_mapping(xml_id, model, new_record_id)
                    
                    self.logger.debug(f"Created action {xml_id or 'no-id'}")
                    return True
            
            return False
            
        except Exception as e:
            error_msg = f"Failed to process action {xml_id or 'no-id'}: {e}"
            self.result.add_error(error_msg)
            return False
    
    async def _convert_element_attributes(self, item: Dict[str, Any], values: Dict[str, Any], element_type: str) -> Dict[str, Any]:
        """Convert element attributes to action field values"""
        converted_values = values.copy()
        
        if element_type == 'act_window':
            # Map act_window attributes
            attribute_mapping = {
                'name': 'name',
                'res_model': 'res_model',
                'view_mode': 'view_mode',
                'view_type': 'view_type',
                'target': 'target',
                'domain': 'domain',
                'context': 'context',
                'limit': 'limit'
            }
        elif element_type == 'function':
            # Map function (server action) attributes
            attribute_mapping = {
                'name': 'name',
                'model_id': 'model_id',
                'code': 'code',
                'state': 'state'
            }
            # Set default state for server actions
            if 'state' not in converted_values:
                converted_values['state'] = {'type': 'text', 'value': 'code'}
        else:
            attribute_mapping = {}
        
        # Apply attribute mapping
        for attr_name, field_name in attribute_mapping.items():
            if attr_name in values and field_name not in converted_values:
                converted_values[field_name] = values[attr_name]
        
        return converted_values
    
    async def _validate_action_data(self, values: Dict[str, Any], model: str) -> Dict[str, Any]:
        """Validate action data structure"""
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check required fields based on action type
        if model == 'ir.actions.act_window':
            required_fields = ['name', 'res_model']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for window action: {field}")
                    result['valid'] = False
        
        elif model == 'ir.actions.server':
            required_fields = ['name', 'model_id']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for server action: {field}")
                    result['valid'] = False
        
        elif model == 'ir.actions.report':
            required_fields = ['name', 'model']
            for field in required_fields:
                if field not in values:
                    result['errors'].append(f"Missing required field for report action: {field}")
                    result['valid'] = False
        
        # Validate view references for window actions
        if model == 'ir.actions.act_window':
            for view_field in ['view_id', 'search_view_id']:
                if view_field in values:
                    view_def = values[view_field]
                    if isinstance(view_def, dict) and view_def.get('type') == 'ref':
                        view_ref = view_def.get('value')
                        if view_ref and not await self._validate_view_exists(view_ref):
                            result['warnings'].append(f"View reference may not exist: {view_ref}")
        
        return result
    
    async def _validate_view_exists(self, view_ref: str) -> bool:
        """Validate that view exists"""
        try:
            view_id = await self.xmlid_manager.resolve_xmlid_to_record_id(view_ref)
            return view_id is not None
        except Exception:
            return False
    
    async def _process_action_values(self, values: Dict[str, Any], model: str) -> Optional[Dict[str, Any]]:
        """Process action-specific field values"""
        processed = {}
        
        for field_name, field_def in values.items():
            try:
                if field_name in ['view_id', 'search_view_id']:
                    # Special handling for view references
                    processed[field_name] = await self._process_view_reference(field_def)
                elif field_name == 'model_id':
                    # Special handling for model reference
                    processed[field_name] = await self._process_model_reference(field_def)
                elif field_name in ['domain', 'context']:
                    # Special handling for domain and context (should be strings)
                    processed[field_name] = await self._process_domain_context_field(field_def)
                else:
                    # Standard field processing
                    processed[field_name] = await self._process_standard_field(field_def)
                    
            except Exception as e:
                error_msg = f"Error processing action field {field_name}: {e}"
                self.result.add_error(error_msg)
                return None
        
        return processed
    
    async def _process_view_reference(self, view_def: Any) -> Optional[str]:
        """Process view reference"""
        if isinstance(view_def, dict):
            if view_def.get('type') == 'ref':
                ref_value = view_def.get('value')
                return await self._resolve_reference(ref_value)
            else:
                return view_def.get('value')
        else:
            return str(view_def) if view_def else None
    
    async def _process_model_reference(self, model_def: Any) -> Optional[str]:
        """Process model reference"""
        if isinstance(model_def, dict):
            if model_def.get('type') == 'ref':
                ref_value = model_def.get('value')
                return await self._resolve_reference(ref_value)
            else:
                return model_def.get('value')
        else:
            return str(model_def) if model_def else None
    
    async def _process_domain_context_field(self, field_def: Any) -> str:
        """Process domain or context field (should be string)"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value', '')
            
            if field_type == 'eval':
                # For eval, return the expression as string
                return field_value
            else:
                return str(field_value)
        else:
            return str(field_def) if field_def else ''
    
    async def _process_standard_field(self, field_def: Any) -> Any:
        """Process standard field values"""
        if isinstance(field_def, dict):
            field_type = field_def.get('type')
            field_value = field_def.get('value')
            
            if field_type == 'ref':
                return await self._resolve_reference(field_value)
            elif field_type == 'eval':
                return self._evaluate_expression(field_value)
            else:
                return field_value
        else:
            return field_def
    
    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(ref_value)
        except Exception as e:
            self.result.add_warning(f"Failed to resolve reference {ref_value}: {e}")
            return None
    
    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        try:
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]
            else:
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression
        except Exception:
            return expression
    
    async def _find_record_by_xmlid(self, xml_id: str) -> Optional[str]:
        """Find a record ID by its XML ID"""
        try:
            return await self.xmlid_manager.resolve_xmlid_to_record_id(xml_id)
        except Exception:
            return None
    
    async def _create_action(self, model: str, values: Dict[str, Any]) -> Optional[str]:
        """Create a new action record"""
        try:
            return await self.model_sql.create_record(model, values)
        except Exception as e:
            self.result.add_error(f"Failed to create action: {e}")
            return None
    
    async def _update_action(self, model: str, record_id: str, values: Dict[str, Any]) -> bool:
        """Update an existing action record"""
        try:
            return await self.model_sql.update_record(model, record_id, values)
        except Exception as e:
            self.result.add_error(f"Failed to update action {record_id}: {e}")
            return False
    
    async def _store_xmlid_mapping(self, xml_id: str, model: str, record_id: str):
        """Store XML ID to record ID mapping"""
        try:
            if '.' in xml_id:
                module, name = xml_id.split('.', 1)
            else:
                module = self.context.get('addon_name', 'base')
                name = xml_id
            
            await self.xmlid_manager.create_xmlid_mapping(
                module=module,
                name=name,
                model=model,
                res_id=record_id
            )
        except Exception as e:
            self.result.add_warning(f"Failed to store XML ID mapping for {xml_id}: {e}")
