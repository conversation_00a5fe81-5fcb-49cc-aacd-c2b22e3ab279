"""
Addon installers

This package provides specialized installer classes for different types of installations:
- AddonInstaller: For regular addon installation/uninstallation/upgrade
- BaseModuleInstaller: For base module installation with clean bootstrap
- InstallationContext: Context and utilities for installation operations
"""

from .addon_installer import AddonInstaller
from .base_installer import BaseModuleInstaller
from .installation_context import InstallationContext, InstallationUtilities, RegistryExtractor

__all__ = [
    'AddonInstaller',
    'BaseModuleInstaller',
    'InstallationContext',
    'InstallationUtilities',
    'RegistryExtractor'
]
