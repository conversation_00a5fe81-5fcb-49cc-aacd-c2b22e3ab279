"""
Test suite for addon hook system

This module tests:
- Hook registration and management
- Hook execution and priority handling
- Hook context and parameters
- Async hook support
- Error handling in hooks
"""
import pytest
from unittest.mock import MagicMock

from erp.addons.hooks import HookRegistry, AddonHook, HookType, HookContext


class TestHookSystem:
    """Test addon hook system"""
    
    def test_hook_context_creation(self):
        """Test HookContext creation"""
        mock_env = MagicMock()
        
        context = HookContext(
            addon_name="test_addon",
            hook_type=HookType.PRE_INSTALL,
            env=mock_env,
            custom_param="test_value"
        )
        
        assert context.addon_name == "test_addon"
        assert context.hook_type == HookType.PRE_INSTALL
        assert context.env == mock_env
        assert context.custom_param == "test_value"
    
    def test_addon_hook_creation(self):
        """Test AddonHook creation"""
        def test_hook(context):
            return True
        
        hook = AddonHook(
            func=test_hook,
            hook_type=HookType.PRE_INSTALL,
            addon_name="test_addon",
            priority=50
        )
        
        assert hook.func == test_hook
        assert hook.hook_type == HookType.PRE_INSTALL
        assert hook.addon_name == "test_addon"
        assert hook.priority == 50
        assert hook.is_async is False
    
    def test_addon_hook_async_detection(self):
        """Test async hook detection"""
        async def async_hook(context):
            return True
        
        hook = AddonHook(
            func=async_hook,
            hook_type=HookType.PRE_INSTALL,
            addon_name="test_addon"
        )
        
        assert hook.is_async is True
    
    def test_hook_registry_registration(self):
        """Test hook registration in registry"""
        registry = HookRegistry()
        
        def test_hook(context):
            return True
        
        registry.register_hook(test_hook, HookType.PRE_INSTALL, "test_addon")
        
        hooks = registry.get_hooks(HookType.PRE_INSTALL)
        assert len(hooks) == 1
        assert hooks[0].addon_name == "test_addon"
    
    def test_hook_registry_priority_sorting(self):
        """Test hook priority sorting"""
        registry = HookRegistry()
        
        def hook1(context):
            return True
        
        def hook2(context):
            return True
        
        # Register hooks with different priorities
        registry.register_hook(hook1, HookType.PRE_INSTALL, "addon1", priority=100)
        registry.register_hook(hook2, HookType.PRE_INSTALL, "addon2", priority=10)
        
        hooks = registry.get_hooks(HookType.PRE_INSTALL)
        assert len(hooks) == 2
        assert hooks[0].priority == 10  # Lower priority number comes first
        assert hooks[1].priority == 100
    
    def test_hook_registry_unregister(self):
        """Test unregistering addon hooks"""
        registry = HookRegistry()
        
        def test_hook(context):
            return True
        
        registry.register_hook(test_hook, HookType.PRE_INSTALL, "test_addon")
        registry.register_hook(test_hook, HookType.POST_INSTALL, "test_addon")
        
        # Verify hooks are registered
        assert len(registry.get_hooks(HookType.PRE_INSTALL)) == 1
        assert len(registry.get_hooks(HookType.POST_INSTALL)) == 1
        
        # Unregister all hooks for addon
        registry.unregister_addon_hooks("test_addon")
        
        # Verify hooks are removed
        assert len(registry.get_hooks(HookType.PRE_INSTALL)) == 0
        assert len(registry.get_hooks(HookType.POST_INSTALL)) == 0
    
    @pytest.mark.asyncio
    async def test_hook_execution_success(self):
        """Test successful hook execution"""
        registry = HookRegistry()
        
        def test_hook(context):
            return "success"
        
        registry.register_hook(test_hook, HookType.PRE_INSTALL, "test_addon")
        
        mock_env = MagicMock()
        results = await registry.execute_hooks(HookType.PRE_INSTALL, "test_addon", mock_env)
        
        assert len(results) == 1
        assert results[0] == "success"
    
    @pytest.mark.asyncio
    async def test_hook_execution_failure(self):
        """Test hook execution with failure"""
        registry = HookRegistry()
        
        def failing_hook(context):
            raise Exception("Hook failed")
        
        registry.register_hook(failing_hook, HookType.PRE_INSTALL, "test_addon")
        
        mock_env = MagicMock()
        results = await registry.execute_hooks(HookType.PRE_INSTALL, "test_addon", mock_env)
        
        assert len(results) == 1
        assert results[0] is None  # Failed hooks return None
