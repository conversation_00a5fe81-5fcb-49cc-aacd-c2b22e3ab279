"""
Addon state management

This module handles addon state definitions and state management operations.
"""
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Tuple

from ..manifest import AddonManifest


class AddonState(Enum):
    """Addon installation states"""
    UNINSTALLED = "uninstalled"
    INSTALLED = "installed"
    TO_INSTALL = "to_install"
    TO_UPGRADE = "to_upgrade"
    TO_REMOVE = "to_remove"
    BROKEN = "broken"


@dataclass
class AddonInfo:
    """Enhanced addon information"""
    name: str
    manifest: AddonManifest
    state: AddonState
    installed_version: Optional[str] = None
    available_version: Optional[str] = None
    install_date: Optional[datetime] = None
    dependencies: List[str] = None
    dependents: List[str] = None
    installed_as_dependency: bool = False  # Track if installed as dependency
    installation_reason: str = "explicit"  # "explicit" or "dependency"

    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = self.manifest.depends.copy()
        if self.dependents is None:
            self.dependents = []


class AddonStateManager:
    """Manages addon states and state transitions"""
    
    def __init__(self):
        self._addon_states = {}
    
    def get_addon_info(self, addon_name: str) -> Optional[AddonInfo]:
        """Get detailed information about an addon"""
        return self._addon_states.get(addon_name)
    
    def set_addon_info(self, addon_name: str, addon_info: AddonInfo) -> None:
        """Set addon information"""
        self._addon_states[addon_name] = addon_info
    
    def list_addons(self, state_filter: AddonState = None) -> dict:
        """List addons, optionally filtered by state"""
        if state_filter is None:
            return self._addon_states.copy()
        
        return {name: info for name, info in self._addon_states.items()
                if info.state == state_filter}
    
    def update_addon_state(self, addon_name: str, new_state: AddonState) -> bool:
        """Update addon state"""
        if addon_name not in self._addon_states:
            return False
        
        self._addon_states[addon_name].state = new_state
        return True
    
    def mark_addon_installed(self, addon_name: str, version: str, as_dependency: bool = False) -> bool:
        """Mark addon as installed with version"""
        if addon_name not in self._addon_states:
            return False

        addon_info = self._addon_states[addon_name]
        addon_info.state = AddonState.INSTALLED
        addon_info.installed_version = version
        addon_info.install_date = datetime.now()
        addon_info.installed_as_dependency = as_dependency
        addon_info.installation_reason = "dependency" if as_dependency else "explicit"
        return True
    
    def mark_addon_uninstalled(self, addon_name: str) -> bool:
        """Mark addon as uninstalled"""
        if addon_name not in self._addon_states:
            return False

        addon_info = self._addon_states[addon_name]
        addon_info.state = AddonState.UNINSTALLED
        addon_info.installed_version = None
        addon_info.install_date = None
        addon_info.installed_as_dependency = False
        addon_info.installation_reason = "explicit"
        return True

    def mark_addon_broken(self, addon_name: str) -> bool:
        """Mark addon as broken"""
        if addon_name not in self._addon_states:
            return False

        self._addon_states[addon_name].state = AddonState.BROKEN
        return True

    def get_addons_by_installation_reason(self, reason: str) -> Dict[str, AddonInfo]:
        """Get addons installed for a specific reason ('explicit' or 'dependency')"""
        return {name: info for name, info in self._addon_states.items()
                if info.state == AddonState.INSTALLED and info.installation_reason == reason}

    def get_dependency_addons(self) -> Dict[str, AddonInfo]:
        """Get addons that were installed as dependencies"""
        return self.get_addons_by_installation_reason("dependency")

    def get_explicit_addons(self) -> Dict[str, AddonInfo]:
        """Get addons that were explicitly installed"""
        return self.get_addons_by_installation_reason("explicit")

    def can_uninstall_safely(self, addon_name: str) -> Tuple[bool, List[str]]:
        """
        Check if an addon can be uninstalled safely.

        Returns:
            Tuple of (can_uninstall, list_of_dependent_addons)
        """
        if addon_name not in self._addon_states:
            return False, ["Addon not found"]

        addon_info = self._addon_states[addon_name]
        if addon_info.state != AddonState.INSTALLED:
            return False, ["Addon not installed"]

        # Check if other installed addons depend on this one
        dependent_installed = []
        for name, info in self._addon_states.items():
            if (info.state == AddonState.INSTALLED and
                addon_name in info.dependencies):
                dependent_installed.append(name)

        if dependent_installed:
            return False, dependent_installed

        return True, []
    
    def check_upgrade_needed(self, addon_name: str) -> bool:
        """Check if addon needs upgrade"""
        if addon_name not in self._addon_states:
            return False
        
        addon_info = self._addon_states[addon_name]
        return (addon_info.state == AddonState.INSTALLED and 
                addon_info.installed_version != addon_info.available_version)
    
    def get_all_states(self) -> dict:
        """Get all addon states"""
        return self._addon_states.copy()
    
    def clear_states(self) -> None:
        """Clear all addon states"""
        self._addon_states.clear()
