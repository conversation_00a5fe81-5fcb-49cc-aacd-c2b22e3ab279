"""
Addon hooks system

This package provides a modular hook system for addon lifecycle management.
"""

from .hook_types import HookType
from .hook_context import <PERSON><PERSON>ontex<PERSON>
from .addon_hook import <PERSON><PERSON><PERSON><PERSON>
from .hook_registry import HookRegistry, get_hook_registry
from .hook_decorators import (
    pre_install_hook,
    post_install_hook,
    pre_uninstall_hook,
    post_uninstall_hook,
    pre_upgrade_hook,
    post_upgrade_hook,
)

__all__ = [
    'HookType',
    'HookContext',
    'AddonHook',
    'HookRegistry',
    'get_hook_registry',
    'pre_install_hook',
    'post_install_hook',
    'pre_uninstall_hook',
    'post_uninstall_hook',
    'pre_upgrade_hook',
    'post_upgrade_hook',
]
