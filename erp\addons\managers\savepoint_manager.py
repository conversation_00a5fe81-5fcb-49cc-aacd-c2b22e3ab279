"""
Savepoint management for dependency-aware addon installations

This module provides the SavepointManager class that handles savepoint creation,
management, and rollback for dependency-aware installations. Each addon gets
its own savepoint, but dependencies are preserved even if dependent addons fail.
"""
import time
from typing import Dict, List, Optional, TYPE_CHECKING
from dataclasses import dataclass
from enum import Enum

if TYPE_CHECKING:
    from ...environment import Environment

from ...logging import get_logger

logger = get_logger(__name__)


class SavepointState(Enum):
    """States for savepoint lifecycle"""
    CREATED = "created"
    COMMITTED = "committed"
    ROLLED_BACK = "rolled_back"


@dataclass
class SavepointInfo:
    """Information about a savepoint"""
    name: str
    addon_name: str
    is_dependency: bool
    state: SavepointState
    created_at: float
    parent_savepoint: Optional[str] = None


class SavepointManager:
    """
    Manages savepoints for dependency-aware addon installations.
    
    This manager ensures that:
    1. Each addon gets its own savepoint
    2. Dependencies are installed with their own savepoints
    3. Dependency savepoints are committed when dependencies succeed
    4. Main addon failures only rollback the main addon, not dependencies
    5. Proper cleanup of savepoints
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._savepoints: Dict[str, SavepointInfo] = {}
        self._addon_savepoints: Dict[str, str] = {}  # addon_name -> savepoint_name
        self._session_id: Optional[str] = None  # Current installation session

    def set_session_id(self, session_id: str):
        """Set the current installation session ID"""
        self._session_id = session_id
        self.logger.debug(f"SavepointManager now tracking session {session_id}")

    def create_savepoint(self, addon_name: str, is_dependency: bool = False) -> str:
        """
        Create a savepoint for an addon installation.

        Args:
            addon_name: Name of the addon
            is_dependency: Whether this addon is being installed as a dependency

        Returns:
            Savepoint name
        """
        timestamp = int(time.time() * 1000)
        savepoint_name = f"addon_install_{addon_name}_{timestamp}"
        
        savepoint_info = SavepointInfo(
            name=savepoint_name,
            addon_name=addon_name,
            is_dependency=is_dependency,
            state=SavepointState.CREATED,
            created_at=time.time()
        )
        
        self._savepoints[savepoint_name] = savepoint_info
        self._addon_savepoints[addon_name] = savepoint_name

        # Register savepoint with lifecycle tracker if session is active
        if self._session_id:
            from .lifecycle_tracker import get_lifecycle_tracker
            lifecycle_tracker = get_lifecycle_tracker()
            # Note: We'll register this as async in the execute_with_savepoint method

        self.logger.debug(f"Created savepoint '{savepoint_name}' for addon '{addon_name}' (dependency: {is_dependency})")
        return savepoint_name
    
    async def execute_with_savepoint(self, addon_name: str, env: 'Environment', 
                                   installation_func, is_dependency: bool = False) -> bool:
        """
        Execute addon installation within a savepoint.
        
        Args:
            addon_name: Name of the addon to install
            env: Environment for database operations
            installation_func: Function to execute for installation
            is_dependency: Whether this addon is being installed as a dependency
            
        Returns:
            True if installation successful, False otherwise
        """
        savepoint_name = self.create_savepoint(addon_name, is_dependency)

        # Register savepoint with lifecycle tracker if session is active
        if self._session_id:
            from .lifecycle_tracker import get_lifecycle_tracker
            lifecycle_tracker = get_lifecycle_tracker()
            await lifecycle_tracker.register_savepoint(self._session_id, savepoint_name, addon_name)

        try:
            # Create the actual database savepoint
            await env.cr.savepoint(savepoint_name)
            
            # Execute the installation
            success = await installation_func()
            
            if success:
                if is_dependency:
                    # For dependencies, commit the savepoint immediately
                    # This ensures dependencies remain even if main addon fails
                    await self._commit_savepoint(savepoint_name, env, addon_name)
                    self.logger.info(f"✅ Dependency '{addon_name}' installed and committed")
                else:
                    # For main addons, release the savepoint (will be committed with transaction)
                    await env.cr.release_savepoint(savepoint_name)
                    self._savepoints[savepoint_name].state = SavepointState.COMMITTED

                    # Notify lifecycle tracker of savepoint commit
                    if self._session_id:
                        from .lifecycle_tracker import get_lifecycle_tracker
                        lifecycle_tracker = get_lifecycle_tracker()
                        await lifecycle_tracker.mark_savepoint_committed(self._session_id, savepoint_name, addon_name)

                    self.logger.info(f"✅ Addon '{addon_name}' installed successfully")

                return True
            else:
                # Installation failed, rollback
                await self._rollback_savepoint(savepoint_name, env)
                return False
                
        except Exception as e:
            self.logger.error(f"Exception during installation of '{addon_name}': {e}")
            try:
                await self._rollback_savepoint(savepoint_name, env)
            except Exception as rollback_error:
                self.logger.error(f"Failed to rollback savepoint '{savepoint_name}': {rollback_error}")
            return False
    
    async def _commit_savepoint(self, savepoint_name: str, env: 'Environment', addon_name: str):
        """Commit a savepoint (for dependencies)"""
        try:
            await env.cr.release_savepoint(savepoint_name)
            self._savepoints[savepoint_name].state = SavepointState.COMMITTED

            # Notify lifecycle tracker of savepoint commit
            if self._session_id:
                from .lifecycle_tracker import get_lifecycle_tracker
                lifecycle_tracker = get_lifecycle_tracker()
                await lifecycle_tracker.mark_savepoint_committed(self._session_id, savepoint_name, addon_name)

            self.logger.debug(f"Committed savepoint '{savepoint_name}'")
        except Exception as e:
            self.logger.error(f"Failed to commit savepoint '{savepoint_name}': {e}")
            raise
    
    async def _rollback_savepoint(self, savepoint_name: str, env: 'Environment'):
        """Rollback a savepoint"""
        try:
            await env.cr.rollback_to_savepoint(savepoint_name)
            await env.cr.release_savepoint(savepoint_name)
            self._savepoints[savepoint_name].state = SavepointState.ROLLED_BACK
            self.logger.debug(f"Rolled back savepoint '{savepoint_name}'")
        except Exception as e:
            self.logger.error(f"Failed to rollback savepoint '{savepoint_name}': {e}")
            raise
    
    def get_savepoint_info(self, addon_name: str) -> Optional[SavepointInfo]:
        """Get savepoint information for an addon"""
        savepoint_name = self._addon_savepoints.get(addon_name)
        if savepoint_name:
            return self._savepoints.get(savepoint_name)
        return None
    
    def get_all_savepoints(self) -> Dict[str, SavepointInfo]:
        """Get all savepoint information"""
        return self._savepoints.copy()
    
    def cleanup_completed_savepoints(self):
        """Clean up savepoints that have been committed or rolled back"""
        completed_savepoints = [
            name for name, info in self._savepoints.items()
            if info.state in (SavepointState.COMMITTED, SavepointState.ROLLED_BACK)
        ]
        
        for savepoint_name in completed_savepoints:
            savepoint_info = self._savepoints[savepoint_name]
            del self._savepoints[savepoint_name]
            if savepoint_info.addon_name in self._addon_savepoints:
                del self._addon_savepoints[savepoint_info.addon_name]
        
        if completed_savepoints:
            self.logger.debug(f"Cleaned up {len(completed_savepoints)} completed savepoints")
    
    def get_installation_summary(self) -> Dict[str, List[str]]:
        """Get a summary of installation results by state"""
        summary = {
            "committed": [],
            "rolled_back": [],
            "pending": []
        }
        
        for savepoint_info in self._savepoints.values():
            if savepoint_info.state == SavepointState.COMMITTED:
                summary["committed"].append(savepoint_info.addon_name)
            elif savepoint_info.state == SavepointState.ROLLED_BACK:
                summary["rolled_back"].append(savepoint_info.addon_name)
            else:
                summary["pending"].append(savepoint_info.addon_name)
        
        return summary
