/**
 * Database List Enhanced - JavaScript functionality
 * Handles modal interactions, database operations, and UI behaviors
 */

// Tailwind CSS configuration
tailwind.config = {
    theme: {
        extend: {
            fontFamily: {
                'sans': ['Inter', 'system-ui', 'sans-serif'],
            },
            colors: {
                'primary': {
                    50: '#eff6ff',
                    500: '#3b82f6',
                    600: '#2563eb',
                    700: '#1d4ed8',
                }
            }
        }
    }
};

// Modal functions
function showCreateDatabaseModal() {
    const modal = document.getElementById('createDatabaseModal');
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    document.getElementById('dbName').focus();
}

function hideCreateDatabaseModal() {
    const modal = document.getElementById('createDatabaseModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    document.getElementById('createDatabaseForm').reset();
}

// Form submission
async function handleCreateDatabase(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = {
        name: formData.get('name'),
        language: formData.get('language'),
        demo: formData.has('demo')
    };

    try {
        const response = await fetch('/api/databases', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok && result.success) {
            hideCreateDatabaseModal();
            alert('Database created successfully! Redirecting...');
            window.location.href = '/home?db=' + encodeURIComponent(data.name);
        } else {
            throw new Error(result.detail || result.message || 'Failed to create database');
        }
    } catch (error) {
        console.error('Error creating database:', error);
        alert('Failed to create database: ' + error.message);
    }
}

function connectToDatabase(dbName) {
    // Add loading state to the clicked button
    const button = event.target.closest('button');
    if (button) {
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Connecting...';
        button.disabled = true;
        button.classList.add('opacity-75');
    }

    // Navigate to the database
    window.location.href = '/home?db=' + encodeURIComponent(dbName);
}



function initializeDatabase(dbName) {
    // Add loading state to the clicked button
    const button = event.target.closest('button');
    if (button) {
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Initializing...';
        button.disabled = true;
        button.classList.add('opacity-75');
    }

    // Initialize the database (this would typically make an API call)
    // For now, we'll simulate the process
    setTimeout(() => {
        alert(`Database ${dbName} initialization started. Please refresh the page to see the updated status.`);
        window.location.reload();
    }, 2000);
}

function initializeDatabase(dbName) {
    if (confirm('This will initialize the database with the base module. This process may take a few minutes. Continue?')) {
        // Add loading state to the clicked card
        const card = document.querySelector(`[data-dbname="${dbName}"]`);
        if (card) {
            const button = card.querySelector('button');
            if (button) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Initializing...';
                button.disabled = true;
                button.classList.add('opacity-75');
            }
        }

        // For now, redirect to home which will trigger initialization through middleware
        // In a full implementation, you might want to call a specific initialization endpoint
        alert('Database initialization will begin when you connect. Please wait for the process to complete.');
        window.location.href = '/home?db=' + encodeURIComponent(dbName);
    }
}

// Global variable to store the database name being removed
let databaseToRemove = null;

function showRemoveDatabaseModal(dbName) {
    databaseToRemove = dbName;
    const modal = document.getElementById('removeDatabaseModal');
    const dbNameElement = document.getElementById('removeDatabaseName');
    const confirmInput = document.getElementById('confirmDatabaseName');
    const confirmButton = document.getElementById('confirmRemoveButton');

    // Set database name in modal
    dbNameElement.textContent = dbName;

    // Clear and focus confirmation input
    confirmInput.value = '';
    confirmButton.disabled = true;

    // Show modal
    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // Focus on input after modal is shown
    setTimeout(() => {
        confirmInput.focus();
    }, 100);
}

function hideRemoveDatabaseModal() {
    const modal = document.getElementById('removeDatabaseModal');
    const confirmInput = document.getElementById('confirmDatabaseName');
    const confirmButton = document.getElementById('confirmRemoveButton');
    const loadingState = document.getElementById('removeLoadingState');

    // Hide modal
    modal.classList.add('hidden');
    modal.classList.remove('flex');

    // Reset state
    confirmInput.value = '';
    confirmButton.disabled = true;
    loadingState.classList.add('hidden');
    databaseToRemove = null;
}

async function confirmDatabaseRemoval() {
    if (!databaseToRemove) return;

    const confirmButton = document.getElementById('confirmRemoveButton');
    const loadingState = document.getElementById('removeLoadingState');

    try {
        // Show loading state
        confirmButton.disabled = true;
        loadingState.classList.remove('hidden');

        const response = await fetch(`/api/databases/${encodeURIComponent(databaseToRemove)}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (response.ok && result.success) {
            // Success - hide modal and reload page
            hideRemoveDatabaseModal();
            showNotification('Database removed successfully', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            throw new Error(result.detail || result.message || 'Failed to remove database');
        }
    } catch (error) {
        console.error('Error removing database:', error);
        loadingState.classList.add('hidden');
        confirmButton.disabled = false;
        showNotification('Failed to remove database: ' + error.message, 'error');
    }
}

// Legacy function for backward compatibility
async function deleteDatabase(dbName) {
    showRemoveDatabaseModal(dbName);
}

function refreshDatabases() {
    window.location.reload();
}

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    // Set colors based on type
    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function backupDatabase(dbName) {
    // Add loading state to the clicked button
    const button = event.target.closest('button');
    if (button) {
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        button.classList.add('opacity-75');
    }

    // Simulate backup process (in real implementation, this would call an API)
    setTimeout(() => {
        alert(`Backup for database ${dbName} has been initiated. You will be notified when it's complete.`);
        if (button) {
            button.innerHTML = '<i class="fas fa-download text-sm"></i>';
            button.disabled = false;
            button.classList.remove('opacity-75');
        }
    }, 2000);
}

function manageDatabaseSettings(dbName) {
    // Navigate to database settings page
    window.location.href = `/settings?db=${encodeURIComponent(dbName)}`;
}

// Search functionality
function filterDatabases() {
    const searchTerm = document.getElementById('database-search').value.toLowerCase();
    const cards = document.querySelectorAll('[data-dbname]');

    cards.forEach(card => {
        const dbName = card.dataset.dbname.toLowerCase();
        const shouldShow = dbName.includes(searchTerm);
        card.style.display = shouldShow ? '' : 'none';
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Handle modal close on backdrop click
    const modal = document.getElementById('createDatabaseModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                hideCreateDatabaseModal();
            }
        });
    }

    // Handle escape key to close modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const createModal = document.getElementById('createDatabaseModal');
            const removeModal = document.getElementById('removeDatabaseModal');

            if (createModal && !createModal.classList.contains('hidden')) {
                hideCreateDatabaseModal();
            } else if (removeModal && !removeModal.classList.contains('hidden')) {
                hideRemoveDatabaseModal();
            }
        }
    });

    // Handle remove database modal events
    const removeModal = document.getElementById('removeDatabaseModal');
    if (removeModal) {
        // Handle modal close on backdrop click
        removeModal.addEventListener('click', function(e) {
            if (e.target === removeModal) {
                hideRemoveDatabaseModal();
            }
        });
    }

    // Handle confirmation input validation
    const confirmInput = document.getElementById('confirmDatabaseName');
    const confirmButton = document.getElementById('confirmRemoveButton');

    if (confirmInput && confirmButton) {
        confirmInput.addEventListener('input', function() {
            const isValid = this.value === databaseToRemove;
            confirmButton.disabled = !isValid;
        });

        // Handle Enter key in confirmation input
        confirmInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !confirmButton.disabled) {
                confirmDatabaseRemoval();
            }
        });
    }
});
