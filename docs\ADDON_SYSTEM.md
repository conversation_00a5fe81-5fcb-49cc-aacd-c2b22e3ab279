# Addon System Documentation

## Overview

The ERP system features a clean, Odoo-inspired addon architecture with clear separation between discovery, registration, and installation processes.

## Terminology and Process Separation

### 1. Discovery
- **What**: Manual identification of addons in the filesystem
- **Class**: Manual process
- **Purpose**: Manually identify valid addons with manifests
- **Output**: Manual identification of addon locations

### 2. Registration
- **What**: Manual importing of Python modules (no automatic registration)
- **Class**: Manual import process
- **Purpose**: Manually import addon Python modules when needed
- **Output**: Addon modules available when explicitly imported

### 3. Installation
- **What**: Executing installation hooks and database setup
- **Class**: `AddonInstaller.install_addon()`
- **Purpose**: Execute pre/post install hooks, create database tables, setup data
- **Output**: Addon installed and ready for use

## Process Flow

```
1. Discovery    → Manual identification of addons
2. Registration → Manual import of Python modules
3. Installation → Execute hooks & DB setup
```

## Classes and Responsibilities

### Manual Addon Management
- **Purpose**: Manual addon handling (no automatic registry)
- **Process**:
  - Manual identification of addons in filesystem
  - Manual import of Python modules when needed
  - Manual management of addon state

### AddonInstaller  
- **Purpose**: Handle installation process only
- **Methods**:
  - `install_addon()` - Execute installation hooks
  - `uninstall_addon()` - Execute uninstallation hooks
  - `upgrade_addon()` - Execute upgrade hooks
  - `register_and_install_addon()` - Convenience method for both

### AddonManager
- **Purpose**: High-level lifecycle management
- **Methods**:
  - Combines registry and installer functionality
  - Provides unified interface for addon operations
  - Handles dependency resolution

## Addon Structure

### Directory Layout
```
addons/
├── my_addon/
│   ├── __init__.py           # Module initialization
│   ├── __manifest__.py       # Addon metadata
│   ├── models/              # Model definitions
│   │   ├── __init__.py
│   │   └── my_model.py
│   ├── controllers/         # HTTP controllers
│   │   ├── __init__.py
│   │   └── my_controller.py
│   ├── templates/           # XML templates
│   │   └── my_template.xml
│   ├── static/             # Static files
│   │   ├── css/
│   │   └── js/

│   └── hooks.py            # Lifecycle hooks
```

### Manifest File
```python
# __manifest__.py
{
    'name': 'My Addon',
    'version': '1.0.0',
    'description': 'Description of my addon',
    'author': 'Your Name',
    'depends': ['base'],
    'data': [
        'data/demo_data.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
```

## Lifecycle Hooks

### Hook Types
- **pre_install**: Before addon installation
- **post_install**: After addon installation
- **pre_uninstall**: Before addon removal
- **post_uninstall**: After addon removal
- **pre_upgrade**: Before addon upgrade
- **post_upgrade**: After addon upgrade

### Hook Implementation
```python
# hooks.py
from erp.addons.hooks import pre_install_hook, post_install_hook

@pre_install_hook('my_addon', priority=50)
async def pre_install(context):
    """Pre-installation hook"""
    logger = context.logger
    logger.info("Running pre-install hook for my_addon")
    
    # Custom pre-installation logic
    return True  # Return True for success, False for failure

@post_install_hook('my_addon', priority=50)
async def post_install(context):
    """Post-installation hook"""
    env = context.env
    logger = context.logger
    
    logger.info("Running post-install hook for my_addon")
    
    # Create default data
    MyModel = env['my.model']
    await MyModel.create({
        'name': 'Default Record',
        'value': 100
    })
    
    return True
```

### Hook Context
```python
class HookContext:
    env: Environment          # Database environment
    logger: Logger           # Addon-specific logger
    addon_name: str         # Name of the addon
    manifest: AddonManifest # Addon manifest data
    phase: str              # Hook phase (install/uninstall/upgrade)
```

## Import System

### AddonImportManager
- **Purpose**: Standardized addon imports
- **Pattern**: `erp.addons.[addon_name]`
- **Features**:
  - Meta path finder integration
  - Automatic module discovery
  - Import caching

### Usage Example
```python
# Import addon modules
from erp.addons.my_addon import models
from erp.addons.my_addon.controllers import my_controller

# Access addon components
MyModel = models.MyModel
controller = my_controller.MyController()
```

## Database Integration

### Database-Specific Routes
Routes can be registered per database:
```python
@route('/db_specific', methods=['GET'], database='my_db')
async def db_specific_route(self, request):
    return self.json_response({'database': 'my_db'})
```

### Memory Registry Integration
Each database has its own memory registry for:
- Model definitions
- Route registrations
- Cache data
- Runtime statistics

## Usage Examples

### Server Startup
```python
# Server startup without automatic addon loading
server_config = ServerConfig()
```

### CLI Installation
```python
# Install addon directly
installer = AddonInstaller()
success = await installer.install_addon('my_addon', env)
```

### Dependency Management
```python
# Automatic dependency resolution
manager = AddonManager()
await manager.install_addon_with_dependencies('my_addon')
```

## Benefits

1. **Clear Separation**: Each class has single responsibility
2. **Better Testing**: Can test registration and installation separately  
3. **Flexible Deployment**: Can register addons without installing them
4. **Clearer Debugging**: Know exactly which phase failed
5. **Better Documentation**: Clear terminology for each process

## Logging Output

The system provides clear logging for each phase:

```
🔍 Starting addon discovery in paths: ['/path/to/addons']
✅ Addon discovery completed: 5 addons found in 0.123s

📦 Starting addon registration process (requested: all)
📦 Registering addon 1/5: base
✅ Successfully registered addon base (1/5)
📦 Addon registration completed in 0.456s

🔧 Installing addon: my_addon
✅ Successfully installed addon my_addon in 0.789s
```

## Best Practices

1. **Follow naming conventions** for addon directories and modules
2. **Declare dependencies** properly in manifest files
3. **Use lifecycle hooks** for setup and cleanup
4. **Write comprehensive tests** for addon functionality
5. **Document addon features** in README files
6. **Handle errors gracefully** in hooks and controllers
7. **Use proper logging** for debugging and monitoring
8. **Keep addons modular** and focused on specific functionality
