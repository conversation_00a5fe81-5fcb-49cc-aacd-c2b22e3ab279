"""
Test suite for XML ID validation functionality

This module tests:
- XML ID validation during XML loading
- Strict validation mode behavior
- Error handling for missing XML IDs
- Configuration options for validation
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from erp.data.loader import DataLoader
from erp.data.xmlid_manager import XMLIDManager
from erp.data.exceptions import XMLIDNotFoundError
from erp.data.parser import ParseMode


class TestXMLIDValidation:
    """Test XML ID validation functionality"""
    
    @pytest.fixture
    def mock_db_manager(self):
        """Create mock database manager"""
        db_manager = MagicMock()
        db_manager.fetchval = AsyncMock()
        db_manager.fetchrow = AsyncMock()
        db_manager.execute = AsyncMock()
        return db_manager
    
    @pytest.fixture
    def xmlid_manager_strict(self, mock_db_manager):
        """Create XML ID manager with strict validation enabled"""
        return XMLIDManager(mock_db_manager, strict_validation=True)
    
    @pytest.fixture
    def xmlid_manager_permissive(self, mock_db_manager):
        """Create XML ID manager with strict validation disabled"""
        return XMLIDManager(mock_db_manager, strict_validation=False)
    
    @pytest.fixture
    def data_loader_strict(self, mock_db_manager):
        """Create data loader with strict XML ID validation"""
        config = {
            'strict_xmlid_validation': True,
            'parse_mode': 'strict',
            'continue_on_error': False
        }
        return DataLoader(mock_db_manager, config)
    
    @pytest.fixture
    def data_loader_permissive(self, mock_db_manager):
        """Create data loader with permissive XML ID validation"""
        config = {
            'strict_xmlid_validation': False,
            'parse_mode': 'permissive',
            'continue_on_error': True
        }
        return DataLoader(mock_db_manager, config)

    @pytest.mark.asyncio
    async def test_xmlid_manager_strict_validation_enabled(self, xmlid_manager_strict):
        """Test that strict validation is properly enabled"""
        assert xmlid_manager_strict._strict_validation is True
    
    @pytest.mark.asyncio
    async def test_xmlid_manager_strict_validation_disabled(self, xmlid_manager_permissive):
        """Test that strict validation is properly disabled"""
        assert xmlid_manager_permissive._strict_validation is False
    
    @pytest.mark.asyncio
    async def test_resolve_xmlid_strict_mode_missing_id(self, xmlid_manager_strict):
        """Test that strict mode throws exception for missing XML ID"""
        # Mock XML ID lookup to return None (not found)
        xmlid_manager_strict.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)
        
        with pytest.raises(XMLIDNotFoundError) as exc_info:
            await xmlid_manager_strict.resolve_xmlid_to_record_id("missing.xmlid", "test context")
        
        assert "missing.xmlid" in str(exc_info.value)
        assert "test context" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_resolve_xmlid_permissive_mode_missing_id(self, xmlid_manager_permissive):
        """Test that permissive mode returns None for missing XML ID"""
        # Mock XML ID lookup to return None (not found)
        xmlid_manager_permissive.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)
        
        result = await xmlid_manager_permissive.resolve_xmlid_to_record_id("missing.xmlid")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_resolve_xmlid_strict_mode_found_id(self, xmlid_manager_strict):
        """Test that strict mode returns record ID when XML ID is found"""
        # Mock XML ID lookup to return a valid result
        xmlid_manager_strict.xmlid_sql.xmlid_lookup = AsyncMock(return_value={
            'model': 'test.model',
            'res_id': 'record-123',
            'id': 'record-123'
        })
        
        result = await xmlid_manager_strict.resolve_xmlid_to_record_id("found.xmlid")
        assert result == 'record-123'
    
    @pytest.mark.asyncio
    async def test_enable_strict_validation_method(self, xmlid_manager_permissive):
        """Test enabling strict validation dynamically"""
        assert xmlid_manager_permissive._strict_validation is False
        
        xmlid_manager_permissive.enable_strict_validation(True)
        assert xmlid_manager_permissive._strict_validation is True
        
        xmlid_manager_permissive.enable_strict_validation(False)
        assert xmlid_manager_permissive._strict_validation is False

    @pytest.mark.asyncio
    async def test_xmlid_not_found_error_creation(self):
        """Test XMLIDNotFoundError exception creation"""
        error = XMLIDNotFoundError("test.xmlid", "test context")
        
        assert error.xml_id == "test.xmlid"
        assert error.context == "test context"
        assert "test.xmlid" in str(error)
        assert "test context" in str(error)
    
    @pytest.mark.asyncio
    async def test_xmlid_not_found_error_without_context(self):
        """Test XMLIDNotFoundError exception creation without context"""
        error = XMLIDNotFoundError("test.xmlid")
        
        assert error.xml_id == "test.xmlid"
        assert error.context is None
        assert "test.xmlid" in str(error)
        assert "test context" not in str(error)

    @pytest.mark.asyncio
    async def test_data_loader_strict_configuration(self, data_loader_strict):
        """Test that data loader properly configures strict validation"""
        assert data_loader_strict.xmlid_manager._strict_validation is True
    
    @pytest.mark.asyncio
    async def test_data_loader_permissive_configuration(self, data_loader_permissive):
        """Test that data loader properly configures permissive validation"""
        assert data_loader_permissive.xmlid_manager._strict_validation is False

    @pytest.mark.asyncio
    async def test_xml_loading_with_missing_reference_strict(self, data_loader_strict):
        """Test XML loading with missing reference in strict mode"""
        xml_content = """<?xml version="1.0" encoding="utf-8"?>
        <data>
            <record id="test_record" model="test.model">
                <field name="name">Test Record</field>
                <field name="parent_id" ref="missing.parent"/>
            </record>
        </data>"""
        
        # Mock the XML ID manager to raise exception for missing reference
        data_loader_strict.xmlid_manager.resolve_xmlid_to_record_id = AsyncMock(
            side_effect=XMLIDNotFoundError("missing.parent", "field reference")
        )
        
        # Mock other necessary methods
        with patch.object(data_loader_strict.processor_manager, 'process_data') as mock_process:
            mock_process.return_value = MagicMock(
                successful_items=0,
                total_items=1,
                errors=["Failed to resolve reference missing.parent: XML ID not found: missing.parent (in field reference)"]
            )
            
            result = await data_loader_strict.load_data_content(xml_content, "test_addon")
            
            # Should have errors due to missing XML ID
            assert len(result.get('errors', [])) > 0 or result.get('processing_result', {}).get('errors', [])

    @pytest.mark.asyncio
    async def test_xml_loading_with_missing_reference_permissive(self, data_loader_permissive):
        """Test XML loading with missing reference in permissive mode"""
        xml_content = """<?xml version="1.0" encoding="utf-8"?>
        <data>
            <record id="test_record" model="test.model">
                <field name="name">Test Record</field>
                <field name="parent_id" ref="missing.parent"/>
            </record>
        </data>"""
        
        # Mock the XML ID manager to return None for missing reference
        data_loader_permissive.xmlid_manager.resolve_xmlid_to_record_id = AsyncMock(return_value=None)
        
        # Mock other necessary methods
        with patch.object(data_loader_permissive.processor_manager, 'process_data') as mock_process:
            mock_process.return_value = MagicMock(
                successful_items=1,
                total_items=1,
                errors=[]
            )
            
            result = await data_loader_permissive.load_data_content(xml_content, "test_addon")
            
            # Should process successfully despite missing XML ID
            assert result.get('processing_result', {}).get('successful_items', 0) >= 0

    @pytest.mark.asyncio
    async def test_processor_manager_xmlid_validation_configuration(self, mock_db_manager):
        """Test that processor manager properly configures XML ID validation"""
        from erp.data.processors.manager import ProcessorManager
        
        manager = ProcessorManager(mock_db_manager)
        
        # Mock processors with xmlid_manager attribute
        for processor in manager.processors.values():
            processor.xmlid_manager = MagicMock()
            processor.xmlid_manager.enable_strict_validation = MagicMock()
        
        # Configure strict validation
        manager.configure_xmlid_validation(True)
        
        # Verify all processors were configured
        for processor in manager.processors.values():
            if hasattr(processor, 'xmlid_manager'):
                processor.xmlid_manager.enable_strict_validation.assert_called_with(True)

    @pytest.mark.asyncio
    async def test_xml_data_loader_component_strict_validation(self, mock_db_manager):
        """Test XML data loader component with strict validation"""
        from erp.addons.installers.components.xml_data_loader import XMLDataLoader
        
        xml_loader = XMLDataLoader()
        
        # Mock path resolver and manifest loading
        xml_loader.path_resolver.find_addon_path = MagicMock(return_value="/path/to/addon")
        xml_loader.path_resolver.get_addon_manifest_path = MagicMock(return_value="/path/to/manifest")
        xml_loader._load_manifest = MagicMock(return_value={'data': ['test_data.xml']})
        xml_loader.path_resolver.get_addon_data_files_path = MagicMock(return_value="/path/to/data.xml")
        
        # Mock DataLoader to verify configuration
        with patch('erp.data.loader.DataLoader') as mock_loader_class:
            mock_loader_instance = MagicMock()
            mock_loader_instance.load_data_file = AsyncMock(return_value={'loaded': 1, 'errors': []})
            mock_loader_class.return_value = mock_loader_instance
            
            await xml_loader.load_addon_data_files(mock_db_manager, "test_addon", strict_xmlid_validation=True)
            
            # Verify DataLoader was created with strict validation config
            mock_loader_class.assert_called_once()
            args, kwargs = mock_loader_class.call_args
            assert args[0] == mock_db_manager
            # Check if config was passed as positional or keyword argument
            if len(args) > 1:
                config = args[1]
            else:
                config = kwargs.get('config', {})
            assert config.get('strict_xmlid_validation') is True

    @pytest.mark.asyncio
    async def test_multiple_missing_xmlids_strict_mode(self, xmlid_manager_strict):
        """Test multiple missing XML IDs in strict mode"""
        xmlid_manager_strict.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)

        missing_ids = ["missing.id1", "missing.id2", "missing.id3"]

        for xml_id in missing_ids:
            with pytest.raises(XMLIDNotFoundError) as exc_info:
                await xmlid_manager_strict.resolve_xmlid_to_record_id(xml_id)
            assert xml_id in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_xmlid_with_module_prefix(self, xmlid_manager_strict):
        """Test XML ID resolution with module prefix"""
        xmlid_manager_strict.xmlid_sql.xmlid_lookup = AsyncMock(return_value={
            'model': 'test.model',
            'res_id': 'record-456',
            'id': 'record-456'
        })

        result = await xmlid_manager_strict.resolve_xmlid_to_record_id("module.test_record")
        assert result == 'record-456'

    @pytest.mark.asyncio
    async def test_xmlid_without_module_prefix(self, xmlid_manager_strict):
        """Test XML ID resolution without module prefix"""
        xmlid_manager_strict.xmlid_sql.xmlid_lookup = AsyncMock(return_value={
            'model': 'test.model',
            'res_id': 'record-789',
            'id': 'record-789'
        })

        result = await xmlid_manager_strict.resolve_xmlid_to_record_id("test_record")
        assert result == 'record-789'

    @pytest.mark.asyncio
    async def test_context_information_in_error(self, xmlid_manager_strict):
        """Test that context information is properly included in error messages"""
        xmlid_manager_strict.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)

        contexts = [
            "field reference in res.partner",
            "menu action reference",
            "security rule reference",
            "workflow transition"
        ]

        for context in contexts:
            with pytest.raises(XMLIDNotFoundError) as exc_info:
                await xmlid_manager_strict.resolve_xmlid_to_record_id("missing.id", context)

            error_message = str(exc_info.value)
            assert "missing.id" in error_message
            assert context in error_message
