"""
XML ID Manager for ERP system

This module provides XML ID management functionality that is completely
independent of AppRegistry and model registry, using only raw SQL operations.
"""

from typing import Dict, List, Any, Optional

from ..logging import get_logger
from ..database.connection.manager import DatabaseManager
from .sql_helpers import SQLHelpers, XMLIDSQLHelpers, ModelSQLHelpers
from .exceptions import XMLIDNotFoundError


class XMLIDManager:
    """
    XML ID Manager that provides XML ID operations without AppRegistry dependency.
    
    This manager handles all XML ID related operations using raw SQL queries,
    making it completely independent of the model registry and AppRegistry.
    """
    
    def __init__(self, db_manager: DatabaseManager, strict_validation: bool = False):
        """
        Initialize XML ID manager.

        Args:
            db_manager: Database manager instance
            strict_validation: Whether to throw exceptions for missing XML IDs
        """
        self.db_manager = db_manager
        self.logger = get_logger(__name__)
        self._strict_validation = strict_validation

        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.xmlid_sql = XMLIDSQLHelpers(self.sql)
        self.model_sql = ModelSQLHelpers(self.sql)

        # Cache for performance
        self._xmlid_cache = {}
        self._cache_enabled = True
    
    def enable_cache(self, enabled: bool = True):
        """
        Enable or disable XML ID caching.
        
        Args:
            enabled: Whether to enable caching
        """
        self._cache_enabled = enabled
        if not enabled:
            self._xmlid_cache.clear()
    
    def clear_cache(self):
        """Clear the XML ID cache."""
        self._xmlid_cache.clear()

    def enable_strict_validation(self, enabled: bool = True):
        """
        Enable or disable strict XML ID validation.

        Args:
            enabled: Whether to enable strict validation
        """
        self._strict_validation = enabled
    
    async def resolve_xmlid(self, xml_id: str) -> Optional[Dict[str, Any]]:
        """
        Resolve an XML ID to model and record ID.
        
        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            
        Returns:
            Dictionary with 'model' and 'res_id' keys, or None if not found
        """
        # Check cache first
        if self._cache_enabled and xml_id in self._xmlid_cache:
            return self._xmlid_cache[xml_id]
        
        result = await self.xmlid_sql.xmlid_lookup(xml_id)
        
        # Cache the result
        if self._cache_enabled and result:
            self._xmlid_cache[xml_id] = result
        
        return result
    
    async def resolve_xmlid_to_record_id(self, xml_id: str, context: str = None) -> Optional[str]:
        """
        Resolve an XML ID to record ID only.

        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            context: Additional context for error reporting

        Returns:
            Record ID as string, or None if not found

        Raises:
            XMLIDNotFoundError: If strict_validation is enabled and XML ID not found
        """
        result = await self.resolve_xmlid(xml_id)

        if result:
            return result['res_id']
        elif self._strict_validation:
            raise XMLIDNotFoundError(xml_id, context)
        else:
            return None
    
    async def resolve_xmlid_to_record(self, xml_id: str) -> Optional[Dict[str, Any]]:
        """
        Resolve an XML ID to the actual record data.
        
        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            
        Returns:
            Dictionary representing the record, or None if not found
        """
        xmlid_result = await self.resolve_xmlid(xml_id)
        if not xmlid_result:
            return None
        
        # Get the actual record
        record = await self.model_sql.find_record_by_id(
            xmlid_result['model'], 
            xmlid_result['res_id']
        )
        
        return record
    
    async def create_xmlid_mapping(self, module: str, name: str, model: str, res_id: str, noupdate: bool = False) -> bool:
        """
        Create an XML ID mapping.
        
        Args:
            module: Module name
            name: XML ID name
            model: Model name
            res_id: Record ID
            noupdate: No update flag
            
        Returns:
            True if successful, False otherwise
        """
        success = await self.xmlid_sql.create_or_update_xmlid(module, name, model, res_id, noupdate)
        
        # Update cache
        if success and self._cache_enabled:
            full_xml_id = f"{module}.{name}"
            self._xmlid_cache[full_xml_id] = {
                'model': model,
                'res_id': res_id,
                'id': res_id
            }
        
        return success
    
    async def update_xmlid_mapping(self, xml_id: str, model: str = None, res_id: str = None) -> bool:
        """
        Update an existing XML ID mapping.
        
        Args:
            xml_id: XML ID in format 'module.name'
            model: New model name (optional)
            res_id: New record ID (optional)
            
        Returns:
            True if successful, False otherwise
        """
        if '.' not in xml_id:
            self.logger.error(f"Invalid XML ID format: {xml_id}. Expected 'module.name'")
            return False
        
        module, name = xml_id.split('.', 1)
        
        # Get current mapping
        current = await self.xmlid_sql.xmlid_lookup(xml_id)
        if not current:
            self.logger.error(f"XML ID not found: {xml_id}")
            return False
        
        # Use current values if not provided
        new_model = model or current['model']
        new_res_id = res_id or current['res_id']
        
        success = await self.xmlid_sql.create_or_update_xmlid(module, name, new_model, new_res_id, False)
        
        # Update cache
        if success and self._cache_enabled:
            self._xmlid_cache[xml_id] = {
                'model': new_model,
                'res_id': new_res_id,
                'id': new_res_id
            }
        
        return success
    
    async def delete_xmlid_mapping(self, xml_id: str) -> bool:
        """
        Delete an XML ID mapping.
        
        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            
        Returns:
            True if successful, False otherwise
        """
        success = await self.xmlid_sql.delete_xmlid(xml_id)
        
        # Remove from cache
        if success and self._cache_enabled:
            self._xmlid_cache.pop(xml_id, None)
        
        return success
    
    async def get_xmlid_for_record(self, model: str, res_id: str) -> Optional[str]:
        """
        Get XML ID for a record.
        
        Args:
            model: Model name
            res_id: Record ID
            
        Returns:
            XML ID in format 'module.name', or None if not found
        """
        return await self.xmlid_sql.get_xmlid_by_record(model, res_id)
    
    async def xmlid_exists(self, xml_id: str) -> bool:
        """
        Check if an XML ID exists.
        
        Args:
            xml_id: XML ID in format 'module.name' or just 'name'
            
        Returns:
            True if XML ID exists, False otherwise
        """
        result = await self.resolve_xmlid(xml_id)
        return result is not None
    
    async def get_all_xmlids_for_module(self, module: str) -> List[Dict[str, Any]]:
        """
        Get all XML IDs for a specific module.
        
        Args:
            module: Module name
            
        Returns:
            List of XML ID mappings for the module
        """
        await self.xmlid_sql.ensure_table_exists()
        return await self.sql.find_records('ir_model_data', {'module': module})
    
    async def get_all_xmlids_for_model(self, model: str) -> List[Dict[str, Any]]:
        """
        Get all XML IDs for a specific model.
        
        Args:
            model: Model name
            
        Returns:
            List of XML ID mappings for the model
        """
        await self.xmlid_sql.ensure_table_exists()
        return await self.sql.find_records('ir_model_data', {'model': model})
    
    async def cleanup_orphaned_xmlids(self) -> int:
        """
        Clean up XML IDs that point to non-existent records.
        
        Returns:
            Number of orphaned XML IDs removed
        """
        await self.xmlid_sql.ensure_table_exists()
        
        # Get all XML ID mappings
        all_xmlids = await self.sql.find_records('ir_model_data', {})
        
        orphaned_count = 0
        for xmlid_record in all_xmlids:
            model = xmlid_record['model']
            res_id = xmlid_record['res_id']
            
            # Check if the referenced record exists
            record = await self.model_sql.find_record_by_id(model, res_id)
            if not record:
                # Record doesn't exist, remove the XML ID mapping
                xml_id = f"{xmlid_record['module']}.{xmlid_record['name']}"
                success = await self.delete_xmlid_mapping(xml_id)
                if success:
                    orphaned_count += 1
                    self.logger.debug(f"Removed orphaned XML ID: {xml_id}")
        
        if orphaned_count > 0:
            self.logger.info(f"Cleaned up {orphaned_count} orphaned XML IDs")
        
        return orphaned_count
    
    async def validate_xmlid_integrity(self) -> Dict[str, Any]:
        """
        Validate the integrity of all XML ID mappings.
        
        Returns:
            Dictionary with validation results
        """
        await self.xmlid_sql.ensure_table_exists()
        
        all_xmlids = await self.sql.find_records('ir_model_data', {})
        
        results = {
            'total_xmlids': len(all_xmlids),
            'valid_xmlids': 0,
            'orphaned_xmlids': 0,
            'invalid_models': 0,
            'errors': []
        }
        
        for xmlid_record in all_xmlids:
            model = xmlid_record['model']
            res_id = xmlid_record['res_id']
            xml_id = f"{xmlid_record['module']}.{xmlid_record['name']}"
            
            try:
                # Check if model table exists
                table_name = model.replace('.', '_')
                if not await self.sql.table_exists(table_name):
                    results['invalid_models'] += 1
                    results['errors'].append(f"Model table {table_name} does not exist for XML ID {xml_id}")
                    continue
                
                # Check if record exists
                record = await self.model_sql.find_record_by_id(model, res_id)
                if record:
                    results['valid_xmlids'] += 1
                else:
                    results['orphaned_xmlids'] += 1
                    results['errors'].append(f"Record {res_id} not found in {model} for XML ID {xml_id}")
                    
            except Exception as e:
                results['errors'].append(f"Error validating XML ID {xml_id}: {e}")
        
        return results
