<?xml version="1.0" encoding="utf-8"?>
<data>
    <!-- Indian States and Union Territories -->
    
    <record id="state_in_ap" model="res.country.state">
        <field name="name">Andhra Pradesh</field>
        <field name="code">AP</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ar" model="res.country.state">
        <field name="name">Arunachal Pradesh</field>
        <field name="code">AR</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_as" model="res.country.state">
        <field name="name">Assam</field>
        <field name="code">AS</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_br" model="res.country.state">
        <field name="name">Bihar</field>
        <field name="code">BR</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ct" model="res.country.state">
        <field name="name">Chhattisgarh</field>
        <field name="code">CT</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ga" model="res.country.state">
        <field name="name">Goa</field>
        <field name="code">GA</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_gj" model="res.country.state">
        <field name="name">Gujarat</field>
        <field name="code">GJ</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_hr" model="res.country.state">
        <field name="name">Haryana</field>
        <field name="code">HR</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_hp" model="res.country.state">
        <field name="name">Himachal Pradesh</field>
        <field name="code">HP</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_jh" model="res.country.state">
        <field name="name">Jharkhand</field>
        <field name="code">JH</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ka" model="res.country.state">
        <field name="name">Karnataka</field>
        <field name="code">KA</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_kl" model="res.country.state">
        <field name="name">Kerala</field>
        <field name="code">KL</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_mp" model="res.country.state">
        <field name="name">Madhya Pradesh</field>
        <field name="code">MP</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_mh" model="res.country.state">
        <field name="name">Maharashtra</field>
        <field name="code">MH</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_mn" model="res.country.state">
        <field name="name">Manipur</field>
        <field name="code">MN</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ml" model="res.country.state">
        <field name="name">Meghalaya</field>
        <field name="code">ML</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_mz" model="res.country.state">
        <field name="name">Mizoram</field>
        <field name="code">MZ</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_nl" model="res.country.state">
        <field name="name">Nagaland</field>
        <field name="code">NL</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_or" model="res.country.state">
        <field name="name">Odisha</field>
        <field name="code">OR</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_pb" model="res.country.state">
        <field name="name">Punjab</field>
        <field name="code">PB</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_rj" model="res.country.state">
        <field name="name">Rajasthan</field>
        <field name="code">RJ</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_sk" model="res.country.state">
        <field name="name">Sikkim</field>
        <field name="code">SK</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_tn" model="res.country.state">
        <field name="name">Tamil Nadu</field>
        <field name="code">TN</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_tg" model="res.country.state">
        <field name="name">Telangana</field>
        <field name="code">TG</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_tr" model="res.country.state">
        <field name="name">Tripura</field>
        <field name="code">TR</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_up" model="res.country.state">
        <field name="name">Uttar Pradesh</field>
        <field name="code">UP</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ut" model="res.country.state">
        <field name="name">Uttarakhand</field>
        <field name="code">UT</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_wb" model="res.country.state">
        <field name="name">West Bengal</field>
        <field name="code">WB</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <!-- Union Territories -->
    <record id="state_in_an" model="res.country.state">
        <field name="name">Andaman and Nicobar Islands</field>
        <field name="code">AN</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ch" model="res.country.state">
        <field name="name">Chandigarh</field>
        <field name="code">CH</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_dn" model="res.country.state">
        <field name="name">Dadra and Nagar Haveli and Daman and Diu</field>
        <field name="code">DN</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_dl" model="res.country.state">
        <field name="name">Delhi</field>
        <field name="code">DL</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_jk" model="res.country.state">
        <field name="name">Jammu and Kashmir</field>
        <field name="code">JK</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_la" model="res.country.state">
        <field name="name">Ladakh</field>
        <field name="code">LA</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_ld" model="res.country.state">
        <field name="name">Lakshadweep</field>
        <field name="code">LD</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <record id="state_in_py" model="res.country.state">
        <field name="name">Puducherry</field>
        <field name="code">PY</field>
        <field name="country_id" ref="country_in"/>
    </record>

    <!-- US States (Major ones) -->
    <record id="state_us_ca" model="res.country.state">
        <field name="name">California</field>
        <field name="code">CA</field>
        <field name="country_id" ref="country_us"/>
    </record>

    <record id="state_us_ny" model="res.country.state">
        <field name="name">New York</field>
        <field name="code">NY</field>
        <field name="country_id" ref="country_us"/>
    </record>

    <record id="state_us_tx" model="res.country.state">
        <field name="name">Texas</field>
        <field name="code">TX</field>
        <field name="country_id" ref="country_us"/>
    </record>

    <record id="state_us_fl" model="res.country.state">
        <field name="name">Florida</field>
        <field name="code">FL</field>
        <field name="country_id" ref="country_us"/>
    </record>

    <!-- Canadian Provinces -->
    <record id="state_ca_on" model="res.country.state">
        <field name="name">Ontario</field>
        <field name="code">ON</field>
        <field name="country_id" ref="country_ca"/>
    </record>

    <record id="state_ca_qc" model="res.country.state">
        <field name="name">Quebec</field>
        <field name="code">QC</field>
        <field name="country_id" ref="country_ca"/>
    </record>

    <record id="state_ca_bc" model="res.country.state">
        <field name="name">British Columbia</field>
        <field name="code">BC</field>
        <field name="country_id" ref="country_ca"/>
    </record>

    <!-- Australian States -->
    <record id="state_au_nsw" model="res.country.state">
        <field name="name">New South Wales</field>
        <field name="code">NSW</field>
        <field name="country_id" ref="country_au"/>
    </record>

    <record id="state_au_vic" model="res.country.state">
        <field name="name">Victoria</field>
        <field name="code">VIC</field>
        <field name="country_id" ref="country_au"/>
    </record>

    <record id="state_au_qld" model="res.country.state">
        <field name="name">Queensland</field>
        <field name="code">QLD</field>
        <field name="country_id" ref="country_au"/>
    </record>

</data>
