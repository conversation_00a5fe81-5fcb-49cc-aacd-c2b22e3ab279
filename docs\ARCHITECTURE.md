# ERP System Architecture

## Overview

This ERP system is built with an Odoo-inspired architecture featuring an addon-based system, ASGI-only server, and comprehensive database management capabilities.

## Core Architecture Components

### 1. Server Architecture

#### ASGI-Only Design
- **FastAPI-based** server with full async support
- **No WSGI compatibility** - pure async implementation
- **Enhanced middleware** for timing, error handling, and database context
- **Uvicorn** as the ASGI server

#### Server Components
```
erp/
├── server.py              # Main ERP server class
├── server_config.py       # Server configuration and setup
├── config.py             # Configuration management
└── context.py            # AsyncLocalStorage-like context management
```

### 2. Addon System

#### Clean Separation of Concerns
- **AddonRegistry**: Discovery and Python module registration
- **AddonInstaller**: Installation hooks and database operations  
- **AddonManager**: High-level lifecycle management

#### Addon Structure
```
addons/
├── base/                 # Core addon (required)
│   ├── __manifest__.py
│   ├── models/          # Core models (ir.module, ir.model)
│   ├── controllers/     # Base controllers
│   └── hooks.py         # Installation hooks
└── [addon_name]/
    ├── __manifest__.py
    ├── models/
    ├── controllers/
    └── hooks.py
```

#### Import System
- **erp.addons.[addon_name]** import pattern
- **AddonImportManager** for standardized module loading
- **Meta path finder** integration for seamless imports

### 3. Database Architecture

#### Multi-Database Support
- **DatabaseRegistry**: Manages multiple database connections
- **DatabaseMemoryRegistry**: Per-database in-memory storage
- **MemoryRegistryManager**: Singleton managing all registries
- **Database filtering** with configurable patterns

#### Connection Management
- **AsyncPG** for PostgreSQL connections
- **Connection pooling** with configurable min/max connections
- **Automatic cleanup** and graceful shutdown

### 4. Environment System

#### Odoo-Style Environment
- **Mandatory cr/uid properties** plus context
- **Database cursor** and **user ID** management
- **Context dictionary** for request-specific data
- **UUID-based request tracking** for enhanced logging

#### Context Management
- **AsyncLocalStorage-like** context for async operations
- **Request-scoped** environment instances
- **Middleware integration** for automatic context setup

### 5. Model System

#### ORM Features
- **Odoo-style field definitions** (Char, Integer, Boolean, etc.)
- **RecordSet implementation** with mapped() method
- **Model inheritance** support
- **Automatic table generation** with dependency resolution

#### Core Models
- **ir.module.module**: Addon metadata and state
- **ir.model**: Model definitions and metadata
- **ir.model.fields**: Field definitions and properties

### 6. HTTP Routing System

#### Route Management
- **@http.route()** decorator with Odoo-style syntax
- **Database-specific routes** with automatic registration
- **JSON RPC 2.0** support for API endpoints
- **Controller-based** organization

#### Template System
- **QWeb-like** XML templates with t-directives
- **Template compilation** with data injection
- **Multi-directory** template discovery

### 7. Logging System

#### Enhanced Logging
- **Colored console output** with professional formatting
- **Configurable log levels** through erp.conf
- **SQL logging** at debug level
- **Request tracking** with UUID correlation
- **Performance timing** for all operations

## Key Design Principles

### 1. Async-First
- All operations are async by default
- No backward compatibility with synchronous code
- AsyncLocalStorage-like context management

### 2. Addon-Centric
- Core functionality implemented as addons
- Manual addon management (no automatic loading)
- Comprehensive lifecycle hooks

### 3. Database-Aware
- Multi-database support with filtering
- Per-database memory registries
- Database-specific route registration

### 4. Developer-Friendly
- Enhanced logging with colors and timing
- Comprehensive error handling
- Type hints throughout the codebase

### 5. Production-Ready
- Connection pooling and resource management
- Graceful shutdown procedures
- Performance monitoring and metrics

## Configuration

### Main Configuration (erp.conf)
```ini
[options]
# Server configuration
http_interface = 127.0.0.1
http_port = 8069

# Database configuration
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp

# Multi-database support
list_db = True
db_filter = ^(erp_.*|test_.*)$

# Addon configuration
addons_path = addons

# Logging configuration
log_level = DEBUG
log_file = logs/erp.log
```

### Docker Integration
- **PostgreSQL 16 Alpine** container
- **pgAdmin4** for database management
- **Host-based** ERP application development
- **Volume mounting** for persistent data

## Next Steps

This architecture provides a solid foundation for:
- **Business module development** (sales, inventory, accounting)
- **Custom addon creation** with full lifecycle support
- **Multi-tenant deployments** with database isolation
- **API integrations** with JSON RPC support
- **Web interface development** with template system
