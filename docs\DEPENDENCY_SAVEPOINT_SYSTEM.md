# Dependency-Aware Savepoint System

## Overview

The enhanced dependency-aware savepoint system ensures robust addon installation with proper dependency management. Each addon gets its own savepoint, and dependencies are preserved even if main addons fail.

## Key Principles

### 1. Individual Savepoints Per Addon
Each addon installation gets its own database savepoint, providing fine-grained transaction control.

### 2. Dependency Preservation
Dependencies are committed immediately upon successful installation, ensuring they remain available even if dependent addons fail.

### 3. Rollback Isolation
Main addon failures only affect the main addon, not its successfully installed dependencies.

### 4. Installation Tracking
The system tracks whether addons were installed explicitly or as dependencies, enabling better management and uninstallation decisions.

## Architecture Components

### DependencyManager
Enhanced with recursive dependency resolution and topological sorting:

```python
# Get enhanced installation plan
plan = dependency_manager.get_enhanced_install_order(['addon_name'])

# Plan includes:
# - installation_order: Complete sorted installation order
# - explicit_addons: Explicitly requested addons
# - dependency_addons: Addons installed as dependencies
# - dependency_graph: Full dependency relationships
```

### SavepointManager
Manages individual savepoints for each addon:

```python
# Execute installation with savepoint
success = await savepoint_manager.execute_with_savepoint(
    addon_name, env, installation_func, is_dependency=True
)
```

### InstallationPlan
Provides detailed installation metadata:

```python
@dataclass
class InstallationPlan:
    installation_order: List[str]      # Complete installation order
    explicit_addons: Set[str]          # Explicitly requested addons
    dependency_addons: Set[str]        # Dependency addons
    dependency_graph: Dict[str, Set[str]]  # Full dependency graph
```

## Installation Process

### Phase 1: Dependency Resolution
1. **Recursive Resolution**: Resolve all dependencies recursively
2. **Topological Sorting**: Sort addons respecting dependency order
3. **Validation**: Check for circular dependencies and missing addons
4. **Planning**: Create detailed installation plan

### Phase 2: Dependency Installation
For each dependency addon:
1. Create individual savepoint
2. Execute installation hooks
3. **Commit savepoint immediately** (preserves dependency)
4. Update addon state with `installation_reason="dependency"`

### Phase 3: Main Addon Installation
For each explicitly requested addon:
1. Create individual savepoint
2. Execute installation hooks
3. Release savepoint (committed with main transaction)
4. Update addon state with `installation_reason="explicit"`

## Example Scenarios

### Scenario 1: Successful Installation
```
Request: Install addon A
Dependencies: A → B, C; B → D

Installation Order: D → B → C → A

1. Install D: ✅ Committed immediately
2. Install B: ✅ Committed immediately  
3. Install C: ✅ Committed immediately
4. Install A: ✅ Released with transaction

Result: All addons installed successfully
```

### Scenario 2: Main Addon Failure
```
Request: Install addon A
Dependencies: A → B, C; B → D

Installation Order: D → B → C → A

1. Install D: ✅ Committed immediately
2. Install B: ✅ Committed immediately
3. Install C: ✅ Committed immediately
4. Install A: ❌ Failed - Rollback only A

Result: D, B, C remain installed; A is marked as broken
```

### Scenario 3: Dependency Failure
```
Request: Install addon A
Dependencies: A → B, C; B → D

Installation Order: D → B → C → A

1. Install D: ✅ Committed immediately
2. Install B: ❌ Failed - Rollback only B
3. Installation stops

Result: D remains installed; B is marked as broken; C and A not attempted
```

## State Management

### AddonInfo Enhancement
```python
@dataclass
class AddonInfo:
    # ... existing fields ...
    installed_as_dependency: bool = False
    installation_reason: str = "explicit"  # "explicit" or "dependency"
```

### State Tracking Methods
```python
# Get addons by installation reason
dependency_addons = state_manager.get_dependency_addons()
explicit_addons = state_manager.get_explicit_addons()

# Check if addon can be safely uninstalled
can_uninstall, dependents = state_manager.can_uninstall_safely('addon_name')
```

## Advanced Features

### Dependency Levels
Organize addons by dependency depth for analysis:

```python
levels = dependency_manager.get_dependency_levels(['addon_name'])
# Level 0: Addons with no dependencies
# Level 1: Addons depending only on level 0
# Level 2: Addons depending on level 0 or 1
# etc.
```

### Installation Groups
Group addons that can be installed in parallel:

```python
groups = installation_plan.get_installation_groups()
# Each group contains addons with no dependencies between them
```

### Dependency Tree
Get complete dependency tree for analysis:

```python
tree = dependency_manager.get_dependency_tree('addon_name')
# Returns nested dictionary representing full dependency hierarchy
```

## Error Handling

### Circular Dependencies
```python
try:
    plan = dependency_manager.get_enhanced_install_order(['addon_name'])
except CircularDependencyError as e:
    print(f"Circular dependency detected: {e}")
```

### Missing Dependencies
```python
try:
    plan = dependency_manager.get_enhanced_install_order(['addon_name'])
except MissingDependencyError as e:
    print(f"Missing dependencies: {e}")
```

### Installation Validation
```python
is_valid, violations = dependency_manager.validate_installation_order(order)
if not is_valid:
    print(f"Invalid installation order: {violations}")
```

## Best Practices

### 1. Always Use Enhanced Installation
Use the new `install_addon` method which automatically handles dependencies:

```python
success = await addon_manager.install_addon('addon_name', env=env)
```

### 2. Check Installation Summary
After installation, review the results:

```python
summary = savepoint_manager.get_installation_summary()
print(f"Committed: {summary['committed']}")
print(f"Rolled back: {summary['rolled_back']}")
```

### 3. Validate Before Installation
Check dependencies before attempting installation:

```python
plan = dependency_manager.get_enhanced_install_order(['addon_name'])
is_valid, violations = dependency_manager.validate_installation_order(plan.installation_order)
```

### 4. Handle Partial Failures Gracefully
When installation fails, dependencies remain available:

```python
if not success:
    # Dependencies are still installed and can be used by other addons
    # Only the main addon needs to be fixed and retried
    pass
```



## Performance Considerations

### Savepoint Overhead
Each addon gets its own savepoint, which adds minimal database overhead but provides significant reliability benefits.

### Dependency Resolution
Enhanced resolution is performed once at the beginning, with results cached in the InstallationPlan.

### Cleanup
Completed savepoints are automatically cleaned up to prevent memory leaks:

```python
savepoint_manager.cleanup_completed_savepoints()
```

## Troubleshooting

### Common Issues

1. **Circular Dependencies**: Use dependency tree analysis to identify cycles
2. **Missing Dependencies**: Ensure all required addons are available
3. **Partial Failures**: Check installation summary to see what succeeded
4. **State Inconsistencies**: Use validation methods to verify installation order

### Debugging Tools

```python
# Analyze dependency tree
tree = dependency_manager.get_dependency_tree('addon_name')

# Check installation order validity
is_valid, violations = dependency_manager.validate_installation_order(order)

# Get installation summary
summary = savepoint_manager.get_installation_summary()
```
