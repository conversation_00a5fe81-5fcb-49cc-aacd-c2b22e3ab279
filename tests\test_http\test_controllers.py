"""
Test suite for HTTP controllers and authentication

This module tests:
- Controller base class functionality
- ControllerRegistry operations
- Authentication system
- Response handling and template rendering
"""
import pytest
from unittest.mock import MagicMock, patch

from fastapi.responses import JSONResponse, HTMLResponse
from erp.http import (
    Controller, ControllerRegistry, AuthType, require_auth
)


class TestController:
    """Test Controller base class functionality"""

    def test_controller_initialization(self):
        """Test Controller initialization"""
        controller = Controller()

        assert hasattr(controller, 'template_manager')
        assert controller._current_request is None
        assert controller._current_env is None

    def test_controller_json_response(self):
        """Test JSON response creation"""
        controller = Controller()

        data = {"message": "test", "status": "success"}
        response = controller.json_response(data)

        assert isinstance(response, JSONResponse)

    def test_controller_error_response(self):
        """Test error response creation"""
        controller = Controller()

        response = controller.error_response("Test error", 400)

        assert isinstance(response, JSONResponse)
        assert response.status_code == 400

    def test_controller_redirect(self):
        """Test redirect response creation"""
        controller = Controller()

        response = controller.redirect("/new_path")

        assert response.status_code == 302
        assert response.headers["location"] == "/new_path"

    @pytest.mark.asyncio
    async def test_controller_render_template(self):
        """Test template rendering"""
        controller = Controller()

        with patch.object(controller.template_manager, 'render_template_async') as mock_render:
            mock_render.return_value = "<html>Test</html>"

            response = await controller.render_template("test_template", {"var": "value"})

            assert isinstance(response, HTMLResponse)
            mock_render.assert_called_once()


class TestAuthSystem:
    """Test authentication system"""

    @pytest.mark.asyncio
    async def test_require_auth_none(self):
        """Test no authentication required"""
        mock_request = MagicMock()

        result = await require_auth(AuthType.NONE, mock_request)

        assert result is None  # No authentication error

    @pytest.mark.asyncio
    async def test_require_auth_user_no_env(self):
        """Test user authentication without environment"""
        mock_request = MagicMock()

        with patch('erp.http.auth.ContextManager.get_environment', return_value=None):
            result = await require_auth(AuthType.USER, mock_request)

            assert isinstance(result, JSONResponse)
            assert result.status_code == 401

    @pytest.mark.asyncio
    async def test_require_auth_user_valid_env(self):
        """Test user authentication with valid environment"""
        mock_request = MagicMock()
        mock_env = MagicMock()
        mock_env.uid = 2  # Valid user ID

        with patch('erp.http.auth.ContextManager.get_environment', return_value=mock_env):
            result = await require_auth(AuthType.USER, mock_request)

            assert result is None  # No authentication error

    @pytest.mark.asyncio
    async def test_require_auth_admin_not_admin(self):
        """Test admin authentication for non-admin user"""
        mock_request = MagicMock()
        mock_env = MagicMock()
        mock_env.uid = 2  # Non-admin user ID

        with patch('erp.http.auth.ContextManager.get_environment', return_value=mock_env):
            result = await require_auth(AuthType.ADMIN, mock_request)

            assert isinstance(result, JSONResponse)
            assert result.status_code == 403

    @pytest.mark.asyncio
    async def test_require_auth_admin_valid(self):
        """Test admin authentication for admin user"""
        mock_request = MagicMock()
        mock_env = MagicMock()
        mock_env.uid = 1  # Admin user ID

        with patch('erp.http.auth.ContextManager.get_environment', return_value=mock_env):
            result = await require_auth(AuthType.ADMIN, mock_request)

            assert result is None  # No authentication error


class TestControllerRegistry:
    """Test ControllerRegistry functionality"""

    def test_controller_registry_initialization(self):
        """Test ControllerRegistry initialization"""
        registry = ControllerRegistry()

        assert hasattr(registry, '_controllers')
        assert isinstance(registry._controllers, dict)

    def test_controller_registry_register(self):
        """Test controller registration"""
        registry = ControllerRegistry()

        class TestController(Controller):
            pass

        controller = TestController()
        registry.register('test', controller)

        assert 'test' in registry._controllers
        assert registry._controllers['test'] == controller

    def test_controller_registry_get(self):
        """Test getting controller from registry"""
        registry = ControllerRegistry()

        class TestController(Controller):
            pass

        controller = TestController()
        registry.register('test', controller)

        retrieved = registry.get('test')
        assert retrieved == controller

        # Test non-existent controller
        assert registry.get('nonexistent') is None
