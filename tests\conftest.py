"""
Pytest configuration and fixtures for ERP system tests
"""
import asyncio
import sys
import pytest
from pathlib import Path
from typing import Async<PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import ERP modules
from erp.database.connection import DatabaseManager
from erp.database.registry import DatabaseRegistry
from erp.database.memory import AppRegistry, MemoryRegistryManager
from erp.addons import AddonManager
from erp.http.registries import SystemRouteRegistry
from erp.logging import get_logger

logger = get_logger(__name__)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def test_database() -> AsyncGenerator[str, None]:
    """Provide a test database name."""
    test_db_name = "test_erp_db"
    yield test_db_name
    
    # Cleanup: Remove test database from registries
    try:
        await MemoryRegistryManager.remove_registry(test_db_name)
        if hasattr(DatabaseRegistry, '_databases'):
            DatabaseRegistry._databases.pop(test_db_name, None)
    except Exception as e:
        logger.warning(f"Cleanup warning for test database {test_db_name}: {e}")


@pytest.fixture
async def memory_registry(test_database: str) -> AsyncGenerator[AppRegistry, None]:
    """Provide a clean memory registry for testing."""
    registry = AppRegistry(test_database)
    yield registry
    
    # Cleanup
    try:
        await MemoryRegistryManager.remove_registry(test_database)
    except Exception as e:
        logger.warning(f"Cleanup warning for memory registry: {e}")


@pytest.fixture
def addon_manager() -> Generator[AddonManager, None, None]:
    """Provide a clean addon manager for testing."""
    manager = AddonManager()
    # Clear any existing state
    manager._addon_states.clear()
    yield manager
    # Cleanup
    manager._addon_states.clear()


@pytest.fixture
def route_registry() -> Generator[SystemRouteRegistry, None, None]:
    """Provide a clean route registry for testing."""
    registry = SystemRouteRegistry()
    yield registry
    # Cleanup
    registry.clear()


@pytest.fixture
def mock_database_manager():
    """Provide a mock database manager."""
    mock_manager = AsyncMock(spec=DatabaseManager)
    mock_manager.create_pool.return_value = None
    mock_manager.close_pool.return_value = None
    mock_manager.execute.return_value = "OK"
    mock_manager.fetch.return_value = []
    mock_manager.fetchrow.return_value = None
    mock_manager.fetchval.return_value = None
    mock_manager.insert.return_value = "test_id"
    mock_manager.update.return_value = True
    mock_manager.delete.return_value = True
    return mock_manager


@pytest.fixture
def mock_request():
    """Provide a mock FastAPI request object."""
    from fastapi import Request
    
    mock_request = MagicMock(spec=Request)
    mock_request.url = MagicMock()
    mock_request.url.path = "/test"
    mock_request.method = "GET"
    mock_request.headers = {}
    mock_request.query_params = {}
    mock_request.path_params = {}
    mock_request.state = MagicMock()
    return mock_request


@pytest.fixture(autouse=True, scope="function")
def cleanup_registries():
    """Automatically cleanup registries after each test."""
    yield

    # Cleanup memory registries (sync version)
    try:
        # For now, we'll skip the async cleanup in autouse fixture
        # Individual tests can use the async fixtures for cleanup
        pass
    except Exception as e:
        logger.warning(f"Registry cleanup warning: {e}")


@pytest.fixture
def temp_addon_path(tmp_path):
    """Provide a temporary addon path for testing."""
    addon_path = tmp_path / "test_addon"
    addon_path.mkdir()
    
    # Create basic addon structure
    (addon_path / "__init__.py").write_text("")
    (addon_path / "__manifest__.py").write_text("""
{
    'name': 'Test Addon',
    'version': '1.0.0',
    'description': 'Test addon for unit tests',
    'author': 'Test',
    'depends': [],
    'installable': True,
    'auto_install': False,
}
""")
    
    return str(addon_path)


# Test environment configuration
@pytest.fixture(autouse=True)
def configure_test_environment():
    """Configure test environment settings."""
    # For tests, we don't need to modify config
    # Just ensure we have a clean environment
    yield


# Async test helpers
class AsyncTestCase:
    """Base class for async test cases."""
    
    def setup_method(self):
        """Setup method called before each test method."""
        pass
    
    def teardown_method(self):
        """Teardown method called after each test method."""
        pass


# Test data factories
@pytest.fixture
def sample_model_data():
    """Provide sample model data for testing."""
    return {
        'name': 'Test Model',
        'description': 'A test model for unit tests',
        'active': True,
        'sequence': 10
    }


@pytest.fixture
def sample_field_data():
    """Provide sample field data for testing."""
    return {
        'name': 'test_field',
        'field_description': 'Test Field',
        'ttype': 'char',
        'size': 100,
        'required': False,
        'readonly': False
    }
