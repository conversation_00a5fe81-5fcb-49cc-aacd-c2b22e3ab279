# ERP Logging System - Organized Architecture

The ERP logging system has been reorganized into a clean, modular architecture for better maintainability and clarity.

## Directory Structure

```
erp/logging/
├── __init__.py                 # Main entry point
├── README.md                   # This documentation
├── core/                       # Core system components
│   ├── __init__.py
│   ├── core.py                 # Core logging system
│   ├── facade.py               # Main facade interface (recommended)
│   └── factories.py            # Component factories
├── config/                     # Configuration system
│   ├── __init__.py
│   └── config.py               # Configuration classes
├── utils/                      # Utility functions and decorators
│   ├── __init__.py
│   ├── utils.py                # Utility functions
│   └── decorators.py           # Logging decorators
├── adapters/                   # Adapter classes for different backends
│   ├── __init__.py
│   └── adapters.py             # Backend adapters
├── middleware/                 # Middleware system for custom processing
│   ├── __init__.py
│   └── middleware.py           # Middleware pipeline
├── monitoring/                 # Performance monitoring and metrics
│   └── __init__.py             # Monitoring components
├── coordination/               # Operation coordination and rate limiting
│   └── __init__.py             # Coordination components

```

## Component Overview

### Core Components (`core/`)
- **facade.py**: Main interface for logging operations (recommended for most use cases)
- **core.py**: Core logging system and component registry
- **factories.py**: Factory classes for creating logging components

### Configuration (`config/`)
- **config.py**: Configuration classes and validation

### Utilities (`utils/`)
- **utils.py**: Utility functions for structured logging and performance tracking
- **decorators.py**: Decorators for automatic logging of methods, database operations, etc.

### Adapters (`adapters/`)
- **adapters.py**: Adapter pattern implementations for different logging backends

### Middleware (`middleware/`)
- **middleware.py**: Middleware system for custom log processing pipelines

### Monitoring (`monitoring/`)
- Performance monitoring and metrics collection
- System and application metrics
- Alert management

### Coordination (`coordination/`)
- Operation tracking and coordination
- Logger suppression and rate limiting
- Duplicate prevention



## Usage Examples

### Recommended Usage (Facade Interface)

```python
from erp.logging import get_logger, initialize_logging

# Initialize logging system
initialize_logging()

# Get a logger
logger = get_logger(__name__)

# Basic logging
logger.info("Application started")
logger.error("An error occurred")
```

### Advanced Usage

```python
from erp.logging import (
    LoggingFacade,
    start_performance_monitoring,
    operation_context,
    log_structured
)

# Get facade instance
facade = LoggingFacade()

# Start performance monitoring
start_performance_monitoring()

# Use operation context
with operation_context("user_login", user_id="123"):
    logger.info("User login attempt")

# Structured logging
log_structured(logger, "info", "User action", 
               user_id="123", action="login", success=True)
```

## Usage Guide

### Standard Usage

Use the facade interface for all logging operations:

```python
from erp.logging import get_logger, initialize_logging

# Initialize logging system
initialize_logging()

# Get a logger
logger = get_logger(__name__)

# Use the logger
logger.info("Application started")
```

## Benefits of Modern Architecture

1. **Clear Separation of Concerns**: Each directory has a specific purpose
2. **Better Maintainability**: Related components are grouped together
3. **Easier Navigation**: Logical structure makes finding components easier
4. **Modern Design**: Uses adapter pattern and modern Python practices
5. **Future-Proof**: Easier to add new features and components
6. **Performance**: Optimized for high-performance logging scenarios

## Best Practices

1. **Use the Facade**: Prefer `from erp.logging import get_logger` over direct component imports
2. **Structured Logging**: Use `log_structured()` for consistent log formatting
3. **Performance Monitoring**: Enable monitoring for production systems
4. **Operation Context**: Use operation contexts for tracking related log entries
5. **Adapter Pattern**: Use adapters for custom backends and integrations

## Future Plans

- Add more specialized adapters
- Enhance monitoring capabilities
- Improve configuration system
- Add more middleware components
- Expand integration capabilities
