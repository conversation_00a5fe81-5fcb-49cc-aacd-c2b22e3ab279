"""
Reference field implementation
"""
from .base import RelationalField, FieldValidationError


class Reference(RelationalField):
    """Reference field - can reference any model"""

    def __init__(self, selection=None, **kwargs):
        """
        Initialize Reference field

        Args:
            selection: List of (model_name, label) tuples for allowed models
        """
        # Reference fields don't have a specific comodel
        super().__init__('reference', **kwargs)
        self.selection = selection or []

    def get_sql_type(self):
        """Reference fields store model,id as text"""
        return "VARCHAR(255)"

    def _validate_value(self, value):
        """Validate Reference field value"""
        if value is None:
            return None

        # Value should be in format "model_name,id"
        if isinstance(value, str):
            if ',' in value:
                model_name, _ = value.split(',', 1)  # _ for unused record_id
                # Validate model is in selection if provided
                if self.selection:
                    valid_models = [item[0] for item in self.selection]
                    if model_name not in valid_models:
                        raise FieldValidationError(f"Invalid model '{model_name}' for Reference field")
                return value
            else:
                raise FieldValidationError("Reference value must be in format 'model,id'")

        elif hasattr(value, '_name') and hasattr(value, 'id'):
            # Model instance
            model_name = value._name
            if self.selection:
                valid_models = [item[0] for item in self.selection]
                if model_name not in valid_models:
                    raise FieldValidationError(f"Invalid model '{model_name}' for Reference field")
            return f"{model_name},{value.id}"

        else:
            raise FieldValidationError(f"Invalid value type for Reference field: {type(value)}")
