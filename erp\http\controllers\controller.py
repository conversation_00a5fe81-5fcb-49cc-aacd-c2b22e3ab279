"""
Main controller class
Combines base controller with common mixins for full functionality
"""

from .base import BaseController
from .mixins import (
    RequestMixin, 
    ResponseMixin, 
    TemplateMixin, 
    DatabaseMixin, 
    AuthMixin,
    ValidationMixin
)


class Controller(
    BaseController,
    RequestMixin,
    ResponseMixin, 
    TemplateMixin,
    DatabaseMixin,
    AuthMixin,
    ValidationMixin
):
    """
    Full-featured controller class
    
    Combines BaseController with all common mixins to provide
    a complete controller implementation similar to the original
    monolithic Controller class, but with better separation of concerns.
    """
    
    def __init__(self):
        super().__init__()
        self.logger.debug(f"Initialized {self.__class__.__name__}")
    
    async def should_redirect_to_db_list(self) -> bool:
        """
        Determine if request should be redirected to database list
        
        Returns:
            True if should redirect to database list
        """
        try:
            from ...config import config
            
            # If list_db is disabled, never redirect
            if not config.list_db:
                return False
            
            # Get database from request
            db_name = self.get_database_from_request()
            
            # If no database specified, redirect to list
            if not db_name:
                return True
            
            # If database filter is set, check if it matches exactly one database
            if config.db_filter:
                from ...database.memory import DatabaseFilterProcessor
                databases = await self.get_database_list()
                db_names = [db['name'] for db in databases]
                accessible_dbs = DatabaseFilterProcessor.filter_databases(db_names, config.db_filter)
                
                # If filter doesn't match exactly one database, redirect to list
                if len(accessible_dbs) != 1:
                    return True
                
                # If specified database is not the filtered one, redirect
                if accessible_dbs[0] != db_name:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking database redirect: {e}")
            return False


class MinimalController(BaseController, RequestMixin, ResponseMixin):
    """
    Minimal controller with only basic functionality
    
    Use this for lightweight controllers that don't need
    template rendering, database access, or authentication.
    """
    pass


class APIController(BaseController, RequestMixin, ResponseMixin, AuthMixin, ValidationMixin):
    """
    API-focused controller
    
    Includes authentication and validation but excludes
    template rendering and database-specific functionality.
    """
    pass


class TemplateController(BaseController, RequestMixin, ResponseMixin, TemplateMixin):
    """
    Template-focused controller
    
    Includes template rendering but excludes database
    and authentication functionality.
    """
    pass


class DatabaseController(BaseController, RequestMixin, ResponseMixin, DatabaseMixin, AuthMixin):
    """
    Database-focused controller
    
    Includes database and authentication functionality
    but excludes template rendering.
    """
    pass
