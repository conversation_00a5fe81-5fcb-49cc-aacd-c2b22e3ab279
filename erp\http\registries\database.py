"""
Database route registry implementation
Handles database-specific routes with lazy loading and lifecycle management
"""

import asyncio
from typing import Dict, List, Optional, Set
from collections import defaultdict

from ..interfaces import IRouteRegistry, RouteInfo, RouteScope, RouteRegistrationError
from ...logging import get_logger

logger = get_logger(__name__)


class DatabaseRouteRegistry(IRouteRegistry):
    """Registry for database-specific routes"""
    
    def __init__(self, database_name: str):
        self.database_name = database_name
        self._routes: Dict[str, List[RouteInfo]] = defaultdict(list)
        self._route_lookup: Dict[str, RouteInfo] = {}  # path:method -> RouteInfo
        self._lock = asyncio.Lock()
        self._sources: Set[str] = set()
        self._initialized = False
    
    async def register_route(self, route_info: RouteInfo) -> bool:
        """Register a database route"""
        if route_info.scope not in (RouteScope.DATABASE, RouteScope.ADDON):
            raise RouteRegistrationError(f"Invalid scope for database registry: {route_info.scope}")
        
        async with self._lock:
            try:
                # Check for conflicts
                for method in route_info.methods:
                    lookup_key = f"{route_info.path}:{method.upper()}"
                    if lookup_key in self._route_lookup:
                        existing = self._route_lookup[lookup_key]
                        logger.warning(
                            f"Route conflict in {self.database_name}: {method} {route_info.path} "
                            f"(existing: {existing.source}, new: {route_info.source})"
                        )
                
                # Register the route
                self._routes[route_info.path].append(route_info)
                
                # Update lookup table
                for method in route_info.methods:
                    lookup_key = f"{route_info.path}:{method.upper()}"
                    self._route_lookup[lookup_key] = route_info
                
                # Track source
                if route_info.source:
                    self._sources.add(route_info.source)
                
                logger.debug(
                    f"Registered database route in {self.database_name}: "
                    f"{route_info.methods} {route_info.path} from {route_info.source or 'unknown'}"
                )
                return True
                
            except Exception as e:
                logger.error(f"Failed to register database route {route_info.path} in {self.database_name}: {e}")
                raise RouteRegistrationError(f"Registration failed: {e}")
    
    async def unregister_route(self, path: str, methods: Optional[List[str]] = None) -> bool:
        """Unregister a database route"""
        async with self._lock:
            try:
                if path not in self._routes:
                    return False
                
                if methods is None:
                    # Remove all methods for this path
                    route_infos = self._routes[path]
                    for route_info in route_infos:
                        for method in route_info.methods:
                            lookup_key = f"{path}:{method.upper()}"
                            self._route_lookup.pop(lookup_key, None)
                    
                    del self._routes[path]
                    logger.debug(f"Unregistered all methods for database route in {self.database_name}: {path}")
                else:
                    # Remove specific methods
                    remaining_routes = []
                    for route_info in self._routes[path]:
                        remaining_methods = [m for m in route_info.methods if m.upper() not in [m.upper() for m in methods]]
                        if remaining_methods:
                            # Create new route info with remaining methods
                            new_route_info = RouteInfo(
                                path=route_info.path,
                                handler=route_info.handler,
                                methods=remaining_methods,
                                route_type=route_info.route_type,
                                auth=route_info.auth,
                                scope=route_info.scope,
                                metadata=route_info.metadata,
                                source=route_info.source
                            )
                            remaining_routes.append(new_route_info)
                        
                        # Remove from lookup
                        for method in methods:
                            lookup_key = f"{path}:{method.upper()}"
                            self._route_lookup.pop(lookup_key, None)
                    
                    if remaining_routes:
                        self._routes[path] = remaining_routes
                    else:
                        del self._routes[path]
                    
                    logger.debug(f"Unregistered methods {methods} for database route in {self.database_name}: {path}")
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to unregister database route {path} in {self.database_name}: {e}")
                return False
    
    async def get_routes(self) -> Dict[str, List[RouteInfo]]:
        """Get all registered database routes"""
        async with self._lock:
            return {path: routes.copy() for path, routes in self._routes.items()}
    
    async def get_route(self, path: str, method: str) -> Optional[RouteInfo]:
        """Get specific route by path and method"""
        lookup_key = f"{path}:{method.upper()}"
        return self._route_lookup.get(lookup_key)
    
    async def clear(self) -> None:
        """Clear all database routes"""
        async with self._lock:
            self._routes.clear()
            self._route_lookup.clear()
            self._sources.clear()
            self._initialized = False
            logger.debug(f"Cleared all database routes for {self.database_name}")
    
    async def get_routes_by_source(self, source: str) -> Dict[str, List[RouteInfo]]:
        """Get routes from a specific source"""
        async with self._lock:
            result = defaultdict(list)
            for path, route_list in self._routes.items():
                for route_info in route_list:
                    if route_info.source == source:
                        result[path].append(route_info)
            return dict(result)
    
    async def unregister_routes_by_source(self, source: str) -> int:
        """Unregister all routes from a specific source"""
        async with self._lock:
            removed_count = 0
            paths_to_remove = []
            
            for path, route_list in self._routes.items():
                remaining_routes = []
                for route_info in route_list:
                    if route_info.source == source:
                        # Remove from lookup
                        for method in route_info.methods:
                            lookup_key = f"{path}:{method.upper()}"
                            self._route_lookup.pop(lookup_key, None)
                        removed_count += 1
                    else:
                        remaining_routes.append(route_info)
                
                if remaining_routes:
                    self._routes[path] = remaining_routes
                else:
                    paths_to_remove.append(path)
            
            # Remove empty paths
            for path in paths_to_remove:
                del self._routes[path]
            
            # Remove source from tracking
            self._sources.discard(source)
            
            logger.debug(f"Unregistered {removed_count} routes from source {source} in {self.database_name}")
            return removed_count
    
    def get_sources(self) -> Set[str]:
        """Get all tracked sources"""
        return self._sources.copy()
    
    def get_stats(self) -> Dict[str, int]:
        """Get registry statistics"""
        total_routes = sum(len(routes) for routes in self._routes.values())
        return {
            'database': self.database_name,
            'total_paths': len(self._routes),
            'total_routes': total_routes,
            'total_sources': len(self._sources),
            'initialized': self._initialized
        }
    
    async def initialize(self) -> None:
        """Initialize the registry (called when database is ready)"""
        async with self._lock:
            self._initialized = True
            logger.debug(f"Initialized database route registry for {self.database_name}")
    
    def is_initialized(self) -> bool:
        """Check if registry is initialized"""
        return self._initialized


class DatabaseRouteManager:
    """Manager for database route registries with lazy loading"""
    
    def __init__(self):
        self._registries: Dict[str, DatabaseRouteRegistry] = {}
        self._lock = asyncio.Lock()
    
    async def get_registry(self, database_name: str) -> DatabaseRouteRegistry:
        """Get or create database route registry"""
        async with self._lock:
            if database_name not in self._registries:
                self._registries[database_name] = DatabaseRouteRegistry(database_name)
                logger.debug(f"Created database route registry for {database_name}")
            return self._registries[database_name]
    
    async def remove_registry(self, database_name: str) -> bool:
        """Remove database route registry"""
        async with self._lock:
            if database_name in self._registries:
                await self._registries[database_name].clear()
                del self._registries[database_name]
                logger.debug(f"Removed database route registry for {database_name}")
                return True
            return False
    
    async def get_all_registries(self) -> Dict[str, DatabaseRouteRegistry]:
        """Get all database registries"""
        async with self._lock:
            return self._registries.copy()
    
    async def clear_all(self) -> None:
        """Clear all database registries"""
        async with self._lock:
            for registry in self._registries.values():
                await registry.clear()
            self._registries.clear()
            logger.debug("Cleared all database route registries")


# Global database route manager
_database_manager = DatabaseRouteManager()


def get_database_route_manager() -> DatabaseRouteManager:
    """Get the global database route manager"""
    return _database_manager
