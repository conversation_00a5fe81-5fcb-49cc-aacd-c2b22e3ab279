"""
Shared Registry Management Utilities
Provides reusable registry update and validation functionality for module installers.
"""
from typing import List, Optional, TYPE_CHECKING
from ...logging import get_logger

if TYPE_CHECKING:
    from ...environment import Environment


class RegistryUpdater:
    """
    Shared registry updater utility that wraps MemoryRegistryManager functionality.
    Can be used by any module installer for consistent registry management.
    """

    def __init__(self):
        self.logger = get_logger(__name__)

    async def update_registry_after_module_action(self, env: 'Environment', module_name: str, action: str) -> bool:
        """
        Update the in-memory registry after a module action.
        Now uses the refresh coordinator to queue refreshes instead of immediate execution.

        Args:
            env: Environment for database operations
            module_name: Name of the module that was acted upon
            action: Action performed (install/uninstall/upgrade)

        Returns:
            True if update queued successfully, False otherwise
        """
        try:
            from ...addons.managers.lifecycle_tracker import get_lifecycle_tracker
            from ...addons.managers.registry_refresh_coordinator import get_refresh_coordinator

            db_name = env.cr.db_name
            self.logger.debug(f"Queuing registry refresh for database '{db_name}' after {action} of module '{module_name}'")

            # Get current session ID if available
            lifecycle_tracker = get_lifecycle_tracker()
            session_id = await lifecycle_tracker.get_session_for_database(db_name)

            # Queue the registry refresh instead of executing immediately
            refresh_coordinator = get_refresh_coordinator()
            await refresh_coordinator.queue_registry_refresh(db_name, module_name, action, session_id)

            self.logger.debug(f"✅ Registry refresh queued for database '{db_name}' after {action} of module '{module_name}'")
            if session_id:
                self.logger.debug(f"Refresh queued as part of session {session_id}")
            else:
                self.logger.debug("No active session - refresh will be executed immediately")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to queue registry refresh for database '{env.cr.db_name}' after {action} of module '{module_name}': {e}")
            return False

    async def validate_module_in_registry(self, env: 'Environment', module_name: str, 
                                        expected_models: Optional[List[str]] = None) -> bool:
        """
        Validate that a module is properly loaded in the registry.
        
        Args:
            env: Environment for database operations
            module_name: Name of the module to validate
            expected_models: Optional list of model names that should be available
            
        Returns:
            True if module is properly loaded, False otherwise
        """
        try:
            from ...database.memory.registry_manager import MemoryRegistryManager

            db_name = env.cr.db_name
            
            # Get the registry for this database
            registry = await MemoryRegistryManager.get_registry(db_name)
            
            # Validate that module is loaded
            if module_name not in registry.installed_modules:
                self.logger.error(f"Module '{module_name}' not found in registry installed modules")
                return False

            # Validate expected models if provided
            if expected_models:
                for model_name in expected_models:
                    if not registry.model_metadata_manager.has_model(model_name):
                        self.logger.error(f"Expected model '{model_name}' not found in registry metadata for module '{module_name}'")
                        return False

            self.logger.debug(f"✓ Module '{module_name}' registry validation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to validate module '{module_name}' in registry: {e}")
            return False

    async def refresh_registry_if_needed(self, env: 'Environment', module_name: str,
                                       expected_models: Optional[List[str]] = None) -> bool:
        """
        Refresh registry if it's in an inconsistent state for the given module.
        
        Args:
            env: Environment for database operations
            module_name: Name of the module to validate
            expected_models: Optional list of model names that should be available
            
        Returns:
            True if registry is valid or successfully refreshed, False otherwise
        """
        try:
            from ...database.memory.registry_manager import MemoryRegistryManager

            db_name = env.cr.db_name
            
            # First validate current state
            if await self.validate_module_in_registry(env, module_name, expected_models):
                self.logger.debug(f"Registry state is valid for module '{module_name}', no refresh needed")
                return True

            # If validation failed, try to refresh
            self.logger.debug(f"Registry state validation failed for module '{module_name}', attempting refresh")
            success = await MemoryRegistryManager.refresh_registry(db_name)
            
            if not success:
                self.logger.error(f"Failed to refresh registry for module '{module_name}'")
                return False

            # Validate again after refresh
            if not await self.validate_module_in_registry(env, module_name, expected_models):
                self.logger.error(f"Registry state still invalid for module '{module_name}' after refresh")
                return False

            self.logger.debug(f"✓ Registry refreshed and validated successfully for module '{module_name}'")
            return True

        except Exception as e:
            self.logger.error(f"Failed to refresh registry for module '{module_name}': {e}")
            return False

    async def get_registry_status(self, env: 'Environment') -> dict:
        """
        Get current registry status for debugging purposes.
        
        Args:
            env: Environment for database operations
            
        Returns:
            Dictionary containing registry status information
        """
        try:
            from ...database.memory.registry_manager import MemoryRegistryManager

            db_name = env.cr.db_name
            registry = await MemoryRegistryManager.get_registry(db_name)
            
            return {
                'database': db_name,
                'installed_modules': list(registry.installed_modules),
                'total_models': len(registry.model_metadata_manager._models) if hasattr(registry.model_metadata_manager, '_models') else 0,
                'active_environments': registry.get_active_environments_count(),
                'created_at': getattr(registry, 'created_at', None)
            }

        except Exception as e:
            self.logger.error(f"Failed to get registry status: {e}")
            return {'error': str(e)}

    async def validate_registry_consistency(self, env: 'Environment') -> dict:
        """
        Validate overall registry consistency
        
        Args:
            env: Environment for database operations
            
        Returns:
            Dictionary containing consistency validation results
        """
        try:
            from ...database.memory.registry_manager import MemoryRegistryManager

            db_name = env.cr.db_name
            registry = await MemoryRegistryManager.get_registry(db_name)
            
            results = {
                'status': 'success',
                'database': db_name,
                'issues': [],
                'warnings': []
            }

            # Check if registry exists
            if not registry:
                results['status'] = 'error'
                results['issues'].append('Registry not found for database')
                return results

            # Check installed modules consistency
            try:
                installed_modules = await registry.get_installed_modules()
                registry_modules = registry.installed_modules
                
                if set(installed_modules) != set(registry_modules):
                    results['warnings'].append('Mismatch between database and registry installed modules')
            except Exception as e:
                results['issues'].append(f'Failed to check module consistency: {e}')

            # Check model metadata consistency
            try:
                if hasattr(registry, 'model_metadata_manager'):
                    model_count = len(registry.model_metadata_manager._models) if hasattr(registry.model_metadata_manager, '_models') else 0
                    if model_count == 0:
                        results['warnings'].append('No models found in registry metadata manager')
            except Exception as e:
                results['issues'].append(f'Failed to check model metadata: {e}')

            # Determine overall status
            if results['issues']:
                results['status'] = 'error'
            elif results['warnings']:
                results['status'] = 'warning'

            return results

        except Exception as e:
            return {
                'status': 'error',
                'database': env.cr.db_name if hasattr(env, 'cr') else 'unknown',
                'issues': [f'Failed to validate registry consistency: {e}'],
                'warnings': []
            }

    async def force_registry_rebuild(self, env: 'Environment') -> bool:
        """
        Force a complete registry rebuild
        
        Args:
            env: Environment for database operations
            
        Returns:
            True if rebuild was successful, False otherwise
        """
        try:
            from ...database.memory.registry_manager import MemoryRegistryManager

            db_name = env.cr.db_name
            self.logger.info(f"Forcing registry rebuild for database '{db_name}'")

            # Clear existing registry
            await MemoryRegistryManager.clear_registry(db_name)
            
            # Rebuild registry
            success = await MemoryRegistryManager.refresh_registry(db_name)
            
            if success:
                self.logger.info(f"✓ Registry rebuild completed successfully for database '{db_name}'")
            else:
                self.logger.error(f"✗ Registry rebuild failed for database '{db_name}'")

            return success

        except Exception as e:
            self.logger.error(f"Failed to force registry rebuild for database '{env.cr.db_name}': {e}")
            return False


# Singleton instance for reuse
_registry_updater = None

def get_registry_updater() -> RegistryUpdater:
    """Get shared RegistryUpdater instance"""
    global _registry_updater
    if _registry_updater is None:
        _registry_updater = RegistryUpdater()
    return _registry_updater
