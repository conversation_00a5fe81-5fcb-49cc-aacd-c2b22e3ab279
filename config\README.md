# Configuration Directory

This directory contains configuration examples and templates for the ERP system.

## Configuration Files

### Example Configurations
- **`erp.conf.example-logging`** - Comprehensive logging configuration
  - Shows all available logging options
  - Demonstrates different log levels and formats
  - Includes performance timing configuration
  - Example of colored console output setup

- **`erp.conf.example-multi-db`** - Multi-database mode configuration
  - Configuration for handling multiple databases
  - Database filtering and routing examples
  - Per-database registry configuration
  - Multi-tenant setup examples

## Usage

### Creating Your Configuration
1. Copy an example file to the project root:
   ```bash
   cp config/erp.conf.example-logging erp.conf
   ```

2. Edit the configuration file to match your environment:
   ```bash
   # Edit database connection settings
   db_host = your_database_host
   db_port = 5432
   db_user = your_username
   db_password = your_password
   ```

3. Start the server:
   ```bash
   ./erp-bin start
   ```

### Configuration Sections

#### Server Configuration
- HTTP port and interface settings
- Server type (ASGI) configuration
- Static file serving options

#### Database Configuration
- PostgreSQL connection settings
- Database filtering options
- Multi-database mode settings

#### Logging Configuration
- Log levels and formats
- Console and file logging
- Performance timing options
- Colored output settings

#### Addon Configuration
- Addon path settings
- Auto-discovery options
- Installation hooks configuration

## Security Notes

- **Never commit actual configuration files** with sensitive data
- Use environment variables for sensitive settings
- Keep example files generic and safe
- Use `.local.conf` suffix for local configurations (ignored by git)

## See Also
- `erp.conf` - Main configuration file (in project root)
- `docs/DEPLOYMENT.md` - Deployment configuration guide
- `docs/DEVELOPMENT_GUIDE.md` - Development setup guide
