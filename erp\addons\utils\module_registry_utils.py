"""
Module registry utilities for addon installation

This module provides shared utility functions for registering addons in the
ir.module.module table. These functions are extracted from module_registry.py
to eliminate code duplication and provide a centralized location for
module registration operations.
"""
import uuid
from datetime import datetime
from typing import Optional, Dict, Any

from ...logging import get_logger

logger = get_logger(__name__)


async def register_addon_in_module_table(
    db_manager,
    addon_name: str,
    display_name: str,
    summary: str,
    description: str,
    author: str = 'ERP System',
    version: str = '1.0.0',
    category: str = 'Uncategorized',
    installable: bool = True,
    auto_install: bool = False,
    **kwargs: Any
) -> bool:
    """
    Register an addon in the ir.module.module table
    
    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon
        display_name: Human readable name
        summary: Short description
        description: Detailed description
        author: Author name
        version: Version string
        category: Category name
        installable: Whether the addon is installable
        auto_install: Whether the addon should auto-install
        **kwargs: Additional fields to set
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        addon_id = str(uuid.uuid4())
        current_time = datetime.now()
        
        # Use UPSERT to register/update the addon in ir.module.module
        await db_manager.execute(
            """INSERT INTO ir_module_module
               (id, name, display_name, summary, description, author, version, category, state,
                installable, auto_install, create_at, update_at, action_at)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
               ON CONFLICT (name) DO UPDATE SET
                   state = EXCLUDED.state,
                   display_name = EXCLUDED.display_name,
                   summary = EXCLUDED.summary,
                   description = EXCLUDED.description,
                   author = EXCLUDED.author,
                   version = EXCLUDED.version,
                   category = EXCLUDED.category,
                   installable = EXCLUDED.installable,
                   auto_install = EXCLUDED.auto_install,
                   update_at = EXCLUDED.update_at,
                   action_at = EXCLUDED.action_at""",
            addon_id, addon_name, display_name, summary, description, author, version, category,
            'installed', installable, auto_install, current_time, current_time, current_time
        )
        
        logger.info(f"✓ Addon '{addon_name}' registered in ir.module.module table")
        return True
        
    except Exception as e:
        logger.error(f"Failed to register addon '{addon_name}' in ir.module.module: {e}")
        return False


async def unregister_addon_from_module_table(
    db_manager,
    addon_name: str
) -> bool:
    """
    Mark an addon as uninstalled in the ir.module.module table
    
    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        current_time = datetime.now()
        await db_manager.execute(
            """UPDATE ir_module_module SET
               state = $1, update_at = $2, action_at = $3
               WHERE name = $4""",
            'uninstalled', current_time, current_time, addon_name
        )
        
        logger.info(f"✓ Addon '{addon_name}' marked as uninstalled in ir.module.module table")
        return True
        
    except Exception as e:
        logger.error(f"Failed to unregister addon '{addon_name}' from ir.module.module: {e}")
        return False


async def update_addon_state_in_module_table(
    db_manager,
    addon_name: str,
    state: str
) -> bool:
    """
    Update addon state in the ir.module.module table
    
    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon
        state: New state ('installed', 'uninstalled', 'to_upgrade', etc.)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        current_time = datetime.now()
        await db_manager.execute(
            """UPDATE ir_module_module SET
               state = $1, update_at = $2, action_at = $3
               WHERE name = $4""",
            state, current_time, current_time, addon_name
        )
        
        logger.debug(f"✓ Addon '{addon_name}' state updated to '{state}' in ir.module.module table")
        return True
        
    except Exception as e:
        logger.error(f"Failed to update addon '{addon_name}' state to '{state}': {e}")
        return False


def read_manifest_file(manifest_path: str) -> Optional[Dict[str, Any]]:
    """
    Read and parse an addon manifest file
    
    Args:
        manifest_path: Path to the __manifest__.py file
    
    Returns:
        Dictionary containing manifest data, or None if failed
    """
    try:
        import ast
        
        with open(manifest_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Parse the manifest as Python code
        manifest_data = ast.literal_eval(content)
        
        if not isinstance(manifest_data, dict):
            logger.error(f"Manifest file {manifest_path} does not contain a dictionary")
            return None
            
        return manifest_data
        
    except Exception as e:
        logger.error(f"Failed to read manifest from {manifest_path}: {e}")
        return None


async def auto_register_addon_from_manifest(
    db_manager,
    addon_name: str,
    addon_path: str
) -> bool:
    """
    Automatically register an addon using information from its __manifest__.py file
    
    Args:
        db_manager: Database manager instance
        addon_name: Technical name of the addon
        addon_path: Path to the addon directory
    
    Returns:
        bool: True if successful, False otherwise
    """
    import os
    
    manifest_path = os.path.join(addon_path, '__manifest__.py')
    
    if not os.path.exists(manifest_path):
        logger.error(f"Manifest file not found: {manifest_path}")
        return False
    
    manifest = read_manifest_file(manifest_path)
    if not manifest:
        return False
    
    return await register_addon_in_module_table(
        db_manager,
        addon_name=addon_name,
        display_name=manifest.get('name', addon_name.replace('_', ' ').title()),
        summary=manifest.get('summary', manifest.get('name', '')),
        description=manifest.get('description', manifest.get('name', '')),
        author=manifest.get('author', 'ERP System'),
        version=manifest.get('version', '1.0.0'),
        category=manifest.get('category', 'Uncategorized'),
        installable=manifest.get('installable', True),
        auto_install=manifest.get('auto_install', False)
    )
