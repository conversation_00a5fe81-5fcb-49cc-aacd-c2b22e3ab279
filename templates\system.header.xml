<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_header" xml:space="preserve">

    <!-- App Header Component -->
    <t t-name="system.header">
        <header class="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
            <div class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center text-white text-xl">
                        <i class="fas fa-database"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">ERP System</h1>
                        <div class="text-sm text-gray-600">Enterprise Resource Planning</div>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" onclick="refreshDatabases()">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </button>
                    <button class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors" onclick="showCreateDatabaseModal()">
                        <i class="fas fa-plus mr-2"></i>
                        Create Database
                    </button>
                </div>
            </div>
        </header>
    </t>

</templates>
