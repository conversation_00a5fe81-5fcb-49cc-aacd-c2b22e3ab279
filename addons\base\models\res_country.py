"""
Country and State models - Geographic location management

This module contains models for managing countries and their states/provinces
in the ERP system, similar to Odoo's res.country and res.country.state models.
"""

from erp.models import Model
from erp import fields


class ResCountry(Model):
    """Countries"""

    _name = 'res.country'
    _description = 'Country'

    name = fields.Char(string='Country Name', required=True, translate=True, size=64, index=True,
                help='Name of the country')

    code = fields.Char(string='Country Code', required=True, size=2, unique=True, index=True,
               help='ISO 3166-1 alpha-2 country code')

    address_format = fields.Text(string='Address Format',
                         help='Format for addresses in this country')

    currency_id = fields.Many2One('res.currency', string='Currency', index=True,
                          help='Default currency for this country')

    phone_code = fields.Integer(string='Country Calling Code',
                        help='Country calling code for phone numbers')


class ResCountryState(Model):
    """Country states/provinces"""

    _name = 'res.country.state'
    _description = 'Country State'

    name = fields.Char(string='State Name', required=True, translate=True, size=64, index=True,
                help='Name of the state or province')

    code = fields.Char(string='State Code', required=True, size=3, index=True,
               help='State or province code')

    country_id = fields.Many2One('res.country', string='Country', required=True, index=True,
                         help='Country this state belongs to')
