"""
Test suite for base Field functionality

This module tests:
- Field base class initialization and parameters
- Default value handling (static and callable)
- Field validation (required/optional)
- String representation
"""
import pytest
from erp.fields import Field, FieldValidationError


class TestField:
    """Test Field base class functionality"""

    def test_field_initialization(self):
        """Test Field initialization with parameters"""
        field = Field(
            string="Test Field",
            required=True,
            readonly=False,
            default="default_value",
            help="Test help text",
            index=True
        )

        assert field.string == "Test Field"
        assert field.required is True
        assert field.readonly is False
        assert field.default == "default_value"
        assert field.help == "Test help text"
        assert field.index is True
        assert field.store is True  # Default value

    def test_field_default_value_callable(self):
        """Test Field with callable default value"""
        def get_default():
            return "dynamic_default"

        field = Field(default=get_default)

        assert field.get_default_value() == "dynamic_default"

    def test_field_default_value_static(self):
        """Test Field with static default value"""
        field = Field(default="static_default")

        assert field.get_default_value() == "static_default"

    def test_field_validation_required(self):
        """Test Field validation for required fields"""
        field = Field(string="Required Field", required=True)

        # Should raise error for None value
        with pytest.raises(FieldValidationError, match="required"):
            field.validate(None)

        # Should pass for non-None value
        result = field.validate("test_value")
        assert result == "test_value"

    def test_field_validation_optional(self):
        """Test Field validation for optional fields"""
        field = Field(string="Optional Field", required=False)

        # Should pass for None value
        result = field.validate(None)
        assert result is None

        # Should pass for non-None value
        result = field.validate("test_value")
        assert result == "test_value"

    def test_field_string_representation(self):
        """Test Field string representation"""
        field_with_string = Field(string="Test Field")
        assert str(field_with_string) == "Field(Test Field)"

        field_without_string = Field()
        assert str(field_without_string) == "Field()"
