"""
Base controller implementation
Provides minimal controller functionality without dependencies
"""

from typing import Any, Optional
from abc import ABC

from ...logging import get_logger

logger = get_logger(__name__)


class BaseController(ABC):
    """
    Minimal base controller class
    
    Provides only essential functionality without external dependencies.
    Use mixins to add specific capabilities.
    """
    
    def __init__(self):
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self._current_request = None
        self._current_env = None
    
    def _set_request_context(self, request: Any, env: Optional[Any] = None) -> None:
        """
        Set request and environment context
        
        Args:
            request: Request object
            env: Environment object (optional)
        """
        self._current_request = request
        self._current_env = env
        self.logger.debug("Request context set")
    
    def _clear_request_context(self) -> None:
        """Clear request context"""
        self._current_request = None
        self._current_env = None
        self.logger.debug("Request context cleared")
    
    def _get_request(self) -> Optional[Any]:
        """Get current request object"""
        return self._current_request
    
    def _get_env(self) -> Optional[Any]:
        """Get current environment object"""
        return self._current_env
    
    def _has_request_context(self) -> bool:
        """Check if request context is available"""
        return self._current_request is not None


class ControllerError(Exception):
    """Base exception for controller errors"""
    pass


class ControllerContextError(ControllerError):
    """Raised when controller context is invalid"""
    pass


class ControllerValidationError(ControllerError):
    """Raised when controller validation fails"""
    pass
