"""
Main cluster manager
"""
from typing import Dict, Any

from .ids import ClusterIdManager
from .networking import ClusterNetworking
from .monitoring import ClusterMonitoring
from ..config import config
from ..logging import get_logger


class ClusterManager:
    """Manages cluster configuration and setup"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.id_manager = ClusterIdManager()
        self.networking = ClusterNetworking()
        self.monitoring = ClusterMonitoring()
        self.cluster_info = {}
        
    @property
    def cluster_id(self):
        """Get cluster ID"""
        return self.id_manager.cluster_id
    
    @property
    def node_id(self):
        """Get node ID"""
        return self.id_manager.node_id
        
    def is_cluster_mode(self) -> bool:
        """Check if cluster mode is enabled"""
        return config.cluster_config.enabled
    
    def setup_cluster(self):
        """Setup cluster configuration"""
        if not self.is_cluster_mode():
            self.logger.debug("Cluster mode disabled, skipping cluster setup")
            return
            
        self.logger.info("🔗 Setting up cluster configuration...")
        
        # Get cluster nodes configuration
        cluster_nodes = self.networking._get_cluster_nodes_config()
        self.logger.info(f"🔢 Cluster nodes configuration: {cluster_nodes}")
        
        # Generate cluster and node IDs
        cluster_id, node_id = self.id_manager.generate_cluster_ids()
        self.logger.debug(f"Generated cluster ID: {cluster_id}")
        self.logger.debug(f"Generated node ID: {node_id}")
        
        # Setup cluster networking
        network_info = self.networking.setup_cluster_networking(cluster_id, node_id)
        self.cluster_info.update(network_info)
        
        # Setup cluster memory monitoring
        monitoring_info = self.monitoring.setup_cluster_monitoring()
        self.cluster_info.update(monitoring_info)
        
        # Log cluster setup completion
        self.logger.info(f"✅ Cluster setup completed - Node ID: {node_id}")
        self.logger.info(f"🌐 Cluster ID: {cluster_id}")
        
    def get_cluster_info(self) -> Dict[str, Any]:
        """Get cluster information"""
        return self.cluster_info.copy()
    
    def get_cluster_memory_details(self) -> Dict[str, Any]:
        """Get detailed cluster memory information"""
        if not self.is_cluster_mode():
            return {}
            
        return self.monitoring.get_cluster_memory_details(
            self.cluster_id, self.node_id
        )
    
    def log_cluster_shutdown_info(self):
        """Log cluster-specific information during shutdown"""
        if not self.is_cluster_mode():
            return
            
        self.monitoring.log_cluster_shutdown_info(
            self.cluster_id, self.node_id
        )
