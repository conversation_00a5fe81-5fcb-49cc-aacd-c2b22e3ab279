@echo off
REM Ultimate Single-Click Virtual Environment Setup for Windows
REM Double-click this file to setup your Python virtual environment

echo.
echo ========================================
echo   Python Virtual Environment Setup
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found!
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

REM Run the setup
echo Running virtual environment setup...
echo.
python env.py

echo.
echo ========================================
echo Setup completed! Press any key to exit.
echo ========================================
pause
