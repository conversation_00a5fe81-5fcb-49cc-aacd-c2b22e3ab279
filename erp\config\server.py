"""
Server configuration management
"""
from typing import Dict, Any, List


class ServerConfig:
    """Server-specific configuration"""
    
    def __init__(self, base_config):
        self._config = base_config
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get server configuration"""
        return {
            'host': self._config.get('options', 'http_interface', '127.0.0.1'),
            'port': self._config.getint('options', 'http_port', 8069),
        }
    
    @property
    def host(self) -> str:
        """Server host/interface"""
        return self._config.get('options', 'http_interface', '127.0.0.1')
    
    @property
    def port(self) -> int:
        """Server port"""
        return self._config.getint('options', 'http_port', 8069)
    
    @property
    def server_type(self) -> str:
        """Server type (asgi, wsgi, etc.)"""
        return self._config.get('options', 'server_type', 'asgi')
    
    @property
    def admin_passwd(self) -> str:
        """Admin password"""
        return self._config.get('options', 'admin_passwd', 'admin')
    
    @property
    def cors_origins(self) -> List[str]:
        """Get CORS origins configuration"""
        origins_str = self._config.get('options', 'cors_origins', '*')
        if origins_str == '*':
            return ['*']
        return [origin.strip() for origin in origins_str.split(',') if origin.strip()]
