services:
  postgres:
    image: postgres:16-alpine
    container_name: erp_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: erp_db
      POSTGRES_USER: erp
      POSTGRES_PASSWORD: erp
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - erp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U erp -d erp_db"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: erp_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./pgadmin-config:/pgadmin4/servers.json
    networks:
      - erp_network
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  erp_network:
    driver: bridge
