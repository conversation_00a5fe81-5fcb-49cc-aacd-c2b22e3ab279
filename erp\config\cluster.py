"""
Cluster configuration management
"""


class ClusterConfig:
    """Cluster-specific configuration"""
    
    def __init__(self, base_config):
        self._config = base_config
    
    @property
    def enabled(self) -> bool:
        """Check if cluster mode is enabled"""
        return self._config.getboolean('options', 'cluster_mode', False)
    
    @property
    def nodes(self) -> str:
        """Get cluster nodes configuration"""
        return self._config.get('options', 'cluster_nodes', 'auto')
