[options]
# =============================================================================
# ERP SYSTEM CONFIGURATION - MULTI-DATABASE MODE EXAMPLE
# =============================================================================
# This example shows how to configure the system for multi-database mode

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
http_port = 8069
http_interface = 127.0.0.1
server_type = asgi

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp

# =============================================================================
# DATABASE MODE CONFIGURATION - MULTI-DATABASE MODE
# =============================================================================
# For multi-database mode, leave db_name empty or comment it out
# db_name = 

# =============================================================================
# MULTI-DATABASE SUPPORT
# =============================================================================
list_db = True

# Database filter examples:
# db_filter = ^erp_.*          # Only databases starting with "erp_"
# db_filter = ^(erp|test)_.*   # Databases starting with "erp_" or "test_"
# db_filter =                  # Empty = load ALL databases
db_filter = 

# =============================================================================
# CONNECTION POOLING
# =============================================================================
db_pool_min_size = 10
db_pool_max_size = 20

# =============================================================================
# ADDONS CONFIGURATION
# =============================================================================
addons_path = addons,custom_addons

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
log_level = info
log_file = erp.log

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
admin_passwd = admin

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
cors_origins = *