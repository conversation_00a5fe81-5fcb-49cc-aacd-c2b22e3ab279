"""
Database configuration management
"""
from typing import Dict, Any, Optional


class DatabaseConfig:
    """Database-specific configuration"""
    
    def __init__(self, base_config):
        self._config = base_config
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            'host': self._config.get('options', 'db_host', 'localhost'),
            'port': self._config.getint('options', 'db_port', 5432),
            'user': self._config.get('options', 'db_user', 'erp'),
            'password': self._config.get('options', 'db_password', 'erp'),
            'database': self._config.get('options', 'db_name', 'erp_db'),
        }
    
    @property
    def host(self) -> str:
        """Database host"""
        return self._config.get('options', 'db_host', 'localhost')
    
    @property
    def port(self) -> int:
        """Database port"""
        return self._config.getint('options', 'db_port', 5432)
    
    @property
    def user(self) -> str:
        """Database user"""
        return self._config.get('options', 'db_user', 'erp')
    
    @property
    def password(self) -> str:
        """Database password"""
        return self._config.get('options', 'db_password', 'erp')
    
    @property
    def database(self) -> str:
        """Database name"""
        return self._config.get('options', 'db_name', 'erp_db')
    
    @property
    def list_db(self) -> bool:
        """Check if database listing is enabled"""
        return self._config.getboolean('options', 'list_db', True)
    
    @property
    def pool_config(self) -> Dict[str, Any]:
        """Get database pool configuration"""
        return {
            'min_size': self._config.getint('options', 'db_pool_min_size', 10),
            'max_size': self._config.getint('options', 'db_pool_max_size', 20),
        }
    
    @property
    def min_pool_size(self) -> int:
        """Minimum pool size"""
        return self._config.getint('options', 'db_pool_min_size', 10)
    
    @property
    def max_pool_size(self) -> int:
        """Maximum pool size"""
        return self._config.getint('options', 'db_pool_max_size', 20)
    
    @property
    def is_single_db_mode(self) -> bool:
        """Check if running in single database mode"""
        # If db_name is explicitly set and not empty, use single database mode
        db_name = self._config.get('options', 'db_name', '').strip()
        return bool(db_name)

    @property
    def is_multi_db_mode(self) -> bool:
        """Check if running in multi-database mode"""
        return not self.is_single_db_mode

    @property
    def filter(self) -> Optional[str]:
        """Get database filter pattern for multi-database mode
        
        Returns:
            str: Filter pattern if set
            None: If filter is null/empty (load all databases)
        """
        filter_value = self._config.get('options', 'db_filter', '').strip()
        return filter_value if filter_value else None

    def get_default_database(self) -> Optional[str]:
        """Get default database name based on mode"""
        if self.is_single_db_mode:
            return self._config.get('options', 'db_name')
        return None
