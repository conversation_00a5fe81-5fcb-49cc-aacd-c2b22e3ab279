"""
Model metaclass for handling field definitions and model setup
"""
from ..fields import Field
from ..logging import get_logger

logger = get_logger(__name__)


class ModelMeta(type):
    """Metaclass for models to handle field definitions"""
    
    def __new__(cls, name, bases, attrs):
        # Collect fields from the class and its parents
        fields = {}
        
        # Get fields from parent classes
        for base in bases:
            if hasattr(base, '_fields'):
                fields.update(base._fields)
        
        # Get fields from current class
        for key, value in list(attrs.items()):
            if isinstance(value, Field):
                fields[key] = value
                # Remove field from class attributes to avoid conflicts
                del attrs[key]

        # Store fields in the class
        attrs['_fields'] = fields

        # Auto-generate _table from _name if _table is None
        if attrs.get('_table') is None and attrs.get('_name'):
            attrs['_table'] = attrs['_name'].replace('.', '_')

        # Create the class
        new_class = super().__new__(cls, name, bases, attrs)

        return new_class
