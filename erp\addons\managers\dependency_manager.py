"""
Dependency management for addons

This module handles dependency resolution, circular dependency detection,
and installation order calculation.
"""
from typing import Dict, List, Set, Tuple, TYPE_CHECKING
from dataclasses import dataclass

if TYPE_CHECKING:
    from .addon_state import AddonInfo

from .addon_state import <PERSON>donState, AddonStateManager
from ..exceptions import CircularDependencyError, MissingDependencyError
from ...logging import get_logger

logger = get_logger(__name__)


@dataclass
class InstallationPlan:
    """
    Represents a complete installation plan with dependency metadata.

    This class provides detailed information about what addons need to be
    installed and in what order, distinguishing between explicitly requested
    addons and their dependencies.
    """
    installation_order: List[str]  # Complete installation order
    explicit_addons: Set[str]      # Addons explicitly requested for installation
    dependency_addons: Set[str]    # Addons that are dependencies of explicit addons
    dependency_graph: Dict[str, Set[str]]  # Full dependency graph

    def is_dependency(self, addon_name: str) -> bool:
        """Check if an addon is a dependency (not explicitly requested)"""
        return addon_name in self.dependency_addons

    def is_explicit(self, addon_name: str) -> bool:
        """Check if an addon was explicitly requested for installation"""
        return addon_name in self.explicit_addons

    def get_dependencies_for(self, addon_name: str) -> Set[str]:
        """Get direct dependencies for a specific addon"""
        return self.dependency_graph.get(addon_name, set())

    def get_installation_groups(self) -> List[List[str]]:
        """
        Group addons by dependency level for parallel installation.

        Returns a list of groups where each group can be installed in parallel
        (no dependencies between addons in the same group).
        """
        groups = []
        remaining = set(self.installation_order)

        while remaining:
            # Find addons with no remaining dependencies
            current_group = []
            for addon in list(remaining):
                deps = self.get_dependencies_for(addon)
                if not (deps & remaining):  # No dependencies in remaining set
                    current_group.append(addon)

            if not current_group:
                # This shouldn't happen with proper dependency resolution
                raise CircularDependencyError("Unable to resolve installation groups")

            groups.append(sorted(current_group))  # Sort for deterministic order
            remaining -= set(current_group)

        return groups


class DependencyManager:
    """Manages addon dependencies and installation order"""
    
    def __init__(self, state_manager: AddonStateManager):
        self.state_manager = state_manager
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._reverse_dependency_graph: Dict[str, Set[str]] = {}
    
    def build_dependency_graphs(self) -> None:
        """Build forward and reverse dependency graphs"""
        self._dependency_graph = {}
        self._reverse_dependency_graph = {}
        
        addon_states = self.state_manager.get_all_states()
        
        for name, addon_info in addon_states.items():
            self._dependency_graph[name] = set(addon_info.dependencies)
            
            # Build reverse dependencies
            for dep in addon_info.dependencies:
                if dep not in self._reverse_dependency_graph:
                    self._reverse_dependency_graph[dep] = set()
                self._reverse_dependency_graph[dep].add(name)
        
        # Update dependents in addon info
        for name, addon_info in addon_states.items():
            addon_info.dependents = list(self._reverse_dependency_graph.get(name, set()))
    
    def check_dependencies(self, addon_name: str) -> Tuple[bool, List[str]]:
        """Check if addon dependencies are satisfied"""
        addon_info = self.state_manager.get_addon_info(addon_name)
        if not addon_info:
            return False, [f"Addon {addon_name} not found"]
        
        missing_deps = []
        
        for dep in addon_info.dependencies:
            dep_info = self.state_manager.get_addon_info(dep)
            if not dep_info:
                missing_deps.append(f"Dependency {dep} not found")
            elif dep_info.state not in [AddonState.INSTALLED, AddonState.TO_UPGRADE]:
                missing_deps.append(f"Dependency {dep} not installed")
        
        return len(missing_deps) == 0, missing_deps
    
    def detect_circular_dependencies(self, addon_names: List[str] = None) -> List[List[str]]:
        """Detect circular dependencies in addon graph"""
        if addon_names is None:
            addon_names = list(self.state_manager.get_all_states().keys())
        
        def dfs(node: str, path: List[str], visited: Set[str], rec_stack: Set[str]) -> List[List[str]]:
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            cycles = []
            
            for neighbor in self._dependency_graph.get(node, set()):
                if neighbor in addon_names:  # Only check specified addons
                    if neighbor in rec_stack:
                        # Found a cycle
                        cycle_start = path.index(neighbor)
                        cycles.append(path[cycle_start:] + [neighbor])
                    elif neighbor not in visited:
                        cycles.extend(dfs(neighbor, path.copy(), visited, rec_stack))
            
            rec_stack.remove(node)
            return cycles
        
        visited = set()
        all_cycles = []
        
        for addon_name in addon_names:
            if addon_name not in visited:
                cycles = dfs(addon_name, [], visited, set())
                all_cycles.extend(cycles)
        
        return all_cycles
    
    def get_install_order(self, addon_names: List[str]) -> List[str]:
        """Get installation order for addons, respecting dependencies"""
        return self.get_enhanced_install_order(addon_names).installation_order

    def get_enhanced_install_order(self, addon_names: List[str]) -> 'InstallationPlan':
        """
        Get enhanced installation order with dependency metadata.

        Returns an InstallationPlan that includes:
        - Complete installation order
        - Dependency relationships
        - Explicit vs dependency addon classification
        """
        # Resolve all dependencies recursively
        all_addons_with_deps = self._resolve_all_dependencies(addon_names)

        # Check for circular dependencies
        cycles = self.detect_circular_dependencies(list(all_addons_with_deps))
        if cycles:
            raise CircularDependencyError(f"Circular dependencies detected: {cycles}")

        # Check for missing dependencies
        addon_states = self.state_manager.get_all_states()
        missing_deps = all_addons_with_deps - set(addon_states.keys())
        if missing_deps:
            raise MissingDependencyError(f"Missing dependencies: {missing_deps}")

        # Perform enhanced topological sort
        installation_order = self._enhanced_topological_sort(all_addons_with_deps, addon_states)

        # Create installation plan
        return InstallationPlan(
            installation_order=installation_order,
            explicit_addons=set(addon_names),
            dependency_addons=all_addons_with_deps - set(addon_names),
            dependency_graph=self._dependency_graph
        )

    def _resolve_all_dependencies(self, addon_names: List[str]) -> Set[str]:
        """Recursively resolve all dependencies for given addons"""
        resolved = set()
        addon_states = self.state_manager.get_all_states()

        def resolve_deps(addon_name: str, visited: Set[str]):
            if addon_name in visited:
                return  # Already processed
            if addon_name not in addon_states:
                return  # Skip missing addons (will be caught later)

            visited.add(addon_name)
            resolved.add(addon_name)

            # Recursively resolve dependencies
            for dep in addon_states[addon_name].dependencies:
                resolve_deps(dep, visited)

        # Resolve dependencies for all requested addons
        for addon_name in addon_names:
            resolve_deps(addon_name, set())

        return resolved

    def _enhanced_topological_sort(self, nodes: Set[str], addon_states: Dict[str, 'AddonInfo']) -> List[str]:
        """
        Enhanced topological sort that properly handles nested dependencies.

        Ensures that if A depends on B and C, and B depends on D,
        the order will be D -> B -> C -> A (dependencies first).
        """
        visited = set()
        temp_visited = set()
        result = []

        def visit(node: str):
            if node in temp_visited:
                raise CircularDependencyError(f"Circular dependency involving {node}")
            if node in visited:
                return

            temp_visited.add(node)

            # Visit all dependencies first (depth-first)
            if node in addon_states:
                for dep in sorted(addon_states[node].dependencies):  # Sort for deterministic order
                    if dep in nodes:  # Only consider nodes we're installing
                        visit(dep)

            temp_visited.remove(node)
            visited.add(node)
            result.append(node)

        # Visit all nodes in sorted order for deterministic results
        for node in sorted(nodes):
            if node not in visited:
                visit(node)

        return result

    def get_dependency_levels(self, addon_names: List[str]) -> Dict[int, List[str]]:
        """
        Get addons organized by dependency levels.

        Level 0: Addons with no dependencies
        Level 1: Addons that only depend on level 0 addons
        Level 2: Addons that depend on level 0 or 1 addons
        etc.

        This is useful for understanding the dependency hierarchy and
        for potential parallel installation within levels.
        """
        plan = self.get_enhanced_install_order(addon_names)
        addon_states = self.state_manager.get_all_states()

        levels = {}
        addon_levels = {}

        def calculate_level(addon_name: str) -> int:
            if addon_name in addon_levels:
                return addon_levels[addon_name]

            if addon_name not in addon_states:
                # Missing addon, assign level -1
                addon_levels[addon_name] = -1
                return -1

            deps = addon_states[addon_name].dependencies
            if not deps:
                # No dependencies, level 0
                level = 0
            else:
                # Level is 1 + max level of dependencies
                dep_levels = [calculate_level(dep) for dep in deps if dep in plan.installation_order]
                level = 1 + max(dep_levels) if dep_levels else 0

            addon_levels[addon_name] = level
            return level

        # Calculate levels for all addons in installation order
        for addon_name in plan.installation_order:
            level = calculate_level(addon_name)
            if level >= 0:  # Skip missing addons
                if level not in levels:
                    levels[level] = []
                levels[level].append(addon_name)

        return levels

    def validate_installation_order(self, installation_order: List[str]) -> Tuple[bool, List[str]]:
        """
        Validate that an installation order respects all dependencies.

        Returns:
            Tuple of (is_valid, list_of_violations)
        """
        addon_states = self.state_manager.get_all_states()
        installed_so_far = set()
        violations = []

        for addon_name in installation_order:
            if addon_name not in addon_states:
                violations.append(f"Addon '{addon_name}' not found in states")
                continue

            # Check if all dependencies have been installed before this addon
            deps = set(addon_states[addon_name].dependencies)
            missing_deps = deps - installed_so_far

            if missing_deps:
                violations.append(
                    f"Addon '{addon_name}' has unmet dependencies at installation time: {missing_deps}"
                )

            installed_so_far.add(addon_name)

        return len(violations) == 0, violations

    def get_dependency_tree(self, addon_name: str, max_depth: int = 10) -> Dict:
        """
        Get the complete dependency tree for an addon.

        Returns a nested dictionary representing the dependency tree.
        """
        addon_states = self.state_manager.get_all_states()

        def build_tree(current_addon: str, depth: int = 0, visited: Set[str] = None) -> Dict:
            if visited is None:
                visited = set()

            if depth > max_depth:
                return {"error": "Max depth exceeded"}

            if current_addon in visited:
                return {"error": "Circular dependency detected"}

            if current_addon not in addon_states:
                return {"error": "Addon not found"}

            visited.add(current_addon)

            deps = addon_states[current_addon].dependencies
            tree = {
                "addon": current_addon,
                "dependencies": {}
            }

            for dep in sorted(deps):
                tree["dependencies"][dep] = build_tree(dep, depth + 1, visited.copy())

            return tree

        return build_tree(addon_name)

    def find_dependency_conflicts(self, addon_names: List[str]) -> List[Dict]:
        """
        Find potential conflicts in dependency requirements.

        This checks for cases where different addons might require
        different versions of the same dependency (future enhancement).
        """
        conflicts = []
        addon_states = self.state_manager.get_all_states()

        # For now, just check for basic dependency availability
        # This can be enhanced later to check version conflicts

        all_deps = set()
        for addon_name in addon_names:
            if addon_name in addon_states:
                all_deps.update(addon_states[addon_name].dependencies)

        missing_deps = all_deps - set(addon_states.keys())
        for missing_dep in missing_deps:
            conflicts.append({
                "type": "missing_dependency",
                "dependency": missing_dep,
                "required_by": [
                    addon for addon in addon_names
                    if addon in addon_states and missing_dep in addon_states[addon].dependencies
                ]
            })

        return conflicts
    
    def get_dependency_tree(self, addon_name: str, max_depth: int = 10) -> dict:
        """Get dependency tree for an addon"""
        addon_info = self.state_manager.get_addon_info(addon_name)
        if not addon_info:
            return {}
        
        def build_tree(name: str, depth: int = 0) -> dict:
            if depth > max_depth:
                return {"name": name, "max_depth_reached": True}
            
            addon_info = self.state_manager.get_addon_info(name)
            if not addon_info:
                return {"name": name, "not_found": True}
            
            tree = {
                "name": name,
                "state": addon_info.state.value,
                "version": addon_info.installed_version or addon_info.available_version,
                "dependencies": []
            }
            
            for dep in addon_info.dependencies:
                tree["dependencies"].append(build_tree(dep, depth + 1))
            
            return tree
        
        return build_tree(addon_name)
    
    def get_dependents(self, addon_name: str) -> List[str]:
        """Get list of addons that depend on the given addon"""
        return list(self._reverse_dependency_graph.get(addon_name, set()))
    
    def get_dependencies(self, addon_name: str) -> List[str]:
        """Get list of dependencies for the given addon"""
        return list(self._dependency_graph.get(addon_name, set()))
