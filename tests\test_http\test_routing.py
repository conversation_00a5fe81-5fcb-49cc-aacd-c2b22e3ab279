"""
Test suite for HTTP routing functionality

This module tests:
- Route decorator functionality
- SystemRouteRegistry operations
- System route registration and retrieval
- Route configuration and parameters
"""

from erp.http import (
    route, systemRoute, SystemRouteRegistry, RouteType, AuthType
)


class TestRouteDecorator:
    """Test HTTP route decorator functionality"""

    def test_route_decorator_basic(self):
        """Test basic route decorator usage"""
        @route('/test', methods=['GET'])
        async def test_handler(request):
            return {'message': 'test'}

        # Check that function is decorated properly
        assert hasattr(test_handler, '_route_info')
        route_info = test_handler._route_info
        assert route_info['path'] == '/test'
        assert route_info['methods'] == ['GET']
        assert route_info['type'] == RouteType.HTTP

    def test_route_decorator_json_type(self):
        """Test route decorator with JSON type"""
        @route('/api/test', type=RouteType.JSON, methods=['POST'])
        async def json_handler(request):
            return {'result': 'success'}

        route_info = json_handler._route_info
        assert route_info['type'] == RouteType.JSON
        assert route_info['methods'] == ['POST']

    def test_route_decorator_auth_settings(self):
        """Test route decorator with authentication settings"""
        @route('/admin', auth=AuthType.ADMIN, methods=['GET'])
        async def admin_handler(request):
            return {'admin': True}

        route_info = admin_handler._route_info
        assert route_info['auth'] == AuthType.ADMIN

    def test_route_decorator_database_specific(self):
        """Test route decorator with database-specific routing"""
        @route('/db/info', database='test_db', methods=['GET'])
        async def db_handler(request):
            return {'database': 'test_db'}

        route_info = db_handler._route_info
        assert route_info['database'] == 'test_db'

    def test_route_decorator_default_methods(self):
        """Test route decorator default methods"""
        @route('/default')
        async def default_handler(request):
            return {'default': True}

        route_info = default_handler._route_info
        assert route_info['methods'] == ['GET']  # Default for HTTP type

        @route('/json_default', type=RouteType.JSON)
        async def json_default_handler(request):
            return {'json': True}

        json_route_info = json_default_handler._route_info
        assert json_route_info['methods'] == ['POST']  # Default for JSON type


class TestSystemRouteRegistry:
    """Test SystemRouteRegistry functionality"""

    def test_system_route_registry_initialization(self):
        """Test SystemRouteRegistry initialization"""
        registry = SystemRouteRegistry()

        assert hasattr(registry, '_routes')
        assert isinstance(registry._routes, dict)

    def test_system_route_registry_register(self):
        """Test system route registration"""
        registry = SystemRouteRegistry()

        async def test_handler(request):
            return {'test': True}

        registry.register_route('/test', test_handler, methods=['GET'], type=RouteType.HTTP)

        routes = registry.get_routes()
        assert '/test' in routes
        assert routes['/test']['handler'] == test_handler
        assert routes['/test']['methods'] == ['GET']

    def test_system_route_registry_get_routes(self):
        """Test getting all system routes from registry"""
        registry = SystemRouteRegistry()

        async def handler1(request):
            return {'1': True}

        async def handler2(request):
            return {'2': True}

        registry.register_route('/route1', handler1)
        registry.register_route('/route2', handler2)

        routes = registry.get_routes()
        assert len(routes) == 2
        assert '/route1' in routes
        assert '/route2' in routes


class TestSystemRouteDecorator:
    """Test systemRoute decorator functionality"""

    def test_system_route_decorator_basic(self):
        """Test basic systemRoute decorator usage"""
        @systemRoute('/system/test', methods=['GET'])
        async def test_handler():
            return {'message': 'system test'}

        # Check that function is decorated properly
        assert hasattr(test_handler, '_route_metadata')
        route_metadata = test_handler._route_metadata
        assert route_metadata['path'] == '/system/test'
        assert route_metadata['methods'] == ['GET']
        assert route_metadata['type'] == RouteType.HTTP
        assert hasattr(test_handler, '_is_system_route')
        assert test_handler._is_system_route is True

    def test_system_route_decorator_json_type(self):
        """Test systemRoute decorator with JSON type"""
        @systemRoute('/system/api', type=RouteType.JSON, auth=AuthType.NONE)
        async def api_handler():
            return {'api': 'response'}

        route_metadata = api_handler._route_metadata
        assert route_metadata['type'] == RouteType.JSON
        assert route_metadata['auth'] == AuthType.NONE
        assert route_metadata['methods'] == ['POST']  # Default for JSON
