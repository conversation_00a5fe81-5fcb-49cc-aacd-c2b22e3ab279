"""
One2One relational field implementation
"""
from .many2one import Many2One


class One2One(Many2One):
    """One-to-one relationship field (unique foreign key)"""

    def __init__(self, comodel_name, inverse_name=None, **kwargs):
        """
        Initialize One2One field

        Args:
            comodel_name: Name of the related model
            inverse_name: Name of the inverse One2One field in the related model
        """
        super().__init__(comodel_name, **kwargs)
        self.inverse_name = inverse_name
        # One2One fields should have unique constraint
        self.unique = True

    def get_sql_constraints(self, field_name, table_name=None):
        """Get SQL constraints for One2One field"""
        constraints = []

        # Add unique constraint
        constraints.append(f'UNIQUE ({field_name})')

        # Add foreign key constraint
        referenced_table = self.comodel_name.replace('.', '_')
        constraints.append(
            f'FOREIGN KEY ({field_name}) REFERENCES {referenced_table} (id) '
            f'ON DELETE {self._get_sql_ondelete()}'
        )

        return constraints

    def _get_sql_ondelete(self):
        """Convert ondelete action to SQL"""
        ondelete_map = {
            'cascade': 'CASCADE',
            'set null': 'SET NULL',
            'restrict': 'RESTRICT',
            'no action': 'NO ACTION'
        }
        return ondelete_map.get(self.ondelete.lower(), 'SET NULL')
