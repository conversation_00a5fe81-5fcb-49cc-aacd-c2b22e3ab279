"""
Logging Core Module
Core components for the refactored logging system with focused responsibilities
"""
import logging
from typing import Dict, Any, Optional
from pathlib import Path

from ..config import LoggingConfig, ConfigLoader, ConfigValidator
from .factories import ComponentFactoryRegistry, get_factory_registry


class ComponentRegistry:
    """Registry for logging components with lifecycle management"""
    
    def __init__(self):
        self.formatters: Dict[str, logging.Formatter] = {}
        self.filters: Dict[str, logging.Filter] = {}
        self.handlers: Dict[str, logging.Handler] = {}
        self.loggers: Dict[str, logging.Logger] = {}
        self._initialized = False
    
    def is_initialized(self) -> bool:
        """Check if registry is initialized"""
        return self._initialized
    
    def clear(self):
        """Clear all registered components"""
        # Close handlers properly
        for handler in self.handlers.values():
            try:
                handler.close()
            except Exception:
                pass
        
        self.formatters.clear()
        self.filters.clear()
        self.handlers.clear()
        self.loggers.clear()
        self._initialized = False
    
    def register_formatter(self, name: str, formatter: logging.Formatter):
        """Register a formatter"""
        self.formatters[name] = formatter
    
    def register_filter(self, name: str, filter_obj: logging.Filter):
        """Register a filter"""
        self.filters[name] = filter_obj
    
    def register_handler(self, name: str, handler: logging.Handler):
        """Register a handler"""
        self.handlers[name] = handler
    
    def register_logger(self, name: str, logger: logging.Logger):
        """Register a logger"""
        self.loggers[name] = logger
    
    def get_formatter(self, name: str) -> Optional[logging.Formatter]:
        """Get a formatter by name"""
        return self.formatters.get(name)
    
    def get_filter(self, name: str) -> Optional[logging.Filter]:
        """Get a filter by name"""
        return self.filters.get(name)
    
    def get_handler(self, name: str) -> Optional[logging.Handler]:
        """Get a handler by name"""
        return self.handlers.get(name)
    
    def get_logger(self, name: str) -> Optional[logging.Logger]:
        """Get a logger by name"""
        return self.loggers.get(name)
    
    def mark_initialized(self):
        """Mark registry as initialized"""
        self._initialized = True


class LoggerManager:
    """Manages logger lifecycle with focused responsibility"""
    
    def __init__(self, registry: ComponentRegistry, factory_registry: ComponentFactoryRegistry):
        self.registry = registry
        self.factory_registry = factory_registry
        self.config: Optional[LoggingConfig] = None
    
    def initialize(self, config: LoggingConfig):
        """Initialize logging system with configuration"""
        if self.registry.is_initialized():
            return
        
        # Validate configuration
        errors = ConfigValidator.validate(config)
        if errors:
            raise ValueError(f"Configuration validation failed: {', '.join(errors)}")
        
        self.config = config
        
        # Create components in order
        self._create_formatters()
        self._create_filters()
        self._create_handlers()
        self._create_loggers()
        self._setup_root_logger()
        self._configure_third_party_loggers()
        
        self.registry.mark_initialized()
    
    def _create_formatters(self):
        """Create and register formatters"""
        formatter_factory = self.factory_registry.get_formatter_factory()
        
        for name, formatter_config in self.config.formatters.items():
            formatter = formatter_factory.create(name, formatter_config)
            self.registry.register_formatter(name, formatter)
    
    def _create_filters(self):
        """Create and register filters"""
        filter_factory = self.factory_registry.get_filter_factory()
        
        for name, filter_config in self.config.filters.items():
            filter_obj = filter_factory.create(name, filter_config)
            self.registry.register_filter(name, filter_obj)
    
    def _create_handlers(self):
        """Create and register handlers"""
        handler_factory = self.factory_registry.get_handler_factory()
        
        for name, handler_config in self.config.handlers.items():
            # Ensure log directory exists for file handlers
            if handler_config.filename:
                log_dir = Path(handler_config.filename).parent
                log_dir.mkdir(parents=True, exist_ok=True)
            
            handler = handler_factory.create(
                name, handler_config,
                self.registry.formatters,
                self.registry.filters
            )
            self.registry.register_handler(name, handler)
    
    def _create_loggers(self):
        """Create and register loggers"""
        logger_factory = self.factory_registry.get_logger_factory()
        
        for name, logger_config in self.config.loggers.items():
            logger = logger_factory.create(
                name, logger_config,
                self.registry.handlers,
                self.registry.filters
            )
            self.registry.register_logger(name, logger)
    
    def _setup_root_logger(self):
        """Setup root logger"""
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.level.upper()))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Add configured handlers
        for handler_name in self.config.root_handlers:
            handler = self.registry.get_handler(handler_name)
            if handler:
                root_logger.addHandler(handler)
    
    def _configure_third_party_loggers(self):
        """Configure third-party loggers"""
        for logger_name, level in self.config.third_party_loggers.items():
            try:
                logger = logging.getLogger(logger_name)
                logger.setLevel(getattr(logging, level.upper()))
                
                # Clear existing handlers to avoid duplicates
                logger.handlers.clear()
                
                # Add console handler if available
                console_handler = self.registry.get_handler('console')
                if console_handler:
                    logger.addHandler(console_handler)
                
                # Prevent propagation to avoid duplicate messages
                logger.propagate = False
                
            except Exception:
                # Don't fail if we can't configure a third-party logger
                pass
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger"""
        # Check if logger is already registered
        logger = self.registry.get_logger(name)
        if logger:
            return logger
        
        # Create new logger
        logger = logging.getLogger(name)
        self.registry.register_logger(name, logger)
        return logger
    
    def shutdown(self):
        """Shutdown logging system"""
        self.registry.clear()
        logging.shutdown()


class LoggingSystem:
    """Main logging system facade with simplified interface"""
    
    def __init__(self):
        self.registry = ComponentRegistry()
        self.factory_registry = get_factory_registry()
        self.manager = LoggerManager(self.registry, self.factory_registry)
        self._initialized = False
    
    def initialize(self, config: Optional[LoggingConfig] = None):
        """Initialize logging system"""
        if self._initialized:
            return
        
        if config is None:
            config = ConfigLoader.get_default_config()
        
        self.manager.initialize(config)
        self._initialized = True
    
    def initialize_from_file(self, config_path: str):
        """Initialize from configuration file"""
        config = ConfigLoader.load_from_file(config_path)
        self.initialize(config)
    
    def initialize_from_dict(self, config_dict: Dict[str, Any]):
        """Initialize from configuration dictionary"""
        config = ConfigLoader.load_from_dict(config_dict)
        self.initialize(config)
    
    def initialize_from_env(self):
        """Initialize from environment variables"""
        config = ConfigLoader.load_from_env()
        self.initialize(config)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger by name"""
        if not self._initialized:
            self.initialize()
        return self.manager.get_logger(name)
    
    def is_initialized(self) -> bool:
        """Check if system is initialized"""
        return self._initialized
    
    def shutdown(self):
        """Shutdown logging system"""
        if self._initialized:
            self.manager.shutdown()
            self._initialized = False
    
    def get_registry(self) -> ComponentRegistry:
        """Get component registry"""
        return self.registry
    
    def get_manager(self) -> LoggerManager:
        """Get logger manager"""
        return self.manager


# Global logging system instance
_logging_system: Optional[LoggingSystem] = None


def get_logging_system() -> LoggingSystem:
    """Get the global logging system instance"""
    global _logging_system
    if _logging_system is None:
        _logging_system = LoggingSystem()
    return _logging_system


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the given name"""
    return get_logging_system().get_logger(name)


def initialize_logging(config: Optional[LoggingConfig] = None):
    """Initialize the logging system with configuration"""
    get_logging_system().initialize(config)


def initialize_logging_from_file(config_path: str):
    """Initialize logging from configuration file"""
    get_logging_system().initialize_from_file(config_path)


def initialize_logging_from_dict(config_dict: Dict[str, Any]):
    """Initialize logging from configuration dictionary"""
    get_logging_system().initialize_from_dict(config_dict)


def initialize_logging_from_env():
    """Initialize logging from environment variables"""
    get_logging_system().initialize_from_env()


def shutdown_logging():
    """Shutdown the logging system"""
    get_logging_system().shutdown()
