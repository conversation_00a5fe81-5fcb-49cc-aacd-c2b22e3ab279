#!/usr/bin/env python3
"""
Test server startup script
"""
import sys
import asyncio
import uvicorn
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def main():
    try:
        print("Importing create_app...")
        from erp.server import create_app
        print("Creating app...")
        app = create_app()

        print("Server created successfully")
        print(f"App type: {type(app)}")
        print(f"App routes: {len(app.routes) if hasattr(app, 'routes') else 'No routes attr'}")
        print("Starting server on http://127.0.0.1:8069")

        uvicorn.run(
            app,
            host='127.0.0.1',
            port=8069,
            log_level='info',
            access_log=True
        )

    except Exception as e:
        print(f"Error starting server: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
