"""
System utilities and helper functions for ERP CLI
"""
import sys
from pathlib import Path
from typing import Optional

from ..config import config
from ..logging import initialize_logging, get_logger


def setup_python_path():
    """Add ERP root directory to Python path"""
    erp_root = Path(__file__).parent.parent.parent
    if str(erp_root) not in sys.path:
        sys.path.insert(0, str(erp_root))


def setup_logging(quiet: bool = False) -> Optional[object]:
    """Initialize logging system and return logger"""
    try:
        initialize_logging(config.logging_config)
        logger = get_logger(__name__)
        if not quiet:
            logger.debug("🚀 ERP CLI starting...")
        return logger
    except Exception:
        # Fallback to basic logging
        import logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        if not quiet:
            logger.info("🚀 ERP CLI starting with basic logging...")
        return logger


def print_system_info(db_name: Optional[str] = None, quiet: bool = False):
    """Print system information banner"""
    if quiet:
        return
        
    try:
        from ..utils import print_startup_banner
        print_startup_banner()
    except Exception:
        # Fallback banner if utils not available
        print("=" * 60)
        print("🚀 ERP System - Command Line Interface")
        print("=" * 60)
        if db_name:
            print(f"📊 Database: {db_name}")
        print()


def validate_database_name(db_name: str) -> bool:
    """Validate database name format"""
    if not db_name:
        return False
    
    # Basic validation - database names should be alphanumeric with underscores
    import re
    pattern = r'^[a-zA-Z][a-zA-Z0-9_]*$'
    return bool(re.match(pattern, db_name))


def get_config_value(section: str, key: str, fallback=None):
    """Get configuration value with fallback"""
    try:
        return config.get(section, key, fallback=fallback)
    except Exception:
        return fallback


def get_config_int(section: str, key: str, fallback: int = 0) -> int:
    """Get integer configuration value with fallback"""
    try:
        return config.getint(section, key, fallback=fallback)
    except Exception:
        return fallback


def get_config_bool(section: str, key: str, fallback: bool = False) -> bool:
    """Get boolean configuration value with fallback"""
    try:
        return config.getboolean(section, key, fallback=fallback)
    except Exception:
        return fallback


def format_error_message(error: Exception, verbose: bool = False) -> str:
    """Format error message for display"""
    if verbose:
        import traceback
        return f"Error: {error}\n\nTraceback:\n{traceback.format_exc()}"
    else:
        return f"Error: {error}"


def confirm_action(message: str, default: bool = False) -> bool:
    """Ask user for confirmation"""
    suffix = " [Y/n]" if default else " [y/N]"
    try:
        response = input(f"{message}{suffix}: ").strip().lower()
        if not response:
            return default
        return response in ['y', 'yes']
    except (KeyboardInterrupt, EOFError):
        return False


def print_success(message: str):
    """Print success message with formatting"""
    print(f"✅ {message}")


def print_error(message: str):
    """Print error message with formatting"""
    print(f"❌ {message}")


def print_warning(message: str):
    """Print warning message with formatting"""
    print(f"⚠️  {message}")


def print_info(message: str):
    """Print info message with formatting"""
    print(f"ℹ️  {message}")


class CLIFormatter:
    """Utility class for consistent CLI output formatting"""
    
    @staticmethod
    def format_list(items: list, title: str = None) -> str:
        """Format a list of items for display"""
        output = []
        if title:
            output.append(f"📋 {title}:")
        for item in items:
            output.append(f"   • {item}")
        return "\n".join(output)
    
    @staticmethod
    def format_key_value(data: dict, title: str = None) -> str:
        """Format key-value pairs for display"""
        output = []
        if title:
            output.append(f"📊 {title}:")
        for key, value in data.items():
            output.append(f"   {key}: {value}")
        return "\n".join(output)
    
    @staticmethod
    def format_progress(current: int, total: int, message: str = "") -> str:
        """Format progress indicator"""
        percentage = (current / total) * 100 if total > 0 else 0
        bar_length = 20
        filled_length = int(bar_length * current // total) if total > 0 else 0
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        return f"🔄 [{bar}] {percentage:.1f}% {message}"


def handle_keyboard_interrupt():
    """Handle keyboard interrupt gracefully"""
    print("\n🛑 Operation cancelled by user")
    sys.exit(130)


def handle_unexpected_error(error: Exception, verbose: bool = False):
    """Handle unexpected errors gracefully"""
    print(f"💥 Unexpected error: {error}")
    if verbose:
        import traceback
        traceback.print_exc()
    sys.exit(1)


def get_version_info() -> dict:
    """Get version information"""
    return {
        "version": "1.0.0",
        "python_version": sys.version,
        "platform": sys.platform
    }


def print_version():
    """Print version information"""
    info = get_version_info()
    print(f"ERP System CLI v{info['version']}")
    print(f"Python {info['python_version']}")
    print(f"Platform: {info['platform']}")
