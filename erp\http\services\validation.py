"""
Route validation service
Provides validation capabilities for routes and registries
"""

import inspect
import re
from typing import Dict, List, Any
from urllib.parse import urlparse

from ..interfaces import IRouteValidator, IRouteRegistry, RouteInfo
from ..metadata import RouteType, AuthType
from ...logging import get_logger

logger = get_logger(__name__)


class RouteValidator(IRouteValidator):
    """Service for validating routes and registries"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._validation_rules = self._initialize_validation_rules()
    
    async def validate_route(self, route_info: RouteInfo) -> List[str]:
        """
        Validate a single route
        
        Args:
            route_info: Route to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        try:
            # Basic validation
            errors.extend(self._validate_basic_properties(route_info))
            
            # Path validation
            errors.extend(self._validate_path(route_info.path))
            
            # Methods validation
            errors.extend(self._validate_methods(route_info.methods))
            
            # Handler validation
            errors.extend(self._validate_handler(route_info.handler))
            
            # Type-specific validation
            errors.extend(self._validate_route_type(route_info))
            
            # Auth validation
            errors.extend(self._validate_auth(route_info.auth))
            
            # Metadata validation
            errors.extend(self._validate_metadata(route_info.metadata))
            
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return errors
    
    async def validate_registry(self, registry: IRouteRegistry) -> Dict[str, List[str]]:
        """
        Validate entire registry
        
        Args:
            registry: Registry to validate
            
        Returns:
            Dictionary mapping route paths to validation errors
        """
        validation_results = {}
        
        try:
            routes = await registry.get_routes()
            
            # Validate individual routes
            for path, route_list in routes.items():
                for route_info in route_list:
                    errors = await self.validate_route(route_info)
                    if errors:
                        key = f"{path}:{':'.join(route_info.methods)}"
                        validation_results[key] = errors
            
            # Validate registry-level constraints
            registry_errors = self._validate_registry_constraints(routes)
            if registry_errors:
                validation_results['_registry_'] = registry_errors
            
        except Exception as e:
            validation_results['_error_'] = [f"Registry validation error: {str(e)}"]
        
        return validation_results
    
    def _initialize_validation_rules(self) -> Dict[str, Any]:
        """Initialize validation rules"""
        return {
            'max_path_length': 255,
            'allowed_methods': ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'],
            'path_pattern': re.compile(r'^/[a-zA-Z0-9/_\-<>{}]*$'),
            'reserved_paths': {'/health', '/metrics', '/admin'},
            'max_metadata_size': 1024 * 10  # 10KB
        }
    
    def _validate_basic_properties(self, route_info: RouteInfo) -> List[str]:
        """Validate basic route properties"""
        errors = []
        
        if not route_info.path:
            errors.append("Route path cannot be empty")
        
        if not route_info.handler:
            errors.append("Route handler cannot be None")
        
        if not route_info.methods:
            errors.append("Route must have at least one method")
        
        if not route_info.route_type:
            errors.append("Route type must be specified")
        
        if not route_info.auth:
            errors.append("Auth type must be specified")
        
        if not route_info.scope:
            errors.append("Route scope must be specified")
        
        return errors
    
    def _validate_path(self, path: str) -> List[str]:
        """Validate route path"""
        errors = []
        
        if not path:
            return ["Path cannot be empty"]
        
        # Check path length
        if len(path) > self._validation_rules['max_path_length']:
            errors.append(f"Path too long (max {self._validation_rules['max_path_length']} characters)")
        
        # Check path format
        if not self._validation_rules['path_pattern'].match(path):
            errors.append("Path contains invalid characters")
        
        # Check if path starts with /
        if not path.startswith('/'):
            errors.append("Path must start with /")
        
        # Check for reserved paths
        if path in self._validation_rules['reserved_paths']:
            errors.append(f"Path {path} is reserved")
        
        # Check for duplicate slashes
        if '//' in path:
            errors.append("Path cannot contain consecutive slashes")
        
        # Check for trailing slash (except root)
        if len(path) > 1 and path.endswith('/'):
            errors.append("Path cannot end with / (except root path)")
        
        return errors
    
    def _validate_methods(self, methods: List[str]) -> List[str]:
        """Validate HTTP methods"""
        errors = []
        
        if not methods:
            return ["At least one method must be specified"]
        
        allowed_methods = self._validation_rules['allowed_methods']
        
        for method in methods:
            if not isinstance(method, str):
                errors.append(f"Method must be string, got {type(method)}")
                continue
            
            method_upper = method.upper()
            if method_upper not in allowed_methods:
                errors.append(f"Invalid method: {method}")
        
        # Check for duplicates
        if len(methods) != len(set(m.upper() for m in methods)):
            errors.append("Duplicate methods not allowed")
        
        return errors
    
    def _validate_handler(self, handler: Any) -> List[str]:
        """Validate route handler"""
        errors = []
        
        if not callable(handler):
            errors.append("Handler must be callable")
            return errors
        
        # Check handler signature
        try:
            sig = inspect.signature(handler)
            
            # Handler should accept at least one parameter (request)
            if len(sig.parameters) == 0:
                errors.append("Handler should accept at least one parameter (request)")
            
            # Check for async handler
            if inspect.iscoroutinefunction(handler):
                # Async handler is fine
                pass
            else:
                # Sync handler is also fine, but log a warning
                self.logger.debug(f"Handler {handler.__name__} is synchronous")
            
        except Exception as e:
            errors.append(f"Error inspecting handler signature: {str(e)}")
        
        return errors
    
    def _validate_route_type(self, route_info: RouteInfo) -> List[str]:
        """Validate route type specific constraints"""
        errors = []
        
        if route_info.route_type == RouteType.JSON:
            # JSON routes should typically use POST
            if 'POST' not in [m.upper() for m in route_info.methods]:
                errors.append("JSON routes should typically use POST method")
            
            # JSON routes should not use GET
            if 'GET' in [m.upper() for m in route_info.methods]:
                errors.append("JSON routes should not use GET method")
        
        elif route_info.route_type == RouteType.HTTP:
            # HTTP routes are more flexible
            pass
        
        return errors
    
    def _validate_auth(self, auth: AuthType) -> List[str]:
        """Validate authentication type"""
        errors = []
        
        # Auth type validation is mostly handled by enum
        # Additional business logic can be added here
        
        return errors
    
    def _validate_metadata(self, metadata: Dict[str, Any]) -> List[str]:
        """Validate route metadata"""
        errors = []
        
        if not isinstance(metadata, dict):
            errors.append("Metadata must be a dictionary")
            return errors
        
        # Check metadata size
        try:
            import json
            metadata_size = len(json.dumps(metadata, default=str))
            if metadata_size > self._validation_rules['max_metadata_size']:
                errors.append(f"Metadata too large (max {self._validation_rules['max_metadata_size']} bytes)")
        except Exception:
            errors.append("Metadata is not JSON serializable")
        
        # Validate specific metadata fields
        if 'cors' in metadata:
            cors_errors = self._validate_cors_metadata(metadata['cors'])
            errors.extend(cors_errors)
        
        if 'cache' in metadata:
            cache_errors = self._validate_cache_metadata(metadata['cache'])
            errors.extend(cache_errors)
        
        return errors
    
    def _validate_cors_metadata(self, cors_config: Any) -> List[str]:
        """Validate CORS metadata"""
        errors = []
        
        if not isinstance(cors_config, dict):
            errors.append("CORS config must be a dictionary")
            return errors
        
        # Validate origins
        if 'origins' in cors_config:
            origins = cors_config['origins']
            if isinstance(origins, str):
                origins = [origins]
            
            if not isinstance(origins, list):
                errors.append("CORS origins must be a list or string")
            else:
                for origin in origins:
                    if not isinstance(origin, str):
                        errors.append("CORS origin must be a string")
                    elif origin != '*' and not self._is_valid_origin(origin):
                        errors.append(f"Invalid CORS origin: {origin}")
        
        return errors
    
    def _validate_cache_metadata(self, cache_config: Any) -> List[str]:
        """Validate cache metadata"""
        errors = []
        
        if not isinstance(cache_config, dict):
            errors.append("Cache config must be a dictionary")
            return errors
        
        # Validate max_age
        if 'max_age' in cache_config:
            max_age = cache_config['max_age']
            if not isinstance(max_age, int) or max_age < 0:
                errors.append("Cache max_age must be a non-negative integer")
        
        return errors
    
    def _is_valid_origin(self, origin: str) -> bool:
        """Check if origin is valid"""
        try:
            parsed = urlparse(origin)
            return parsed.scheme in ('http', 'https') and parsed.netloc
        except:
            return False
    
    def _validate_registry_constraints(self, routes: Dict[str, List[RouteInfo]]) -> List[str]:
        """Validate registry-level constraints"""
        errors = []
        
        # Check for path conflicts
        path_method_combinations = set()
        
        for path, route_list in routes.items():
            for route_info in route_list:
                for method in route_info.methods:
                    combination = (path, method.upper())
                    if combination in path_method_combinations:
                        errors.append(f"Duplicate route: {method} {path}")
                    path_method_combinations.add(combination)
        
        return errors


# Global validator instance
_route_validator = RouteValidator()


def get_route_validator() -> RouteValidator:
    """Get the global route validator"""
    return _route_validator
