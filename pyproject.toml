[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
  | node_modules
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*", "venv/*", "node_modules/*"]

[tool.flake8]
max-line-length = 88
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    "node_modules",
    "*.egg-info",
    "build",
    "dist",
]
per-file-ignores = [
    "__init__.py:F401",  # imported but unused
]
