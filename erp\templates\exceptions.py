"""
Template engine exceptions
"""


class TemplateError(Exception):
    """Base exception for template errors"""
    pass


class TemplateNotFoundError(TemplateError):
    """Raised when a template cannot be found"""
    pass


class TemplateSyntaxError(TemplateError):
    """Raised when template syntax is invalid"""
    pass


class TemplateRenderError(TemplateError):
    """Raised when template rendering fails"""
    pass


class TemplateSecurityError(TemplateError):
    """Raised when template security validation fails"""
    pass
