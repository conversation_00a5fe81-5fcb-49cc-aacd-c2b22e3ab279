"""
Route registries package
Provides separate registries for different route scopes
"""

from .system import SystemRouteRegistry, get_system_route_registry, reset_system_route_registry
from .database import DatabaseRouteRegistry, DatabaseRouteManager, get_database_route_manager

__all__ = [
    'SystemRouteRegistry',
    'get_system_route_registry', 
    'reset_system_route_registry',
    'DatabaseRouteRegistry',
    'DatabaseRouteManager',
    'get_database_route_manager'
]
