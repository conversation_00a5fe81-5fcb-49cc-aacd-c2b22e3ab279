"""
Base Module Schema Manager
Handles database schema operations for the base module installation.
Separated from installer for clear separation of concerns.
"""
from typing import Dict, TYPE_CHECKING
from erp.logging import get_logger
from erp.models.lifecycle_manager import ModelRegistry

if TYPE_CHECKING:
    from erp.database.manager import DatabaseManager


class BaseSchemaManager:
    """
    Manages database schema operations for base module installation.
    Handles table creation, constraints, and indexes specifically for base models.
    Uses ModelRegistry for implicit model discovery and generates constraints/indexes
    dynamically from model field definitions instead of hardcoded mappings.
    """

    # Note: Constraints and indexes are now generated dynamically from model field definitions
    # This eliminates the need for hardcoded BASE_CONSTRAINTS and BASE_INDEXES

    def __init__(self):
        self.logger = get_logger(__name__)
        self._model_registry = None

    def _get_model_registry(self) -> ModelRegistry:
        """Get or create ModelRegistry for base addon"""
        if self._model_registry is None:
            self._model_registry = ModelRegistry('base')
            self._model_registry.discover_models()
        return self._model_registry

    def get_discovered_models(self) -> Dict[str, type]:
        """Get all discovered base models"""
        registry = self._get_model_registry()
        return registry.all()

    async def import_base_models(self) -> bool:
        """Discover and import base models using ModelRegistry"""
        try:
            # Use ModelRegistry to discover base models
            registry = self._get_model_registry()
            models = registry.all()

            if not models:
                self.logger.error("No base models discovered")
                return False

            self.logger.debug(f"✓ Discovered {len(models)} base models: {list(models.keys())}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to discover base models: {e}")
            return False

    async def generate_base_tables(self, db_manager: 'DatabaseManager') -> bool:
        """Generate database tables for base models using discovered models"""
        try:
            from erp.utils.schema import SchemaGenerator

            self.logger.debug("Generating base model tables...")

            # Get discovered models from ModelRegistry
            models = self.get_discovered_models()
            if not models:
                self.logger.error("No base models discovered for table generation")
                return False

            # Generate tables for each discovered model
            table_results = await SchemaGenerator.generate_model_tables_from_classes(
                db_manager, models
            )

            failed_tables = [model for model, success in table_results.items() if not success]
            if failed_tables:
                self.logger.error(f"Failed to create tables for models: {', '.join(failed_tables)}")
                return False

            self.logger.debug(f"✓ Generated {len(table_results)} base model tables successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to generate base tables: {e}")
            return False

    async def setup_base_constraints_and_indexes(self, db_manager: 'DatabaseManager') -> bool:
        """Set up base-specific constraints and indexes dynamically from model definitions"""
        try:
            from erp.utils.schema import SchemaGenerator

            # Get discovered models for dynamic constraint generation
            models = self.get_discovered_models()
            if not models:
                self.logger.error("No base models discovered for constraint generation")
                return False

            # Generate constraints and indexes dynamically from model field definitions
            constraints, indexes = SchemaGenerator.generate_dynamic_constraints_and_indexes(models)

            self.logger.debug(f"Generated {len(constraints)} constraints and {len(indexes)} indexes dynamically")

            # Use the generic method with dynamically generated constraints and indexes
            success = await SchemaGenerator.setup_table_constraints(
                db_manager,
                constraints,
                indexes
            )

            if not success:
                self.logger.error("Failed to set up base constraints and indexes")
                return False

            self.logger.debug("✓ Base constraints and indexes set up successfully")
            return True

        except Exception as e:
            self.logger.error(f"✗ Error setting up base constraints: {e}")
            return False

    async def validate_base_tables(self, db_manager: 'DatabaseManager') -> bool:
        """Validate that all base tables exist and have correct structure"""
        try:
            # Get discovered models
            models = self.get_discovered_models()
            if not models:
                self.logger.error("No base models discovered for validation")
                return False

            for model_name in models.keys():
                table_name = model_name.replace('.', '_')

                # Check if table exists
                exists = await db_manager.fetchval(
                    """SELECT EXISTS (
                       SELECT FROM information_schema.tables
                       WHERE table_schema = 'public'
                       AND table_name = $1
                    )""",
                    table_name
                )

                if not exists:
                    self.logger.error(f"Core table {table_name} does not exist after installation")
                    return False

            self.logger.debug(f"✓ All {len(models)} core tables validated successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to validate base tables: {e}")
            return False

    async def create_base_schema(self, db_manager: 'DatabaseManager') -> bool:
        """
        Complete base schema creation process.
        This is the main entry point for schema operations.
        """
        try:
            # Step 1: Import base models
            if not await self.import_base_models():
                return False

            # Step 2: Generate base model tables
            if not await self.generate_base_tables(db_manager):
                return False

            # Step 3: Set up constraints and indexes
            if not await self.setup_base_constraints_and_indexes(db_manager):
                return False

            # Step 4: Validate tables
            if not await self.validate_base_tables(db_manager):
                return False

            self.logger.debug("✓ Base schema creation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create base schema: {e}")
            return False
