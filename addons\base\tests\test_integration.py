"""
Integration tests for res.user and res.group models

Tests the complete implementation including model relationships,
data loading, and end-to-end functionality.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from erp.data import DataLoader, XMLDataParser
from erp.environment import Environment


class TestModelIntegration:
    """Test integration between user and group models"""

    @pytest.fixture
    async def models(self):
        """Get all model classes"""
        from addons.base.models.res_group import ResGroup, IrModuleCategory
        from addons.base.models.res_user import ResUser
        from addons.base.models.res_country import ResCountry

        return {
            'country': ResCountry,
            'group': ResGroup,
            'module_category': IrModuleCategory,
            'user': ResUser
        }
    
    def test_user_model_structure(self, models):
        """Test that res.users has proper standalone structure"""
        ResUser = models['user']

        # Check that user model is standalone (no inheritance)
        assert not hasattr(ResUser, '_inherits') or not ResUser._inherits

        # Check that user has its own fields
        user_fields = ResUser._fields
        assert 'name' in user_fields
        assert 'email' in user_fields
        assert 'login' in user_fields
        assert 'password' in user_fields
    
    def test_user_group_relationships(self, models):
        """Test user-group many-to-many relationships"""
        ResUser = models['user']
        ResGroup = models['group']

        # Create user
        user = ResUser(
            name='Test User',
            email='<EMAIL>',
            login='testuser'
        )
        user.id = 'user_id'

        # Create groups
        user_group = ResGroup(name='User')
        user_group.id = 'user_group_id'

        admin_group = ResGroup(name='Admin')
        admin_group.id = 'admin_group_id'
        admin_group.implied_ids = [user_group]

        # Assign groups to user
        user.groups_id = [admin_group]

        # Test share computation
        user_group.share = False
        admin_group.share = False
        user._compute_share()
        assert user.share is False
    
    async def test_group_hierarchy(self, models):
        """Test group hierarchy with implied groups"""
        ResGroup = models['group']
        
        # Create group hierarchy
        base_group = ResGroup(name='Employee')
        base_group.id = 'employee_id'
        base_group.implied_ids = []
        
        manager_group = ResGroup(name='Manager')
        manager_group.id = 'manager_id'
        manager_group.implied_ids = [base_group]
        
        admin_group = ResGroup(name='Administrator')
        admin_group.id = 'admin_id'
        admin_group.implied_ids = [manager_group]
        
        # Test implied groups collection
        implied = await admin_group.get_implied_groups()
        assert 'manager_id' in implied
        assert 'employee_id' in implied
    
    def test_category_relationships(self, models):
        """Test category relationships"""
        IrModuleCategory = models['module_category']
        ResGroup = models['group']
        
        # Create category
        category = IrModuleCategory(
            name='Administration',
            description='Admin features',
            sequence=1
        )
        category.id = 'admin_category'
        
        # Create group with category
        group = ResGroup(name='System Admin')
        group.category_id = category
        
        # Test full name computation
        group._compute_full_name()
        assert group.full_name == 'Administration / System Admin'


class TestXMLDataLoading:
    """Test XML data loading functionality"""
    
    @pytest.fixture
    def sample_xml_data(self):
        """Sample XML data for testing"""
        return '''<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">
    <record id="test_category" model="ir.module.category">
        <field name="name">Test Category</field>
        <field name="sequence">10</field>
    </record>

    <record id="test_group" model="res.groups">
        <field name="name">Test Group</field>
        <field name="category_id" ref="test_category"/>
        <field name="comment">Test group for XML loading</field>
    </record>

    <record id="test_user" model="res.users">
        <field name="name">Test User</field>
        <field name="email"><EMAIL></field>
        <field name="login">testuser</field>
        <field name="password">testpass</field>
        <field name="groups_id" eval="[(6, 0, [ref('test_group')])]"/>
    </record>
</data>'''
    
    def test_xml_parser(self, sample_xml_data):
        """Test XML data parsing"""
        parser = XMLDataParser()
        records = parser.parse_content(sample_xml_data)

        assert len(records) == 3

        # Check category record
        category_record = next(r for r in records if r['xml_id'] == 'test_category')
        assert category_record['model'] == 'ir.module.category'
        assert category_record['values']['name']['value'] == 'Test Category'

        # Check group record
        group_record = next(r for r in records if r['xml_id'] == 'test_group')
        assert group_record['model'] == 'res.groups'
        assert group_record['values']['category_id']['type'] == 'ref'
        assert group_record['values']['category_id']['value'] == 'test_category'

        # Check user record
        user_record = next(r for r in records if r['xml_id'] == 'test_user')
        assert user_record['model'] == 'res.users'
        assert user_record['values']['name']['value'] == 'Test User'
    
    async def test_data_loader(self, sample_xml_data):
        """Test data loading functionality"""
        # Mock environment
        env = MagicMock(spec=Environment)
        
        # Create data loader
        loader = DataLoader(env)
        
        # Mock model resolution
        loader._get_model = AsyncMock(return_value=MagicMock())
        loader._find_record_by_xml_id = AsyncMock(return_value=None)
        loader._store_xml_id_mapping = AsyncMock()
        
        # Test data loading
        result = await loader.load_data_content(sample_xml_data, 'base')
        
        # Should have attempted to load 3 records
        assert result['loaded'] >= 0  # May fail due to mocking, but should not error
        assert isinstance(result, dict)
        assert 'loaded' in result
        assert 'errors' in result


class TestEndToEndScenarios:
    """Test complete end-to-end scenarios"""
    
    @pytest.fixture
    async def models(self):
        """Get all model classes"""
        from addons.base.models.res_group import ResGroup, IrModuleCategory
        from addons.base.models.res_user import ResUser

        return ResGroup, IrModuleCategory, ResUser
    
    async def test_user_creation_workflow(self, models):
        """Test complete user creation workflow"""
        ResGroup, IrModuleCategory, ResUser = models

        # 1. Create category
        category = IrModuleCategory(
            name='Human Resources',
            description='HR management',
            sequence=5
        )
        category.id = 'hr_category'

        # 2. Create group
        hr_group = ResGroup(
            name='HR User',
            category_id=category,
            comment='Basic HR access'
        )
        hr_group.id = 'hr_user_group'

        # 3. Create user
        user = ResUser(
            name='Jane Smith',
            email='<EMAIL>',
            phone='******-987-6543',
            login='jane.smith',
            password='securepassword123',
            active=True,
            notification_type='inbox'
        )
        user.id = 'jane_user'
        user.groups_id = [hr_group]

        # Test user properties
        assert user.login == 'jane.smith'
        assert user.active is True
        assert user.name == 'Jane Smith'

        # Test password encryption
        encrypted = user._encrypt_password('securepassword123')
        assert user._verify_password('securepassword123', encrypted)

        # Test group membership
        user._compute_share()
        assert user.share is False  # Not a share user

        # Test name representation
        result = await user.name_get()
        assert result == [('jane_user', 'Jane Smith')]
    
    async def test_authentication_workflow(self, models):
        """Test complete authentication workflow"""
        ResGroup, IrModuleCategory, ResUser = models

        # Create user for authentication
        user = ResUser(
            name='Auth User',
            email='<EMAIL>',
            login='authuser',
            active=True,
            locked_until=None,
            login_attempts=0
        )
        user.id = 'auth_user'
        
        # Mock database operations
        user.search = AsyncMock(return_value=[user])
        user.write = AsyncMock()
        
        # Set up password
        password = 'testpassword123'
        user.password_crypt = user._encrypt_password(password)
        
        # Test successful authentication
        result = await user.authenticate('authuser', password)
        assert result == user
        
        # Test session creation
        session_token = await user.create_session()
        assert session_token is not None
        
        # Test session validation
        user.session_token = session_token
        from datetime import datetime, timedelta
        user.session_expiry = datetime.now() + timedelta(hours=1)
        
        is_valid = await user.validate_session(session_token)
        assert is_valid is True
        
        # Test session invalidation
        await user.invalidate_session()
        # Session should be invalidated in write call
