"""
Test suite for res.users model
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timedelta

from erp.fields import FieldValidationError


class TestResUser:
    """Test res.users model functionality"""
    
    @pytest.fixture
    async def user_model(self):
        """Create a user model instance for testing"""
        from addons.base.models.res_user import ResUser
        return ResUser
    
    @pytest.fixture
    def sample_user_data(self):
        """Sample user data for testing"""
        return {
            'name': 'Test User',
            'email': '<EMAIL>',
            'phone': '******-123-4567',
            'lang': 'en_US',
            'tz': 'UTC',
            'login': 'testuser',
            'password': 'testpassword123',
            'active': True,
            'notification_type': 'email',
            'signature': 'Test User\<EMAIL>'
        }
    
    def test_user_model_attributes(self, user_model):
        """Test user model has correct attributes"""
        assert user_model._name == 'res.users'
        assert user_model._description == 'Users'
        assert user_model._table == 'res_users'

        # Check that required fields exist
        fields = user_model._fields
        assert 'name' in fields
        assert 'email' in fields
        assert 'login' in fields
        assert 'password' in fields
        assert 'password_crypt' in fields
        assert 'groups_id' in fields
        assert 'active' in fields
        assert 'login_date' in fields
        assert 'session_token' in fields
    
    def test_user_initialization(self, user_model, sample_user_data):
        """Test user initialization with data"""
        user = user_model(**sample_user_data)

        assert user.name == 'Test User'
        assert user.email == '<EMAIL>'
        assert user.login == 'testuser'
        assert user.active is True
        assert user.notification_type == 'email'
        assert user.signature == 'Test User\<EMAIL>'
    
    def test_password_encryption(self, user_model):
        """Test password encryption functionality"""
        user = user_model(login='testuser')
        
        # Test password encryption
        password = 'testpassword123'
        encrypted = user._encrypt_password(password)
        
        assert encrypted is not False
        assert encrypted != password  # Should be encrypted
        assert encrypted.startswith('pbkdf2_sha256$')
        
        # Test password verification
        assert user._verify_password(password, encrypted) is True
        assert user._verify_password('wrongpassword', encrypted) is False
    
    def test_api_key_generation(self, user_model):
        """Test API key generation"""
        user = user_model(login='testuser')
        
        api_key = user._generate_api_key()
        
        assert api_key is not None
        assert len(api_key) > 20  # Should be reasonably long
        assert isinstance(api_key, str)
    
    def test_share_computation(self, user_model):
        """Test share user computation"""
        user = user_model(login='testuser')
        
        # Mock groups
        share_group = MagicMock()
        share_group.share = True
        
        non_share_group = MagicMock()
        non_share_group.share = False
        
        # Test user with only share groups
        user.groups_id = [share_group]
        user._compute_share()
        assert user.share is True
        
        # Test user with non-share groups
        user.groups_id = [non_share_group]
        user._compute_share()
        assert user.share is False
        
        # Test user with mixed groups
        user.groups_id = [share_group, non_share_group]
        user._compute_share()
        assert user.share is False  # Has non-share groups
    
    async def test_authenticate_success(self, user_model):
        """Test successful user authentication"""
        user = user_model(login='testuser')
        user.id = 'test_user_id'
        user.active = True
        user.locked_until = None
        user.login_attempts = 0
        
        # Mock password verification
        user._verify_password = MagicMock(return_value=True)
        
        # Mock search and write methods
        user.search = AsyncMock(return_value=[user])
        user.write = AsyncMock()
        
        # Test authentication
        result = await user.authenticate('testuser', 'correctpassword')
        
        assert result == user
        user.write.assert_called_once()
        # Check that login_attempts was reset and login_date was updated
        write_args = user.write.call_args[0][0]
        assert write_args['login_attempts'] == 0
        assert write_args['locked_until'] is False
        assert 'login_date' in write_args
    
    async def test_authenticate_failure(self, user_model):
        """Test failed user authentication"""
        user = user_model(login='testuser')
        user.id = 'test_user_id'
        user.active = True
        user.locked_until = None
        user.login_attempts = 2
        
        # Mock password verification to fail
        user._verify_password = MagicMock(return_value=False)
        
        # Mock search and write methods
        user.search = AsyncMock(return_value=[user])
        user.write = AsyncMock()
        
        # Test authentication failure
        result = await user.authenticate('testuser', 'wrongpassword')
        
        assert result is False
        user.write.assert_called_once()
        # Check that login_attempts was incremented
        write_args = user.write.call_args[0][0]
        assert write_args['login_attempts'] == 3
    
    async def test_authenticate_account_lockout(self, user_model):
        """Test account lockout after failed attempts"""
        user = user_model(login='testuser')
        user.id = 'test_user_id'
        user.active = True
        user.locked_until = None
        user.login_attempts = 4  # One more failure will lock
        
        # Mock password verification to fail
        user._verify_password = MagicMock(return_value=False)
        
        # Mock search and write methods
        user.search = AsyncMock(return_value=[user])
        user.write = AsyncMock()
        
        # Test authentication failure that triggers lockout
        result = await user.authenticate('testuser', 'wrongpassword')
        
        assert result is False
        assert user.write.call_count == 2  # Once for increment, once for lockout
        
        # Check that account was locked
        lockout_call = user.write.call_args_list[1][0][0]
        assert 'locked_until' in lockout_call
        assert lockout_call['locked_until'] > datetime.now()
    
    async def test_session_management(self, user_model):
        """Test session creation and validation"""
        user = user_model(login='testuser')
        user.write = AsyncMock()
        
        # Test session creation
        session_token = await user.create_session()
        
        assert session_token is not None
        assert len(session_token) > 20
        user.write.assert_called_once()
        
        # Verify session data was written
        write_args = user.write.call_args[0][0]
        assert write_args['session_token'] == session_token
        assert 'session_expiry' in write_args
        
        # Test session validation
        user.session_token = session_token
        user.session_expiry = datetime.now() + timedelta(hours=1)
        
        is_valid = await user.validate_session(session_token)
        assert is_valid is True
        
        # Test invalid session
        is_valid = await user.validate_session('invalid_token')
        assert is_valid is False
        
        # Test expired session
        user.session_expiry = datetime.now() - timedelta(hours=1)
        is_valid = await user.validate_session(session_token)
        assert is_valid is False
    
    async def test_session_invalidation(self, user_model):
        """Test session invalidation"""
        user = user_model(login='testuser')
        user.write = AsyncMock()
        
        await user.invalidate_session()
        
        user.write.assert_called_once()
        write_args = user.write.call_args[0][0]
        assert write_args['session_token'] is False
        assert write_args['session_expiry'] is False
    
    async def test_has_group(self, user_model):
        """Test group membership checking"""
        user = user_model(login='testuser')
        
        # Mock group
        group = MagicMock()
        group.has_group = AsyncMock(return_value=True)
        
        user.groups_id = [group]
        
        # Test group membership
        has_group = await user.has_group('test_group')
        assert has_group is True
        
        group.has_group.assert_called_once_with('test_group')
    
    def test_required_fields(self, user_model):
        """Test that required fields are enforced"""
        # Login is required
        with pytest.raises(FieldValidationError):
            user = user_model()
            user._fields['login'].validate(None)

        # Name is required
        with pytest.raises(FieldValidationError):
            user = user_model()
            user._fields['name'].validate(None)
    
    def test_default_values(self, user_model):
        """Test default field values"""
        user = user_model(name='Test User', login='testuser')

        # Check default values
        assert user.active is True
        assert user.share is False
        assert user.notification_type == 'email'
        assert user.login_attempts == 0
        assert user.totp_enabled is False
    
    async def test_name_get(self, user_model):
        """Test name_get method"""
        user = user_model(login='testuser')
        user.id = 'user_id'
        user.name = 'Test User'
        
        result = await user.name_get()
        assert result == [('user_id', 'Test User')]
        
        # Test with no name (should use login)
        user.name = None
        result = await user.name_get()
        assert result == [('user_id', 'testuser')]


class TestUserSecurity:
    """Test user security features"""
    
    @pytest.fixture
    async def user_model(self):
        """Create a user model instance for testing"""
        from addons.base.models.res_user import ResUser
        return ResUser
    
    def test_password_strength(self, user_model):
        """Test password encryption strength"""
        user = user_model(login='testuser')
        
        passwords = ['simple', 'complex123!', 'verylongpasswordwithspecialchars@#$']
        
        for password in passwords:
            encrypted = user._encrypt_password(password)
            
            # Each encryption should be different (due to salt)
            encrypted2 = user._encrypt_password(password)
            assert encrypted != encrypted2
            
            # But both should verify correctly
            assert user._verify_password(password, encrypted) is True
            assert user._verify_password(password, encrypted2) is True
    
    def test_api_key_uniqueness(self, user_model):
        """Test that API keys are unique"""
        user = user_model(login='testuser')
        
        keys = set()
        for _ in range(100):  # Generate many keys
            key = user._generate_api_key()
            assert key not in keys  # Should be unique
            keys.add(key)
    
    async def test_locked_account_authentication(self, user_model):
        """Test that locked accounts cannot authenticate"""
        user = user_model(login='testuser')
        user.id = 'test_user_id'
        user.active = True
        user.locked_until = datetime.now() + timedelta(minutes=30)  # Locked
        
        # Mock search method
        user.search = AsyncMock(return_value=[user])
        
        # Test authentication of locked account
        result = await user.authenticate('testuser', 'anypassword')
        
        assert result is False
