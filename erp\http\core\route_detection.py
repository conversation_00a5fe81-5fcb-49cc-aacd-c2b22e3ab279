"""
Route type detection for error handling
Determines whether to return JSON RPC or HTML error responses
"""

from typing import Optional
from fastapi import Request

from ..metadata import RouteType
from ..interfaces import RouteInfo


class RouteTypeDetector:
    """Detects route type for appropriate error response formatting"""
    
    @staticmethod
    def detect_route_type(request: Request, route_info: Optional[RouteInfo] = None) -> RouteType:
        """
        Detect the route type based on request and route information

        Args:
            request: FastAPI request object
            route_info: Optional route information

        Returns:
            RouteType enum value
        """
        # First check if we have explicit route info
        if route_info and hasattr(route_info, 'route_type'):
            return route_info.route_type

        # Check URL path patterns
        path = request.url.path

        # JSON RPC routes typically use /jsonrpc prefix
        if path.startswith('/jsonrpc'):
            return RouteType.JSON

        # Check request headers for JSON RPC indicators
        content_type = request.headers.get('content-type', '').lower()
        if 'application/json' in content_type:
            # Check if request body looks like JSON RPC
            if RouteTypeDetector._is_jsonrpc_request(request):
                return RouteType.JSON

        # Check Accept header preferences
        accept_header = request.headers.get('accept', '').lower()
        if 'application/json' in accept_header and 'text/html' not in accept_header:
            return RouteType.JSON

        # Check if it's a browser request (should get HTML)
        user_agent = request.headers.get('user-agent', '').lower()
        if any(browser in user_agent for browser in ['mozilla', 'chrome', 'safari', 'firefox', 'edge']):
            return RouteType.HTTP

        # Default to HTTP for web browsers and other clients
        return RouteType.HTTP
    
    @staticmethod
    def _is_jsonrpc_request(request: Request) -> bool:
        """
        Check if request body contains JSON RPC structure
        
        Args:
            request: FastAPI request object
            
        Returns:
            True if request appears to be JSON RPC
        """
        try:
            # This is a heuristic check - we can't read the body here
            # as it might have already been consumed
            # Instead, we rely on other indicators
            
            # Check for JSON RPC specific headers
            if request.headers.get('x-jsonrpc-version') == '2.0':
                return True
            
            # Check user agent for JSON RPC clients
            user_agent = request.headers.get('user-agent', '').lower()
            if any(indicator in user_agent for indicator in ['jsonrpc', 'rpc-client']):
                return True
            
            return False
            
        except Exception:
            return False
    
    @staticmethod
    def is_browser_request(request: Request) -> bool:
        """
        Check if request comes from a web browser
        
        Args:
            request: FastAPI request object
            
        Returns:
            True if request appears to come from a browser
        """
        user_agent = request.headers.get('user-agent', '').lower()
        browser_indicators = [
            'mozilla', 'chrome', 'safari', 'firefox', 'edge', 'opera'
        ]
        return any(indicator in user_agent for indicator in browser_indicators)


def detect_route_type_from_request(request: Request, route_info: Optional[RouteInfo] = None) -> RouteType:
    """
    Convenience function to detect route type
    
    Args:
        request: FastAPI request object
        route_info: Optional route information
        
    Returns:
        RouteType enum value
    """
    return RouteTypeDetector.detect_route_type(request, route_info)
