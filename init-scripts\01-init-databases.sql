-- Initialize additional databases for multi-database support
-- This script runs when PostgreSQL container starts for the first time

-- Create additional test databases
CREATE DATABASE erp_test;
CREATE DATABASE erp_demo;

-- Grant permissions to erp user for all databases
GRANT ALL PRIVILEGES ON DATABASE erp_db TO erp;
GRANT ALL PRIVILEGES ON DATABASE erp_test TO erp;
GRANT ALL PRIVILEGES ON DATABASE erp_demo TO erp;

-- Connect to each database and create necessary extensions
\c erp_db;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c erp_test;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

\c erp_demo;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
