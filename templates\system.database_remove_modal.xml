<?xml version="1.0" encoding="utf-8"?>
<templates id="template_system_database_remove_modal" xml:space="preserve">

    <!-- Database Remove Confirmation Modal Component -->
    <t t-name="system.database_remove_modal">
        <div id="removeDatabaseModal" class="modal fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
            <div class="bg-white rounded-xl p-8 w-full max-w-md mx-4 shadow-2xl border border-gray-200">
                <!-- Modal Header -->
                <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                    <div class="flex items-center space-x-2 text-xl font-semibold text-red-600">
                        <i class="fas fa-exclamation-triangle text-red-500"></i>
                        <span>Remove Database</span>
                    </div>
                    <button type="button" class="p-2 hover:bg-gray-100 rounded-md transition-colors text-gray-400 hover:text-gray-600" onclick="hideRemoveDatabaseModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Warning Content -->
                <div class="mb-6">
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-sm flex-shrink-0 mt-0.5">
                                <i class="fas fa-exclamation"></i>
                            </div>
                            <div>
                                <h4 class="text-red-800 font-semibold mb-1">Permanent Action</h4>
                                <p class="text-red-700 text-sm">This action cannot be undone. All data in this database will be permanently lost.</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <p class="text-gray-700">You are about to remove the database:</p>
                        <div class="bg-gray-100 border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-database text-gray-500"></i>
                                <span id="removeDatabaseName" class="font-mono font-semibold text-gray-900"></span>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600">This will delete all tables, data, and configurations associated with this database.</p>
                    </div>
                </div>

                <!-- Confirmation Input -->
                <div class="mb-6">
                    <label for="confirmDatabaseName" class="block text-sm font-medium text-gray-700 mb-2">
                        Type the database name to confirm:
                    </label>
                    <input type="text"
                           id="confirmDatabaseName"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 font-mono"
                           placeholder="Enter database name"
                           autocomplete="off"/>
                    <p class="text-xs text-gray-500 mt-1">This ensures you understand which database will be removed.</p>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3">
                    <button type="button" 
                            class="flex-1 px-4 py-2.5 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 transition-all duration-200"
                            onclick="hideRemoveDatabaseModal()">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </button>
                    <button type="button"
                            id="confirmRemoveButton"
                            class="flex-1 px-4 py-2.5 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            onclick="confirmDatabaseRemoval()"
                            disabled="disabled">
                        <i class="fas fa-trash mr-2"></i>
                        Remove Database
                    </button>
                </div>

                <!-- Loading State -->
                <div id="removeLoadingState" class="hidden mt-4 text-center">
                    <div class="flex items-center justify-center space-x-2 text-gray-600">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                        <span class="text-sm">Removing database...</span>
                    </div>
                </div>
            </div>
        </div>
    </t>

</templates>
