"""
Addon utilities

This package provides shared utility functions and classes for addon operations,
eliminating code duplication and providing centralized functionality.
"""

# Module registry utilities
from .module_registry_utils import (
    register_addon_in_module_table,
    unregister_addon_from_module_table,
    update_addon_state_in_module_table,
    read_manifest_file,
    auto_register_addon_from_manifest
)

# Discovery utilities
from .discovery_utils import (
    discover_addons_in_path,
    discover_addons_in_multiple_paths,
    validate_addon_structure,
    get_addon_dependencies,
    build_dependency_graph,
    detect_circular_dependencies,
    get_installation_order,
    find_missing_dependencies
)

# Path resolver utilities
from .path_resolver import (
    AddonPathResolver,
    get_addon_path_resolver,
    find_addon_path,
    get_addon_manifest_path,
    get_addon_data_file_path,
    list_all_addons
)

# Helper functions
from .helpers import (
    TimingContext,
    retry_async_operation,
    format_duration,
    safe_get_nested_value,
    merge_dictionaries,
    validate_addon_name,
    normalize_addon_name,
    create_error_response,
    create_success_response,
    transaction_context,
    chunk_list,
    flatten_list
)

# Constants
from .constants import (
    AddonState,
    AddonCategory,
    ManifestKeys,
    DefaultValues,
    FileNames,
    DatabaseTables,
    ModuleStates,
    HookPriorities,
    ValidationLevels,
    LogMessages,
    ErrorCodes
)

__all__ = [
    # Module registry utilities
    'register_addon_in_module_table',
    'unregister_addon_from_module_table',
    'update_addon_state_in_module_table',
    'read_manifest_file',
    'auto_register_addon_from_manifest',

    # Discovery utilities
    'discover_addons_in_path',
    'discover_addons_in_multiple_paths',
    'validate_addon_structure',
    'get_addon_dependencies',
    'build_dependency_graph',
    'detect_circular_dependencies',
    'get_installation_order',
    'find_missing_dependencies',

    # Path resolver utilities
    'AddonPathResolver',
    'get_addon_path_resolver',
    'find_addon_path',
    'get_addon_manifest_path',
    'get_addon_data_file_path',
    'list_all_addons',

    # Helper functions
    'TimingContext',
    'retry_async_operation',
    'format_duration',
    'safe_get_nested_value',
    'merge_dictionaries',
    'validate_addon_name',
    'normalize_addon_name',
    'create_error_response',
    'create_success_response',
    'transaction_context',
    'chunk_list',
    'flatten_list',

    # Constants
    'AddonState',
    'AddonCategory',
    'ManifestKeys',
    'DefaultValues',
    'FileNames',
    'DatabaseTables',
    'ModuleStates',
    'HookPriorities',
    'ValidationLevels',
    'LogMessages',
    'ErrorCodes'
]
