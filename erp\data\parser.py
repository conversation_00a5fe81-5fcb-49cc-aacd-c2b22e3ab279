"""
XML Data Parser for ERP system

Enhanced XML data parser with support for complex XML structures, template processing,
conditional loading, and comprehensive field parsing with validation.
Similar to Odoo's XML data parsing functionality but with additional features.
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional
import re
from datetime import datetime
from enum import Enum

from .exceptions import XMLParsingError
from ..logging import get_logger


class ParseMode(Enum):
    """XML parsing modes"""
    STRICT = "strict"           # Strict parsing with validation
    PERMISSIVE = "permissive"   # Allow some errors and continue
    TEMPLATE = "template"       # Template processing mode


class FieldType(Enum):
    """Supported field types in XML"""
    TEXT = "text"
    REF = "ref"
    EVAL = "eval"
    VALUE = "value"
    TEMPLATE = "template"
    FUNCTION = "function"
    MANY2MANY = "many2many"
    ONE2MANY = "one2many"


class XMLDataParser:
    """Enhanced parser for XML data files with advanced features"""

    def __init__(self, mode: ParseMode = ParseMode.STRICT, context: Optional[Dict[str, Any]] = None):
        """
        Initialize XML Data Parser

        Args:
            mode: Parsing mode (strict, permissive, template)
            context: Context variables for template processing
        """
        self.logger = get_logger(__name__)
        self.mode = mode
        self.context = context or {}
        self.template_vars = {}
        self.conditional_stack = []
        self.errors = []
        self.warnings = []

        # Statistics
        self.stats = {
            'records_parsed': 0,
            'fields_parsed': 0,
            'templates_processed': 0,
            'conditions_evaluated': 0,
            'errors': 0,
            'warnings': 0
        }
        
    def parse_file(self, file_path: str, addon_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Parse an XML data file and return comprehensive parsing results

        Args:
            file_path: Path to the XML file
            addon_name: Name of the addon for context

        Returns:
            Dictionary with records, metadata, and statistics
        """
        try:
            self.logger.info(f"Parsing XML file: {file_path}")
            self._reset_stats()

            # Add file context
            self.context.update({
                'file_path': file_path,
                'addon_name': addon_name,
                'parse_time': datetime.utcnow().isoformat()
            })

            tree = ET.parse(file_path)
            root = tree.getroot()

            # Process templates if in template mode
            if self.mode == ParseMode.TEMPLATE:
                root = self._process_templates(root)

            records = self._parse_data_element(root)

            return {
                'records': records,
                'stats': self.stats,
                'errors': self.errors,
                'warnings': self.warnings,
                'context': self.context
            }

        except ET.ParseError as e:
            error_msg = f"Failed to parse XML file {file_path}: {e}"
            self._add_error(error_msg)
            if self.mode == ParseMode.STRICT:
                raise XMLParsingError(error_msg)
            return self._create_error_result(error_msg)
        except Exception as e:
            error_msg = f"Error processing XML file {file_path}: {e}"
            self._add_error(error_msg)
            if self.mode == ParseMode.STRICT:
                raise XMLParsingError(error_msg)
            return self._create_error_result(error_msg)

    def parse_content(self, xml_content: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Parse XML content string and return comprehensive parsing results

        Args:
            xml_content: XML content as string
            context: Additional context for parsing

        Returns:
            Dictionary with records, metadata, and statistics
        """
        try:
            self._reset_stats()

            # Update context
            if context:
                self.context.update(context)

            root = ET.fromstring(xml_content)

            # Process templates if in template mode
            if self.mode == ParseMode.TEMPLATE:
                root = self._process_templates(root)

            records = self._parse_data_element(root)

            return {
                'records': records,
                'stats': self.stats,
                'errors': self.errors,
                'warnings': self.warnings,
                'context': self.context
            }

        except ET.ParseError as e:
            error_msg = f"Failed to parse XML content: {e}"
            self._add_error(error_msg)
            if self.mode == ParseMode.STRICT:
                raise XMLParsingError(error_msg)
            return self._create_error_result(error_msg)
        except Exception as e:
            error_msg = f"Error processing XML content: {e}"
            self._add_error(error_msg)
            if self.mode == ParseMode.STRICT:
                raise XMLParsingError(error_msg)
            return self._create_error_result(error_msg)
    
    def _reset_stats(self):
        """Reset parsing statistics"""
        self.stats = {
            'records_parsed': 0,
            'fields_parsed': 0,
            'templates_processed': 0,
            'conditions_evaluated': 0,
            'errors': 0,
            'warnings': 0
        }
        self.errors = []
        self.warnings = []

    def _add_error(self, message: str):
        """Add an error message"""
        self.errors.append(message)
        self.stats['errors'] += 1
        self.logger.error(message)

    def _add_warning(self, message: str):
        """Add a warning message"""
        self.warnings.append(message)
        self.stats['warnings'] += 1
        self.logger.warning(message)

    def _create_error_result(self, error_msg: str) -> Dict[str, Any]:
        """Create error result structure"""
        return {
            'records': [],
            'stats': self.stats,
            'errors': self.errors,
            'warnings': self.warnings,
            'context': self.context
        }

    def _process_templates(self, root: ET.Element) -> ET.Element:
        """Process template variables and substitutions"""
        self.stats['templates_processed'] += 1

        # Process template variables
        for elem in root.iter():
            if elem.tag == 'template':
                self._process_template_element(elem)
            elif elem.text:
                elem.text = self._substitute_template_vars(elem.text)

            # Process attributes
            for attr_name, attr_value in elem.attrib.items():
                elem.attrib[attr_name] = self._substitute_template_vars(attr_value)

        return root

    def _process_template_element(self, template_elem: ET.Element):
        """Process a template element"""
        name = template_elem.get('name')
        value = template_elem.text or template_elem.get('value', '')

        if name:
            self.template_vars[name] = self._substitute_template_vars(value)

    def _substitute_template_vars(self, text: str) -> str:
        """Substitute template variables in text"""
        if not text or '${' not in text:
            return text

        # Replace template variables
        pattern = r'\$\{([^}]+)\}'

        def replace_var(match):
            var_name = match.group(1)
            if var_name in self.template_vars:
                return str(self.template_vars[var_name])
            elif var_name in self.context:
                return str(self.context[var_name])
            else:
                self._add_warning(f"Template variable not found: {var_name}")
                return match.group(0)  # Return original if not found

        return re.sub(pattern, replace_var, text)

    def _parse_data_element(self, root: ET.Element) -> List[Dict[str, Any]]:
        """Parse the root data element and extract records with enhanced features"""
        records = []

        # Handle both <data> root and direct records
        if root.tag == 'data':
            elements = root
        else:
            elements = [root]

        for element in elements:
            # Check conditional processing
            if not self._should_process_element(element):
                continue

            if element.tag == 'record':
                record = self._parse_record_element(element)
                if record:
                    records.append(record)
                    self.stats['records_parsed'] += 1
            elif element.tag == 'data':
                # Nested data elements
                nested_records = self._parse_data_element(element)
                records.extend(nested_records)
            elif element.tag == 'template':
                # Template definitions are processed separately
                self._process_template_element(element)
            elif element.tag in ['function', 'menuitem', 'act_window']:
                # Handle special element types
                record = self._parse_special_element(element)
                if record:
                    records.append(record)
                    self.stats['records_parsed'] += 1

        return records
    
    def _should_process_element(self, element: ET.Element) -> bool:
        """Check if element should be processed based on conditions"""
        # Check if element has conditional attributes
        if_attr = element.get('if')
        unless_attr = element.get('unless')

        if if_attr:
            self.stats['conditions_evaluated'] += 1
            result = self._evaluate_condition(if_attr)
            if not result:
                return False

        if unless_attr:
            self.stats['conditions_evaluated'] += 1
            result = self._evaluate_condition(unless_attr)
            if result:
                return False

        return True

    def _evaluate_condition(self, condition: str) -> bool:
        """Evaluate a conditional expression"""
        try:
            # Simple condition evaluation
            # Support for context variables and basic expressions
            condition = self._substitute_template_vars(condition)

            # Handle simple boolean conditions
            if condition.lower() in ('true', '1', 'yes'):
                return True
            elif condition.lower() in ('false', '0', 'no'):
                return False

            # Handle context variable checks
            if condition in self.context:
                value = self.context[condition]
                return bool(value)

            # Handle simple comparisons (context_var == value)
            if '==' in condition:
                left, right = condition.split('==', 1)
                left = left.strip()
                right = right.strip().strip('"\'')

                if left in self.context:
                    return str(self.context[left]) == right

            # Default to False for unknown conditions
            return False

        except Exception as e:
            self._add_warning(f"Error evaluating condition '{condition}': {e}")
            return False

    def _parse_special_element(self, element: ET.Element) -> Optional[Dict[str, Any]]:
        """Parse special elements like function, menuitem, act_window"""
        element_type = element.tag
        xml_id = element.get('id')

        # Map special elements to models
        model_mapping = {
            'function': 'ir.actions.server',
            'menuitem': 'ir.ui.menu',
            'act_window': 'ir.actions.act_window'
        }

        model = model_mapping.get(element_type)
        if not model:
            self._add_warning(f"Unknown special element type: {element_type}")
            return None

        record = {
            'model': model,
            'xml_id': xml_id,
            'values': {},
            'context': element.get('context', '{}'),
            'noupdate': element.get('noupdate', '0') == '1',
            'element_type': element_type
        }

        # Parse attributes as field values
        for attr_name, attr_value in element.attrib.items():
            if attr_name not in ['id', 'context', 'noupdate']:
                record['values'][attr_name] = {
                    'type': 'text',
                    'value': self._substitute_template_vars(attr_value)
                }

        # Parse child elements as fields
        for field_elem in element:
            field_name = field_elem.get('name') or field_elem.tag
            field_value = self._parse_field_element(field_elem)
            record['values'][field_name] = field_value

        return record

    def _parse_record_element(self, record_elem: ET.Element) -> Optional[Dict[str, Any]]:
        """Parse a single record element with enhanced validation"""
        # Get record attributes
        model = record_elem.get('model')
        xml_id = record_elem.get('id')

        if not model:
            error_msg = "Record element missing 'model' attribute, skipping"
            self._add_warning(error_msg)
            return None

        # Validate model name format
        if not self._validate_model_name(model):
            error_msg = f"Invalid model name format: {model}"
            self._add_warning(error_msg)
            if self.mode == ParseMode.STRICT:
                return None

        record = {
            'model': model,
            'xml_id': xml_id,
            'values': {},
            'context': record_elem.get('context', '{}'),
            'noupdate': record_elem.get('noupdate', '0') == '1',
            'forcecreate': record_elem.get('forcecreate', '0') == '1',
            'priority': int(record_elem.get('priority', '16'))
        }

        # Parse field elements
        for field_elem in record_elem:
            field_name = field_elem.get('name')
            if not field_name:
                self._add_warning(f"Field element missing 'name' attribute in record {xml_id}")
                continue

            field_value = self._parse_field_element(field_elem)
            if field_value is not None:
                record['values'][field_name] = field_value
                self.stats['fields_parsed'] += 1

        return record
    
    def _validate_model_name(self, model_name: str) -> bool:
        """Validate model name format"""
        # Model names should follow pattern: module.model or just model
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_.]*$'
        return bool(re.match(pattern, model_name))

    def _parse_field_element(self, field_elem: ET.Element) -> Optional[Dict[str, Any]]:
        """Parse a field element and return its value with enhanced type support"""
        field_type = field_elem.tag
        field_name = field_elem.get('name')

        try:
            if field_type == 'field':
                return self._parse_regular_field(field_elem)
            elif field_type == 'value':
                return self._parse_value_field(field_elem)
            elif field_type in ['many2many', 'one2many']:
                return self._parse_relational_field(field_elem, field_type)
            elif field_type == 'function':
                return self._parse_function_field(field_elem)
            else:
                # Unknown field type, treat as text with warning
                self._add_warning(f"Unknown field type '{field_type}' for field '{field_name}'")
                return {'type': 'text', 'value': field_elem.text or ''}

        except Exception as e:
            error_msg = f"Error parsing field '{field_name}' of type '{field_type}': {e}"
            self._add_error(error_msg)
            if self.mode == ParseMode.STRICT:
                return None
            return {'type': 'text', 'value': field_elem.text or ''}

    def _parse_regular_field(self, field_elem: ET.Element) -> Dict[str, Any]:
        """Parse a regular field element"""
        eval_attr = field_elem.get('eval')
        ref_attr = field_elem.get('ref')
        search_attr = field_elem.get('search')
        model_attr = field_elem.get('model')

        if eval_attr:
            # Python expression to evaluate
            return {
                'type': 'eval',
                'value': self._substitute_template_vars(eval_attr),
                'original': eval_attr
            }
        elif ref_attr:
            # Reference to another record
            return {
                'type': 'ref',
                'value': self._substitute_template_vars(ref_attr),
                'original': ref_attr
            }
        elif search_attr and model_attr:
            # Search for record in specified model
            return {
                'type': 'search',
                'value': self._substitute_template_vars(search_attr),
                'model': model_attr,
                'original': search_attr
            }
        else:
            # Simple text value
            text_value = field_elem.text or ''
            return {
                'type': 'text',
                'value': self._substitute_template_vars(text_value),
                'original': text_value
            }

    def _parse_value_field(self, field_elem: ET.Element) -> Dict[str, Any]:
        """Parse a value element (for One2many/Many2many)"""
        eval_attr = field_elem.get('eval')
        ref_attr = field_elem.get('ref')

        if eval_attr:
            return {
                'type': 'eval',
                'value': self._substitute_template_vars(eval_attr),
                'original': eval_attr
            }
        elif ref_attr:
            return {
                'type': 'ref',
                'value': self._substitute_template_vars(ref_attr),
                'original': ref_attr
            }
        else:
            text_value = field_elem.text or ''
            return {
                'type': 'value',
                'value': self._substitute_template_vars(text_value),
                'original': text_value
            }

    def _parse_relational_field(self, field_elem: ET.Element, field_type: str) -> Dict[str, Any]:
        """Parse relational field (many2many, one2many)"""
        values = []

        for child in field_elem:
            if child.tag in ['value', 'record']:
                child_value = self._parse_field_element(child)
                if child_value:
                    values.append(child_value)

        return {
            'type': field_type,
            'values': values,
            'model': field_elem.get('model'),
            'relation': field_elem.get('relation')
        }

    def _parse_function_field(self, field_elem: ET.Element) -> Dict[str, Any]:
        """Parse a function field"""
        return {
            'type': 'function',
            'name': field_elem.get('name'),
            'model': field_elem.get('model'),
            'eval': field_elem.get('eval'),
            'value': field_elem.text or ''
        }
    
    def validate_xml_structure(self, file_path: str) -> Dict[str, Any]:
        """
        Validate XML file structure with comprehensive checks

        Args:
            file_path: Path to XML file

        Returns:
            Dictionary with validation results
        """
        result = {
            'valid': False,
            'errors': [],
            'warnings': [],
            'structure': {},
            'statistics': {}
        }

        try:
            tree = ET.parse(file_path)
            root = tree.getroot()

            # Basic structure validation
            if root.tag not in ['data', 'odoo']:
                result['errors'].append(f"Invalid root element: {root.tag}. Expected 'data' or 'odoo'")
                return result

            # Count elements
            stats = {
                'records': 0,
                'templates': 0,
                'functions': 0,
                'menuitems': 0,
                'act_windows': 0,
                'total_elements': 0
            }

            # Validate structure and count elements
            for element in root.iter():
                stats['total_elements'] += 1

                if element.tag == 'record':
                    stats['records'] += 1
                    if not element.get('model'):
                        result['errors'].append(f"Record missing 'model' attribute at line {element.sourceline if hasattr(element, 'sourceline') else 'unknown'}")
                elif element.tag == 'template':
                    stats['templates'] += 1
                elif element.tag == 'function':
                    stats['functions'] += 1
                elif element.tag == 'menuitem':
                    stats['menuitems'] += 1
                elif element.tag == 'act_window':
                    stats['act_windows'] += 1

            result['statistics'] = stats
            result['structure'] = {
                'root_tag': root.tag,
                'has_records': stats['records'] > 0,
                'has_templates': stats['templates'] > 0,
                'has_special_elements': (stats['functions'] + stats['menuitems'] + stats['act_windows']) > 0
            }

            # Check if file has any meaningful content
            if stats['records'] == 0 and stats['functions'] == 0 and stats['menuitems'] == 0:
                result['warnings'].append("File contains no records or functional elements")

            result['valid'] = len(result['errors']) == 0

        except ET.ParseError as e:
            result['errors'].append(f"XML parsing error: {e}")
        except Exception as e:
            result['errors'].append(f"Validation error: {e}")

        return result

    def get_parsing_statistics(self) -> Dict[str, Any]:
        """Get comprehensive parsing statistics"""
        return {
            'stats': self.stats.copy(),
            'errors': self.errors.copy(),
            'warnings': self.warnings.copy(),
            'context': self.context.copy(),
            'template_vars': self.template_vars.copy()
        }

    def reset_parser_state(self):
        """Reset parser state for reuse"""
        self._reset_stats()
        self.template_vars.clear()
        self.conditional_stack.clear()
        self.context.clear()

    def set_context(self, context: Dict[str, Any]):
        """Set parsing context"""
        self.context.update(context)

    def add_template_var(self, name: str, value: Any):
        """Add a template variable"""
        self.template_vars[name] = value

    def validate_record_data(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a parsed record structure"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }

        # Check required fields
        if 'model' not in record:
            validation_result['errors'].append("Record missing 'model' field")
            validation_result['valid'] = False

        if 'values' not in record:
            validation_result['errors'].append("Record missing 'values' field")
            validation_result['valid'] = False

        # Validate model name
        if 'model' in record and not self._validate_model_name(record['model']):
            validation_result['errors'].append(f"Invalid model name: {record['model']}")
            validation_result['valid'] = False

        # Check for empty values
        if 'values' in record and not record['values']:
            validation_result['warnings'].append("Record has no field values")

        return validation_result
