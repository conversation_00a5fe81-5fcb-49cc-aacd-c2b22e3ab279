"""
Database manager that coordinates all database operations
"""
import asyncpg
from typing import Dict, Any, Optional, List
from .pool import ConnectionPool
from .sql_logger import S<PERSON>Logger
from .transactions import TransactionManager
from ..operations.crud import CRUDOperations
from ..operations.schema import SchemaOperations


class DatabaseManager:
    """Database manager with connection pooling and modular operations"""

    def __init__(self, db_name: Optional[str] = None):
        self.db_name = db_name
        
        # Initialize components
        self.pool = ConnectionPool(db_name)
        self.sql_logger = SQLLogger(self.db_name)
        self.transactions = TransactionManager(self.pool)
        self.crud = CRUDOperations(self.pool, self.sql_logger)
        self.schema = SchemaOperations(self.pool, self.sql_logger)

    async def create_pool(self):
        """Create connection pool"""
        await self.pool.create_pool()

    async def close_pool(self):
        """Close connection pool"""
        await self.pool.close_pool()

    # Delegate CRUD operations
    async def execute(self, query: str, *args) -> str:
        """Execute a query and return status"""
        return await self.crud.execute(query, *args)

    async def fetch(self, query: str, *args) -> List[asyncpg.Record]:
        """Fetch multiple rows"""
        return await self.crud.fetch(query, *args)

    async def fetchrow(self, query: str, *args) -> Optional[asyncpg.Record]:
        """Fetch single row"""
        return await self.crud.fetchrow(query, *args)

    async def fetchval(self, query: str, *args) -> Any:
        """Fetch single value"""
        return await self.crud.fetchval(query, *args)

    async def insert(self, table: str, data: Dict[str, Any]) -> Optional[str]:
        """Insert a record and return the ID"""
        return await self.crud.insert(table, data)

    async def update(self, table: str, record_id: str, data: Dict[str, Any]) -> bool:
        """Update a record"""
        return await self.crud.update(table, record_id, data)

    async def delete(self, table: str, record_id: str) -> bool:
        """Delete a record"""
        return await self.crud.delete(table, record_id)

    async def exists(self, table: str, record_id: str) -> bool:
        """Check if a record exists"""
        return await self.crud.exists(table, record_id)

    async def count(self, table: str, where_clause: str = "", params: List = None) -> int:
        """Count records in table"""
        return await self.crud.count(table, where_clause, params)

    # Delegate schema operations
    async def create_table(self, table_name: str, columns: Dict[str, str]):
        """Create a table with specified columns"""
        await self.schema.create_table(table_name, columns)

    async def drop_table(self, table_name: str):
        """Drop a table"""
        await self.schema.drop_table(table_name)

    async def table_exists(self, table_name: str) -> bool:
        """Check if a table exists"""
        return await self.schema.table_exists(table_name)

    async def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a table"""
        return await self.schema.get_table_columns(table_name)

    # Delegate transaction operations
    async def begin_transaction(self):
        """Begin a transaction"""
        return await self.transactions.begin_transaction()

    async def commit_transaction(self):
        """Commit current transaction"""
        return await self.transactions.commit_transaction()

    async def rollback_transaction(self):
        """Rollback current transaction"""
        return await self.transactions.rollback_transaction()

    # Direct access to components for advanced usage
    @property
    def acquire_connection(self):
        """Get connection context manager"""
        return self.pool.acquire_connection
