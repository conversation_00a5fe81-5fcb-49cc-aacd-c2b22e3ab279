"""
Utility functions for environment management
"""
from typing import Dict, Any, Optional

from .core import Environment
from .manager import EnvironmentManager


# Convenience function to get current environment
def env() -> Optional[Environment]:
    """Get current environment from context"""
    return EnvironmentManager.get_current_environment()


# Convenience function to create environment
async def create_env(db_name: str, uid: int, context: Dict[str, Any] = None) -> Environment:
    """Create a new environment"""
    return await EnvironmentManager.create_environment(db_name, uid, context)
